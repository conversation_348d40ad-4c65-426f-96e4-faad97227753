import { CreditApplicationType } from '@linqpal/models/src/dictionaries/creditApplicationType'
import { ApiRequester } from '../helpers/ApiRequester'
import { ICredit, ILoan, LoanProduct } from './lms.service'

const getRequester = () => {
  const baseURL = process.env.NET_LOAN_SERVICE_API_URL
  const userId = 'AutomaticNodeJsServices'

  if (!baseURL) {
    throw new Error(
      'NET_LOAN_SERVICE_API_URL environment variable is not provided',
    )
  }

  return new ApiRequester(
    baseURL,
    'LoanService',
    process.env.LS_API_KEY,
    undefined,
    userId,
  )
}

async function getLoansList(filters: any) {
  const requester = getRequester()

  const response = await requester.get<{
    result: ILoan[]
    total: number
  }>('Loans/Query', {
    detailed: true,
    Product: LoanProduct.InHouseCredit,
    ...filters,
  })

  return response
}

async function getLoanById(loanId: string): Promise<ILoan> {
  const requester = getRequester()

  const response = await requester.get<ILoan>(`Loans/${loanId}`, {
    detailed: true,
  })

  return response
}

async function getCreditById(creditId: string) {
  const requester = getRequester()

  const response = await requester.get<ICredit>(`Credits/${creditId}`, {
    detailed: true,
  })

  return response
}

async function cancelCreditHold(id: string) {
  const requester = getRequester()

  const response = await requester.patch<unknown>(`creditHolds/${id}/cancel`)

  return response
}

async function getActiveCredit(creditApplicationId: string) {
  const requester = getRequester()

  const response = await requester.get<
    {
      id: string
    }[]
  >(`Credits`, {
    CreditApplicationId: creditApplicationId,
    Status: 'Active',
    Product: CreditApplicationType.LineOfCredit,
  })

  // In case of multiple approved credits take latest
  return response.at(-1) ?? null
}

export const loanService = {
  cancelCreditHold,
  getLoansList,
  getLoanById,
  getCreditById,
  getActiveCredit,
}

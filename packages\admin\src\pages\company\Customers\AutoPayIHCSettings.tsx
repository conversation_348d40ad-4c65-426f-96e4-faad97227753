import React from 'react'
import { CFormGroup, CInputRadio, CLabel, CCol, CRow } from '@coreui/react'
import { observer } from 'mobx-react-lite'
import { ICustomerCompanySettings } from '@linqpal/models/src/types/routes'

interface AutoPayIHCSettingsProps {
  settings: Partial<ICustomerCompanySettings>
  onUpdate: (settings: Partial<ICustomerCompanySettings>) => void
}

export const AutoPayIHCSettings = observer(
  ({ settings, onUpdate }: AutoPayIHCSettingsProps) => {
    const inHouseCredit = {
      isAutoPayRequired: settings?.inHouseCredit?.isAutoPayRequired ?? false,
      isAutoPayEnabledByCompanyUser:
        settings?.inHouseCredit?.isAutoPayEnabledByCompanyUser ?? false,
    }

    const handleAutoPayChange = (isRequired: boolean) => {
      onUpdate({
        inHouseCredit: {
          ...inHouseCredit,
          isAutoPayRequired: isRequired,
        },
      })
    }
    return (
      <>
        <CFormGroup className="mt-4">
          <CRow className="align-items-center mb-1">
            <CCol className="d-flex align-items-center justify-content-between">
              <h5 className="mb-0 mr-3">Company Account Level- Auto Pay</h5>
              <div className="d-flex">
                <CFormGroup variant="checkbox" inline className="mr-4 mb-0">
                  <CInputRadio
                    id="rb-auto-pay-required"
                    checked={inHouseCredit.isAutoPayRequired}
                    onChange={() => handleAutoPayChange(true)}
                  />
                  <CLabel htmlFor="rb-auto-pay-required" variant="checkbox">
                    Required
                  </CLabel>
                </CFormGroup>
                <CFormGroup variant="checkbox" inline className="mb-0">
                  <CInputRadio
                    id="rb-auto-pay-not-required"
                    checked={!inHouseCredit.isAutoPayRequired}
                    onChange={() => handleAutoPayChange(false)}
                  />
                  <CLabel htmlFor="rb-auto-pay-not-required" variant="checkbox">
                    Not Required
                  </CLabel>
                </CFormGroup>
              </div>
            </CCol>
          </CRow>
          <CLabel>any changes here will reflect other supplier settings</CLabel>
        </CFormGroup>
      </>
    )
  },
)

import { authRequired } from '../../../services/auth.service'
import { getInvoice } from '../../../controllers/invoice.controller'
import { ControllerItem } from 'src/routes/controllerItem'
import { exceptions, InvoicePaymentType } from '@linqpal/models'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'
import * as LMS from '@linqpal/common-backend/src/services/lms.service'
import { Logger } from '@linqpal/common-backend'

const logger = new Logger({ module: 'invoices', subModule: 'paymentSchedule' })

export default {
  middlewares: { pre: [authRequired()] },
  get: async (req, res) => {
    logger.info(
      `received request for payment schedule: ${JSON.stringify(
        req.query,
      )}, company ${req.company!._id}, user ${req.user!._id}`,
    )

    const { id } = req.query

    if (!id) {
      throw new exceptions.LogicalError('Invoice ID is required')
    }

    const invoice = await getInvoice({
      _id: id as string,
      companyId: req.company!._id.toString(),
    })

    if (!invoice) {
      throw new exceptions.LogicalError('Invoice not found')
    }

    const isFactoringApproved =
      invoice.paymentDetails?.paymentType === InvoicePaymentType.FACTORING &&
      invoice.paymentDetails?.arAdvanceStatus === ArAdvanceStatus.Approved

    if (!isFactoringApproved) {
      throw new exceptions.LogicalError(
        'Payment schedule is only available for approved factoring invoices',
      )
    }

    try {
      logger.info(`Getting loans for payable ID: ${invoice._id}`)
      const loans = await LMS.findLoans({
        payableId: invoice._id.toString(),
        detailed: true,
      })

      if (!loans || loans.length === 0) {
        logger.warn(`No loans found for payable ID: ${invoice._id}`)
        return res.send({
          result: 'ok',
          loanReceivables: [],
        })
      }

      const firstLoan = loans[0]
      logger.info(`Getting loan info for loan ID: ${firstLoan.id}`)

      const loanInfo = await LMS.getLoanInfo(firstLoan.id, true)

      if (!loanInfo) {
        logger.warn(`No loan info found for loan ID: ${firstLoan.id}`)
        return res.send({
          result: 'ok',
          loanReceivables: [],
        })
      }

      return res.send({
        result: 'ok',
        loanReceivables: loanInfo.loanReceivables || [],
      })
    } catch (err) {
      logger.error(`Error getting payment schedule for invoice ${id}: ${err}`)
      throw new exceptions.LogicalError('Failed to get payment schedule')
    }
  },
} as ControllerItem

import React, { ReactNode, useEffect, useMemo } from 'react'
import { Platform, ScrollView, View } from 'react-native'
import { Text } from 'react-native-paper'
import Modal from '../../ui/atoms/Modal'
import { useResponsive } from '../../utils/hooks'
import { SIDE_NAVIGATION_DRAWER_WIDTH } from '../../utils/helpers/commonUtils'
import { helpers } from '@linqpal/common-frontend'
import { BackIcon, ModalCloseIcon } from '../../assets/icons'
import { GetIcon } from '../atoms/GetIcon'
import { BtPlainText } from '@linqpal/components/src/ui'

const { composeStyle } = helpers

interface PropTypes {
  title?: string | React.ReactElement
  subtitle?: string | ReactNode
  onClose?: () => void
  onBack?: () => void
  visible: boolean
  children?: React.ReactNode
  height?: string | number
  backgroundColor?: string
  fixedContent?: React.ReactNode
  paddingHorizontal?: number
  footer?: React.ReactNode | null
  testID?: string
  width?: number
  backdropOpacity?: number
  minWidth?: number
  isScrollable?: boolean
}

export default ({
  title = '',
  onClose,
  onBack,
  visible = false,
  children,
  height,
  backgroundColor = 'white',
  subtitle = '',
  fixedContent = undefined,
  paddingHorizontal = 20,
  footer = null,
  backdropOpacity = 0.7,
  testID,
  width: modalWidth,
  minWidth = 600,
}: PropTypes) => {
  const { screenWidth, screenHeight, md, sm } = useResponsive()

  useEffect(() => {
    const onEscapePress = (event) => {
      if (event.keyCode === 27) {
        onClose && onClose()
      }
    }

    if (Platform.OS === 'web') {
      document.addEventListener('keydown', onEscapePress, false)
    }

    return () => {
      if (Platform.OS === 'web') {
        document.removeEventListener('keydown', onEscapePress, false)
      }
    }
  }, [onClose])

  const width = md
    ? (screenWidth - SIDE_NAVIGATION_DRAWER_WIDTH) / 2
    : screenWidth > 300
    ? modalWidth ?? screenWidth
    : 300
  const margins = md ? (screenWidth - width) / 2 : 16
  const marginLeft = md ? SIDE_NAVIGATION_DRAWER_WIDTH + margins : margins

  const maxHeight = screenHeight - 20

  const header = useMemo(() => {
    if (!title) return <View style={{ flex: 1 }} />
    return typeof title === 'object' ? (
      title
    ) : (
      <View
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          height: 30,
          flex: 1,
        }}
      >
        <BtPlainText
          style={{
            fontWeight: '700',
            fontSize: 20,
            lineHeight: 26,
            textAlign: 'center',
          }}
          testID={title || ''}
        >
          {title}
        </BtPlainText>
        {!!subtitle && (
          <Text
            style={{
              fontWeight: '500',
              fontSize: 12,
              color: '#668598',
              lineHeight: 20,
            }}
          >
            {subtitle}
          </Text>
        )}
      </View>
    )
  }, [title, subtitle])

  return (
    <Modal
      testID={testID}
      visible={visible}
      onClose={onClose}
      backdropOpacity={backdropOpacity}
      style={composeStyle(
        {
          justifyContent: 'flex-end',
          marginHorizontal: sm ? margins : -0,
          marginLeft: sm ? marginLeft : 0,
          marginBottom: 0,
          width: sm && (modalWidth || width - 2 * margins),
          overflowY: 'visible', // Change from 'auto' to 'visible'
        },
        md
          ? {
              justifyContent: 'center',
              alignContent: 'center',
              marginTop: 0,
              marginRight: 'auto',
              marginLeft: 'auto',
              minWidth: minWidth ?? 600,
            }
          : {},
      )}
    >
      <View
        style={composeStyle(
          {
            backgroundColor,
            height,
            paddingHorizontal: paddingHorizontal,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            maxHeight,
            overflowY: 'auto',
          },
          md
            ? {
                borderRadius: 20,
              }
            : {},
        )}
      >
        {!md && (
          <View
            style={{
              backgroundColor: '#E6EBEE',
              height: 4,
              borderRadius: 3,
              width: 32,
              alignSelf: 'center',
              marginTop: 8,
              marginBottom: 8,
            }}
          />
        )}
        <View
          style={composeStyle(
            {
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 10,
            },
            md
              ? {
                  marginTop: 24,
                }
              : {},
          )}
        >
          {onBack ? (
            <GetIcon
              icon={<BackIcon width={40} height={40} />}
              onPress={onBack}
              style={{ marginLeft: 20 }}
            />
          ) : (
            <View style={{ width: 30 }} />
          )}
          {header}
          {!!onClose && (
            <View style={{ marginRight: 10 }}>
              <GetIcon
                style={{ marginRight: '10' }}
                testID={`${title}_close_btn`}
                icon={<ModalCloseIcon width={40} height={40} />}
                onPress={onClose}
              />
            </View>
          )}
        </View>
        {fixedContent}
        <ScrollView
          contentContainerStyle={{ height: '100%' }}
          showsVerticalScrollIndicator={false}
        >
          {children}
        </ScrollView>
        {footer}
      </View>
    </Modal>
  )
}

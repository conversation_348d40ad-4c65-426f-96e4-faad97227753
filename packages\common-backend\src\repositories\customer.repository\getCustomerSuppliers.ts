import { CustomerAccountType } from '@linqpal/models/src/dictionaries/customerAccountType'
import { Company, CustomerAccount, User, UserRole } from '../../models'
import { CustomerSuppliers } from '../../models/types'

export async function getCustomersSuppliers(
  companyId: string | null | undefined,
): Promise<CustomerSuppliers[]> {
  if (!companyId) throw new Error('companyId is required')

  const userRoles = await UserRole.aggregate([
    {
      $match: {
        company_id: companyId,
      },
    },
    {
      $lookup: {
        from: User.collection.name,
        localField: 'sub',
        foreignField: 'sub',
        as: 'user',
      },
    },
    { $unwind: '$user' },
  ])

  const matchConditions = userRoles
    .map((userRole) => userRole.user.email || userRole.user.login)
    .filter(Boolean)
    .map(String)

  console.log(matchConditions)

  const pipeline = [
    {
      $match: {
        $and: [
          {
            $or: [
              { email: { $in: matchConditions } },
              { phone: { $in: matchConditions } },
            ],
          },
          { isDeleted: { $ne: true } },
        ],
      },
    },
    {
      $lookup: {
        from: Company.collection.name,
        as: 'suppliers',
        let: {
          supplierId: { $toObjectId: '$company_id' },
          customerType: '$type',
          isInHouseCreditEnabled: '$settings.inHouseCredit.isEnabled',
        },
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$supplierId'] } } },
          {
            $project: {
              id: { $toString: '$_id' },
              name: 1,
              legalName: 1,
              settings: 1,
              customerType: {
                $ifNull: ['$$customerType', CustomerAccountType.TradeCredit],
              },
              isFactoringEnabled: {
                $and: [
                  { $ifNull: ['$$isInHouseCreditEnabled', false] },
                  { $ifNull: ['$settings.arAdvance.isEnabled', false] },
                ],
              },
            },
          },
        ],
      },
    },
    { $unwind: '$suppliers' },
    {
      $group: {
        _id: '$suppliers.id',
        suppliers: { $first: '$suppliers' },
      },
    },
    {
      $replaceRoot: { newRoot: '$suppliers' },
    },
  ]

  const results = await CustomerAccount.aggregate(pipeline)

  return results as CustomerSuppliers[]
}

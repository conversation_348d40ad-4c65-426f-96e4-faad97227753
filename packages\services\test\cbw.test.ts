import { GetObjectCommand, S3Client } from '@aws-sdk/client-s3'
import {
  ACH_TRANSACTION_TYPE,
  Company,
  CustomerAccount,
  emailService,
  getLoanPricingPackage,
  initCbwApiRequester,
  Invoice,
  LMS,
  LoanApplication,
  Operation,
  Sms,
  Transaction,
} from '@linqpal/common-backend'
import { CBWKYB } from '../../../packages/common-backend/index'
import mapper from '../../../packages/common-backend/src/services/lexisNexis/mapper'
import { dictionaries } from '@linqpal/models'
import { mockClient } from 'aws-sdk-client-mock'
import chai from 'chai'
import lambda from 'lambda-tester'
import moment from 'moment'
import * as sinon from 'sinon'
import { v4 as UUID } from 'uuid'
import { loanBuyback } from '../cbw'
import { checkStatus } from '../cbw/checkStatus'
import { checkLoan } from '../linqpal/operationCheck'
import { createBuilder, createSupplier } from './fixtures/bluetape_data'
import {
  beforeEachMockEncryption,
  beforeEachMockSecretsManager,
} from './helper'

chai.should()

const {
  OPERATION_STATUS,
  OPERATION_TYPES,
  PAYMENT_METHODS,
  TRANSACTION_TYPES,
  TRANSACTION_STATUS,
} = dictionaries

describe('CBW', () => {
  let emailSendStub: sinon.SinonStub
  let smsSendStub: sinon.SinonStub
  beforeEach(() => {
    emailSendStub = sinon.stub(emailService, 'send')
    smsSendStub = sinon.stub(Sms, 'send')
  })
  afterEach(() => {
    emailSendStub.restore()
    smsSendStub.restore()
  })

  describe('Checking transaction status', () => {
    let mockAch: sinon.SinonStub,
      builder_id: string,
      supplier_id: string,
      mockedS3: ReturnType<typeof mockClient>
    let cbwkybMock: sinon.SinonStub
    let mapperMock: sinon.SinonStub
    beforeEachMockEncryption()
    beforeEachMockSecretsManager()
    beforeEach(async () => {
      cbwkybMock = sinon
        .stub(CBWKYB, 'getBusinessInstantIdData')
        .callsFake(() => Promise.resolve())

      mapperMock = sinon
        .stub(mapper, 'mapCBWBusinessInstantId')
        .callsFake(() => Promise.resolve())

      const { builderCompanyId } = await createBuilder()
      builder_id = builderCompanyId
      const { supplierCompanyId } = await createSupplier()
      supplier_id = supplierCompanyId
      const achApi = initCbwApiRequester()
      process.env.LP_CBW_ACH_IDENTIFICATION = '*********'
      mockAch = sinon.stub(achApi, 'post')
      mockedS3 = mockClient(S3Client)
      mockedS3.on(GetObjectCommand).callsFake(function () {
        const Body = Buffer.from('')
        return { Body }
      })
    })
    afterEach(() => {
      mockAch.restore()
      mockedS3.reset()
      cbwkybMock.restore()
      mapperMock.restore()
    })

    async function createTransactions() {
      const op = await Operation.create({
        status: OPERATION_STATUS.PROCESSING,
        amount: 10,
        type: OPERATION_TYPES.ACH.PAYMENT,
      })
      const outTransaction = await Transaction.create({
        payee_id: supplier_id,
        type: TRANSACTION_TYPES.ACH.TRANSFER,
        payment_method: PAYMENT_METHODS.ACH,
        status: TRANSACTION_STATUS.PENDING,
        amount: 10,
        operation_id: op.id,
        metadata: {
          transactionNumber: UUID(),
          transactionType: ACH_TRANSACTION_TYPE.OUT,
        },
      })
      const pullTransaction = await Transaction.create({
        payer_id: builder_id,
        type: TRANSACTION_TYPES.ACH.TRANSFER,
        payment_method: PAYMENT_METHODS.ACH,
        status: TRANSACTION_STATUS.PROCESSING,
        amount: 10,
        operation_id: op.id,
        metadata: {
          transactionNumber: UUID(),
          transactionType: ACH_TRANSACTION_TYPE.PULL,
          nextTransaction: outTransaction.id,
        },
      })
      return { op, outTransaction, pullTransaction }
    }

    const EVENT = {
      Records: [
        {
          s3: {
            bucket: { name: 'test-bucket' },
            object: {
              key: '200265891976207_202106290010_20210629.csv',
            },
          },
        } as any,
      ],
    }

    async function findOperation(id: string) {
      const op = await Operation.findById(id)
      if (!op) return null as never
      return op
    }

    async function findTransaction(id: string) {
      const transaction = await Transaction.findById(id)
      if (!transaction) return null as never
      return transaction
    }

    function mockS3(
      id: string,
      Transaction_Type: 'ACH_PULL' | 'ACH_OUT',
      Transaction_Number: string,
      Transaction_Status: 'PENDING' | 'PROCESSED' | 'ERROR',
    ) {
      mockedS3.on(GetObjectCommand).callsFake(function () {
        const Body =
          Buffer.from(`Date,Updated_Date,Transaction_Type,Transaction_Number,Original_Transaction_Number,Reference_Number,Batch_Id,Name,Account_Number,Institution_Name,Institution_Id,Amount,Currency_Code,Status,Transaction_Status,Approvers,Reason,FiToFiDescription,Beneficiary,Beneficiary_Acct
"06/10/2021 10:39:46","06/10/2021 10:39:46",${Transaction_Type},${Transaction_Number},,${id},,"LIAM TEST",************,"************","*********","10.00",840,VERIFIED,${Transaction_Status},,"Invoice payment",,,`)
        return { Body }
      })
    }

    it('should wait on pending', async () => {
      let { op, outTransaction, pullTransaction } = await createTransactions()
      mockS3(
        `prod_${pullTransaction.__v}-${pullTransaction.id}`,
        'ACH_PULL',
        pullTransaction.metadata.transactionNumber || '',
        'PENDING',
      )
      await lambda(checkStatus).event(EVENT).expectResolve()
      op = await findOperation(op.id)
      op.status.should.equal(OPERATION_STATUS.PROCESSING)
      pullTransaction = await findTransaction(pullTransaction.id)
      pullTransaction.status.should.equal(TRANSACTION_STATUS.PROCESSING)
      outTransaction = await findTransaction(outTransaction.id)
      outTransaction.status.should.equal(TRANSACTION_STATUS.PENDING)
    })
    it('should not send out transaction on pull transaction success', async () => {
      let { op, outTransaction, pullTransaction } = await createTransactions()
      mockS3(
        `prod_${pullTransaction.__v}-${pullTransaction.id}`,
        'ACH_PULL',
        pullTransaction.metadata.transactionNumber || '',
        'PROCESSED',
      )
      await lambda(checkStatus).event(EVENT).expectResolve()
      op = await findOperation(op._id)
      op.status.should.equal(OPERATION_STATUS.PROCESSING)
      op.metadata.should.have.property('pullResult')
      pullTransaction = await findTransaction(pullTransaction.id)
      pullTransaction.status.should.equal(TRANSACTION_STATUS.SUCCESS)
      outTransaction = await findTransaction(outTransaction.id)
      outTransaction.status.should.equal(TRANSACTION_STATUS.PENDING)
    })
    it('should mark pull transaction success on duplicate error', async () => {
      let { op, outTransaction, pullTransaction } = await createTransactions()
      mockS3(
        `prod_${pullTransaction.__v}-${pullTransaction.id}`,
        'ACH_PULL',
        pullTransaction.metadata.transactionNumber || '',
        'PROCESSED',
      )
      await lambda(checkStatus).event(EVENT).expectResolve()
      op = await findOperation(op._id)
      op.status.should.equal(OPERATION_STATUS.PROCESSING)
      pullTransaction = await findTransaction(pullTransaction.id)
      pullTransaction.status.should.equal(TRANSACTION_STATUS.SUCCESS)
      outTransaction = await findTransaction(outTransaction.id)
      outTransaction.status.should.equal(TRANSACTION_STATUS.PENDING)
    })
    it('should mark pull transaction failed on status error', async () => {
      let { op, outTransaction, pullTransaction } = await createTransactions()
      mockS3(
        `prod_${pullTransaction.__v}-${pullTransaction.id}`,
        'ACH_PULL',
        pullTransaction.metadata.transactionNumber || '',
        'ERROR',
      )
      await lambda(checkStatus).event(EVENT).expectResolve()
      op = await findOperation(op._id)
      op.status.should.equal(OPERATION_STATUS.FAIL)
      pullTransaction = await findTransaction(pullTransaction.id)
      pullTransaction.status.should.equal(TRANSACTION_STATUS.ERROR)
      outTransaction = await findTransaction(outTransaction.id)
      outTransaction.status.should.equal(TRANSACTION_STATUS.PENDING)
    })
    it('should mark operation success when both pull and out transactions are success', async () => {
      let { op, outTransaction, pullTransaction } = await createTransactions()

      mockS3(
        `prod_${pullTransaction.__v}-${pullTransaction.id}`,
        'ACH_PULL',
        pullTransaction.metadata.transactionNumber || '',
        'PROCESSED',
      )
      await lambda(checkStatus).event(EVENT).expectResolve()
      op = await findOperation(op._id)
      op.status.should.equal(OPERATION_STATUS.PROCESSING)

      mockS3(
        `prod_${outTransaction.__v}-${outTransaction.id}`,
        'ACH_OUT',
        outTransaction.metadata.transactionNumber || '',
        'PROCESSED',
      )
      await lambda(checkStatus).event(EVENT).expectResolve()
      op = await findOperation(op._id)
      op.status.should.equal(OPERATION_STATUS.SUCCESS)
    })
    it('should operation keep unchanged when out transaction fails', async () => {
      let { op, outTransaction, pullTransaction } = await createTransactions()

      mockS3(
        `prod_${outTransaction.__v}-${outTransaction.id}`,
        'ACH_OUT',
        outTransaction.metadata.transactionNumber || '',
        'ERROR',
      )
      await lambda(checkStatus).event(EVENT).expectResolve()
      op = await findOperation(op._id)
      op.status.should.equal(OPERATION_STATUS.PROCESSING)

      mockS3(
        `prod_${pullTransaction.__v}-${pullTransaction.id}`,
        'ACH_PULL',
        pullTransaction.metadata.transactionNumber || '',
        'ERROR',
      )
      await lambda(checkStatus).event(EVENT).expectResolve()
      op = await findOperation(op._id)
      op.status.should.equal(OPERATION_STATUS.FAIL)

      mockS3(
        `prod_${outTransaction.__v}-${outTransaction.id}`,
        'ACH_OUT',
        outTransaction.metadata.transactionNumber || '',
        'PROCESSED',
      )
      await lambda(checkStatus).event(EVENT).expectResolve()
      op = await findOperation(op._id)
      op.status.should.equal(OPERATION_STATUS.FAIL)
    })
  })

  describe('Loan Buyback', async () => {
    beforeEachMockEncryption()
    beforeEachMockSecretsManager()
    let aiMock: sinon.SinonStub
    beforeEach(async () => {
      process.env.LP_LOAN_BUYBACK_DURATION_IN_DAYS = '3'
      process.env.LP_CBW_GL2_IDENTIFICATION = '111111'
      process.env.LP_CBW_OP_IDENTIFICATION = '111111'
      process.env.LP_CBW_GL_IDENTIFICATION = '222222'

      const cbwApi = initCbwApiRequester()
      aiMock = sinon
        .stub(cbwApi, 'post')
        .callsFake((path, { payload }: any) => {
          return Promise.resolve({
            transactionNumber: '123',
            transactionAmountCents: parseInt(payload.transactionAmount.amount),
            transactionStatus: 'PENDING',
            api: {
              reference: '123',
              dateTime: '',
              originalReference: payload.reference,
            },
            statusCode: '000',
            statusDescription: 'SUCCESS',
          })
        })
    })
    afterEach(async () => {
      process.env.LP_LOAN_BUYBACK_DURATION_IN_DAYS = undefined
      aiMock.restore()
    })

    it('should process loan buyback successfully', async () => {
      const loanPackage = await getLoanPricingPackage('packageA')
      const company = await Company.create({
        settings: { loanPricingPackageId: loanPackage!.name },
      })
      const invoice = await Invoice.create({ company_id: company.id })
      const app = await LoanApplication.create({
        status: 'approved',
        approvedAmount: 100,
        invoiceDetails: { invoiceId: invoice.id },
        issueDate: moment()
          .subtract(process.env.LP_LOAN_BUYBACK_DURATION_IN_DAYS! + 2, 'day')
          .toDate(),
      })
      await Operation.create({
        owner_id: app.id,
        type: OPERATION_TYPES.LOAN.ISSUE,
        status: OPERATION_STATUS.SUCCESS,
        amount: app.approvedAmount,
      })
      await LoanApplication.create({
        status: 'approved',
        approvedAmount: 200,
        createdAt: moment()
          .subtract(process.env.LP_LOAN_BUYBACK_DURATION_IN_DAYS, 'days')
          .toDate(),
        issueDate: moment()
          .subtract(process.env.LP_LOAN_BUYBACK_DURATION_IN_DAYS, 'days')
          .toDate(),
      })
      let response = await lambda(loanBuyback).event({}).expectResult()
      response.length.should.eq(1)
      const op = await Operation.findOne({
        type: OPERATION_TYPES.LOAN.BUYBACK,
      })
      console.log(op)
      const advanceRate =
        (((app.approvedAmount * (100 - loanPackage!.metadata.merchant)) / 100) *
          loanPackage!.metadata.advanceRate) /
        100
      op!.amount.should.eq(advanceRate)
      op!.owner_id.should.eq(app.id)
      op!.status.should.eq(OPERATION_STATUS.SUCCESS)
      const t = await Transaction.findOne({ operation_id: op!.id })
      console.log(t)
      t!.status.should.eq(TRANSACTION_STATUS.SUCCESS)

      response = await lambda(loanBuyback).event({}).expectResult()
      response.length.should.eq(0)
    })
  })

  describe('checkLoan testing: ', async () => {
    let loanFindStub: sinon.SinonStub,
      checkIsPaidStub: sinon.SinonStub,
      companyFindStub: sinon.SinonStub,
      syncStub: sinon.SinonStub

    beforeEach(async () => {
      loanFindStub = sinon.stub(LoanApplication, 'findOne').callsFake(() => {
        return { _id: 1000, lms_id: 20, company_id: '999' } as any
      })
      companyFindStub = sinon.stub(Company, 'findOne').callsFake(() => {
        return { name: 'BlueTape', id: '999' } as any
      })
      syncStub = sinon.stub(LMS, 'syncLoanApplicationFields')
    })

    afterEach(async () => {
      checkIsPaidStub.restore()
      loanFindStub.restore()
      companyFindStub.restore()
      syncStub.restore()
    })

    it('test successful repayment', async () => {
      checkIsPaidStub = sinon
        .stub(LMS, 'checkIsFullyPaid')
        .callsFake(() => Promise.resolve(false))
      const loanInfoStub = sinon.stub(LMS, 'getLoanInfo').callsFake(() => {
        return {
          id: '5bff930f-1151-4f37-ad5f-98d66743bc32',
          companyId: '62eb9f1b3fe244183b375bfd',
          amount: 350,
          fee: 0,
          activeLoanTemplateId: '3476b83d-f7c7-492d-a986-a6e3b3b87b43',
          activeLoanTemplate: {
            id: '3476b83d-f7c7-492d-a986-a6e3b3b87b43',
            loanFeePercentage: 0,
            lateFeePercentage: 0,
            installmentsNumber: 1,
            paymentIntervalInDays: 7,
            minimumLateFeeAmount: 35,
            gracePeriodInDays: 0,
            code: '30',
          },
          loanReceivables: [
            {
              id: '8bfdf6bd-bd79-427b-a5b6-3cde5c84180e',
              expectedDate: '2022-08-04',
              type: 'Installment',
              paidDate: null,
              expectedAmount: 350,
              paidAmount: 0,
              status: 'Late',
              loanId: '5bff930f-1151-4f37-ad5f-98d66743bc32',
            },
          ],
          status: 'Started',
          isDeleted: false,
          isOverdue: true,
        } as any
      })
      const smsUserStub = sinon.stub(Sms, 'smsUser')
      const getMessageStub = sinon.stub(Sms, 'getMessage').callsFake(() => 'ne')

      await checkLoan({ owner_id: '55' } as any)

      loanInfoStub.calledOnce.should.be.true
      getMessageStub.calledWithExactly({
        key: 'loanRepayment',
        nextDueDate: '10/15/2021',
        BusinessName: 'BlueTape',
      } as any)

      loanFindStub.calledWithExactly({ _id: '55' }).should.be.true
      smsUserStub.calledWithExactly('999', 'ne')

      getMessageStub.restore()
      loanInfoStub.restore()
      smsUserStub.restore()
    })
  })

  describe('Notification', () => {
    let mockAch: sinon.SinonStub,
      mockedS3: ReturnType<typeof mockClient>,
      lmsStub: sinon.SinonStub
    beforeEachMockEncryption()
    beforeEachMockSecretsManager()
    beforeEach(async () => {
      const achApi = initCbwApiRequester()
      process.env.LP_CBW_ACH_IDENTIFICATION = '*********'
      mockAch = sinon.stub(achApi, 'post')
      mockedS3 = mockClient(S3Client)
      mockedS3.on(GetObjectCommand).callsFake(function () {
        const Body = Buffer.from('')
        return { Body }
      })
      lmsStub = sinon.stub(LMS, 'rejectPayment')
    })
    afterEach(() => {
      mockAch.restore()
      mockedS3.reset()
      lmsStub.restore()
    })

    it('Notify customer', async () => {
      const customer = await CustomerAccount.create({
        email: '<EMAIL>',
        phone: '+***********',
      })
      const invoice = await Invoice.create({ customer_account_id: customer.id })
      const app = await LoanApplication.create({
        invoiceDetails: { invoiceId: [invoice.id] },
      })
      const operation = await Operation.create({
        owner_id: app.id,
        type: OPERATION_TYPES.LOAN.REPAYMENT,
        status: OPERATION_STATUS.PROCESSING,
        amount: 100_000,
        metadata: {
          lms_paymentId: '234',
          paymentDate: moment().subtract(1, 'week').toDate(),
        },
      })
      const transaction = await Transaction.create({
        operation_id: operation.id,
        metadata: { transactionNumber: '123' },
        type: TRANSACTION_TYPES.ACH.TRANSFER,
        payment_method: PAYMENT_METHODS.ACH,
        status: TRANSACTION_STATUS.PROCESSING,
        amount: 100_000,
      })
      const id = `prod_${transaction.__v}-${transaction.id}`
      mockedS3.on(GetObjectCommand).callsFake(function () {
        const Body =
          Buffer.from(`Date,Updated_Date,Transaction_Type,Transaction_Number,Original_Transaction_Number,Reference_Number,Batch_Id,Name,Account_Number,Institution_Name,Institution_Id,Amount,Currency_Code,Status,Transaction_Status,Approvers,Reason,FiToFiDescription,Beneficiary,Beneficiary_Acct
"06/10/2021 10:39:46","06/10/2021 10:39:46",ACH_PULL_RETURN,CLEDGER1541092444854,123,${id},,"LIAM TEST",************,"************","*********","10.00",840,VERIFIED,PROCESSED,,"Invoice payment",,,`)
        return { Body }
      })
      await lambda(checkStatus)
        .event({
          Records: [
            {
              s3: {
                bucket: { name: 'test-bucket' },
                object: {
                  key: '200265891976207_202106290010_20210629.csv',
                },
              },
            } as any,
          ],
        })
        .expectResolve()
      lmsStub.called.should.be.true
      lmsStub.firstCall.firstArg.should.eq(operation.metadata.lms_paymentId)
      smsSendStub.called.should.be.true
      smsSendStub.firstCall.firstArg.should.eq(customer.phone)
      emailSendStub.calledTwice.should.be.true
      emailSendStub.secondCall.firstArg.to.should.eq(customer.email)
    })
  })
})

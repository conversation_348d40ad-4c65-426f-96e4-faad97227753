import { useState, useCallback } from 'react'
import { routes } from '@linqpal/models'
import { TurnOffAutoPayIHCFlowState } from '../flows/TurnOffAutoPayIHCFlow/types'

export const useTurnOffAutoPayIHC = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const turnOffAutoPayIHC = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      await routes.contractor.ihcAutoPayDisable()

      setLoading(false)
      return true
    } catch (err) {
      setError(err as Error)
      setLoading(false)
      return false
    }
  }, [])

  return { turnOffAutoPayIHC, loading, error }
}

export function useTurnOffAutoPayIHCFlow() {
  const [flowState, setFlowState] = useState<TurnOffAutoPayIHCFlowState>(
    TurnOffAutoPayIHCFlowState.INIT,
  )

  const { turnOffAutoPayIHC, loading } = useTurnOffAutoPayIHC()

  const handleTurnOffAutoPay = useCallback(async () => {
    const success = await turnOffAutoPayIHC()
    if (success) {
      setFlowState(TurnOffAutoPayIHCFlowState.SUCCESS)
    }
    return success
  }, [turnOffAutoPayIHC])

  return {
    flowState,
    loading,
    handleTurnOffAutoPay,
  }
}

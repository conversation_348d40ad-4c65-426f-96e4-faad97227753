import React from 'react'
import { StyleSheet, Text, View } from 'react-native'
import { Divider } from 'react-native-paper'
import { useInvoiceDetailsContext } from '../../../TabInvoice/InvoiceDetailsContext'
import BuilderBottomModal from '../../../../../ui/molecules/BuilderBottomModal'
import { useTranslation } from 'react-i18next'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'
import { currencyMask } from '../../../../../utils/helpers/masking'
import {
  PaidInvoiceStatuses,
  PaymentProcessingInvoiceStatuses,
} from '../../../PayablesTab/enums'

export const PaymentHistoryInvoiceDetailsBottomModal = ({ invoice }) => {
  const { t } = useTranslation('global')
  const {
    paymentHistoryInvoiceDetailsModalVisibility,
    setPaymentHistoryInvoiceDetailsModalVisibility,
  } = useInvoiceDetailsContext()

  const onClose = () => {
    setPaymentHistoryInvoiceDetailsModalVisibility(false)
  }
  const paidAmount = invoice.totalPaidAmount
    ? invoice.totalPaidAmount
    : PaidInvoiceStatuses.includes(invoice.status)
    ? invoice.totalAmount
    : 0

  const processingAmount = invoice.totalProcessingAmount
    ? invoice.totalProcessingAmount
    : PaymentProcessingInvoiceStatuses.includes(invoice.status)
    ? invoice.totalAmount
    : 0

  const remainingAmount =
    PaymentProcessingInvoiceStatuses.includes(invoice.status) ||
    PaidInvoiceStatuses.includes(invoice.status)
      ? 0
      : invoice.totalRemainingAmount

  const processingFee = React.useMemo(() => {
    const transactions = invoice?.operation?.allTransactions || []
    return transactions.reduce((sum, transaction) => {
      return sum + (transaction.fee || 0)
    }, 0)
  }, [invoice?.operation?.allTransactions])

  return (
    <BuilderBottomModal
      visible={paymentHistoryInvoiceDetailsModalVisibility}
      title={t('PaymentHistoryInvoice.invoiceDetailsModal.title')}
      onClose={onClose}
      height={614}
    >
      <View style={styles.container}>
        <AmountDisplay
          amount={invoice.total_amount}
          label={t('PaymentHistoryInvoice.invoiceDetailsModal.invoiceAmount')}
        />

        <AmountDisplay
          amount={invoice.customerFee}
          label={t('PaymentHistoryInvoice.invoiceDetailsModal.customerFee')}
        />

        <AmountDisplay
          amount={processingFee}
          label={t('PaymentHistoryInvoice.invoiceDetailsModal.processingFee')}
        />

        <AmountDisplay
          amount={invoice.lateFee}
          label={t('PaymentHistoryInvoice.invoiceDetailsModal.lateFee')}
        />

        <AmountDisplay
          amount={invoice.totalAmountDue}
          label={t('PaymentHistoryInvoice.invoiceDetailsModal.totalAmountDue')}
        />

        <AmountDisplay
          amount={processingAmount}
          label={t(
            'PaymentHistoryInvoice.invoiceDetailsModal.processingAmount',
          )}
        />

        <AmountDisplay
          amount={paidAmount}
          label={t('PaymentHistoryInvoice.invoiceDetailsModal.paidAmount')}
        />

        <AmountDisplay
          amount={remainingAmount}
          label={t('PaymentHistoryInvoice.invoiceDetailsModal.remainingAmount')}
          valueStyle={remainingAmount ? { fontWeight: '700' } : {}}
        />
      </View>
    </BuilderBottomModal>
  )
}

const AmountDisplay = ({ amount, label, valueStyle = {} }) => (
  <>
    <View style={styles.amountTypeContainer}>
      <Text style={composeStyle(styles.normalText, { maxWidth: '50%' })}>
        {label}
      </Text>
      <Text style={[styles.valueText, valueStyle]}>{currencyMask(amount)}</Text>
    </View>

    <Divider style={styles.divider} />
  </>
)

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 20,
    marginVertical: 20,
  },
  normalText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 21,
    color: '#758590',
    fontFamily: 'Inter',
  },
  valueText: {
    fontSize: 14,
    lineHeight: 21,
    fontFamily: 'Inter',
    fontWeight: '500',
    maxWidth: '50%',
    textAlign: 'right',
    color: '#001929',
  },

  amountTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 26,
  },
  divider: {
    marginTop: 14,
    marginBottom: 14,
  },
})

const SessionStorage = {
  /**
   * Store a value in session storage
   * @param {string} key - The key to store the value under
   * @param {any} value - The value to store (will be JSON stringified)
   * @returns {void}
   */
  set: (key, value) => {
    if (!key) return null
    try {
      const serializedValue = JSON.stringify(value)
      window.sessionStorage.setItem(key, serializedValue)
      return true
    } catch (error) {
      console.error('SessionStorage.set error:', error)
      return null
    }
  },

  /**
   * Get a value from session storage
   * @param {string} key - The key to retrieve the value from
   * @param {any} defaultValue - Default value if not found
   * @returns {any} - The stored value or defaultValue if not found
   */
  get: (key, defaultValue = null) => {
    if (!key) return defaultValue
    try {
      const item = window.sessionStorage.getItem(key)
      if (item === null) return defaultValue
      return JSON.parse(item)
    } catch (error) {
      console.error('SessionStorage.get error:', error)
      return defaultValue
    }
  },

  /**
   * Remove a value from session storage
   * @param {string} key - The key to remove
   * @returns {boolean} - True if successful
   */
  remove: (key) => {
    if (!key) return false
    try {
      window.sessionStorage.removeItem(key)
      return true
    } catch (error) {
      console.error('SessionStorage.remove error:', error)
      return false
    }
  },

  /**
   * Clear all session storage items
   * @returns {void}
   */
  clear: () => {
    try {
      window.sessionStorage.clear()
      return true
    } catch (error) {
      console.error('SessionStorage.clear error:', error)
      return false
    }
  },
}

export default SessionStorage

import {
  Company,
  connectToDatabase,
  emailService,
  getEnvironmentVariables,
  initializeFinicity,
  LMS,
  LoanApplication,
  PlaidService,
} from '@linqpal/common-backend'
import EmailBuilder from '@linqpal/common-backend/src/helpers/EmailBuilder'
import {
  ICompany,
  ILoanApplication,
} from '@linqpal/common-backend/src/models/types'
import { ILoan } from '@linqpal/common-backend/src/services/lms.service'
import moment from 'moment-timezone'
import { stringify as stringifySearchParams } from 'query-string'

const groupBy = <T>(arr: T[], callback: (item: T, index: number) => string) => {
  return arr.reduce((acc = {}, item, index) => {
    const key = callback(item, index)
    acc[key] ??= []
    acc[key].push(item)
    return acc
  }, {} as { [key: string]: T[] })
}

function adminUrl() {
  return process.env.LP_MODE === 'prod'
    ? 'https://admin.bluetape.com'
    : `https://${process.env.LP_MODE}-admin.bluetape.com`
}

async function reportAlert(
  company: ICompany,
  app: ILoanApplication,
  availableBalance: number,
  lmsInfo: ILoan,
) {
  const search_string = stringifySearchParams({
    search: company.name,
    status: app.status,
    id: app.id,
  })

  const emailMessage = EmailBuilder.getSubjectAndBody({
    key: 'checkBalanceReportAlert',
    data: {
      name: company.name,
      approvedAmount: app.approvedAmount,
      nextPaymentAmount: lmsInfo.nextPaymentAmount,
      availableBalance,
      url: `${adminUrl()}/loan-applications?${search_string}`,
    },
  })
  const html = `<div>${emailMessage.body}</div>`
  console.log('Reporting', emailMessage.body)
  await emailService.send({
    to:
      process.env.LP_MODE === 'prod'
        ? emailService.EMAILS.PRODUCT_OPERATION_TEAM?.split(',')
        : emailService.EMAILS.PRODUCT_OPERATION_TEAM_TEST?.split(','),
    subject: emailMessage.subject,
    html,
  })
}

async function getAvailableBalance(customerId: string, accountId: string) {
  const finicity = await initializeFinicity()

  let tries = 2
  while (tries > 0) {
    try {
      const { availableBalance } = await finicity.accounts.availableBalanceLive(
        customerId,
        accountId,
      )
      return availableBalance
    } catch (e: any) {
      console.log(e.message)
      tries--
    }
  }
  try {
    const { availableBalance } = await finicity.accounts.availableBalance(
      customerId,
      accountId,
    )
    return availableBalance
  } catch (e: any) {
    console.log(e.message)
  }
  const { balance } = await finicity.accounts.getAccounts(customerId, accountId)
  return balance
}

async function balanceReportAlert(
  app: ILoanApplication,
  company: ICompany,
  lmsInfo: LMS.ILoan | null,
  availableBalance: number,
) {
  console.log(
    app.loanpro_id || app.lms_id,
    lmsInfo?.nextPaymentDate,
    availableBalance,
    lmsInfo?.nextPaymentAmount,
  )
  if (+lmsInfo!.nextPaymentAmount > availableBalance) {
    await reportAlert(company, app, availableBalance, lmsInfo!)
  }
}

async function findLoanApplications(
  start_from: Date,
  end_to: Date,
): Promise<ILoanApplication[]> {
  return LoanApplication.aggregate()
    .addFields({
      issue_date: {
        $convert: {
          input: '$issueDate',
          to: 'date',
          onError: null,
        },
      },
    })
    .match({
      $expr: {
        $and: [
          { $gte: ['$issue_date', start_from] },
          { $lt: ['$issue_date', end_to] },
        ],
      },
    })
}

async function checkCompanyLoanBalances(grouped: [string, ILoanApplication[]]) {
  const company = await Company.findById(grouped[0]).populate('bankAccounts')

  if (!company) return

  const plaid_account = company.bankAccounts?.find(
    (account) =>
      (account.isPrimaryForCredit ||
        ((account.isPrimaryForCredit === null ||
          account.isPrimaryForCredit === undefined) &&
          account.isPrimary)) &&
      account.plaid?.account_id &&
      account.plaid?.access_token,
  )
  let available_balance = 0
  if (plaid_account) {
    available_balance =
      (await PlaidService.getAvailableBalance(company.id, plaid_account)) || 0
  } else {
    const accountId = company.bankAccounts?.find((account) => {
      return account.finicity?.accountId && account.isPrimary
    })?.finicity?.accountId
    const customerId = company.finicity?.customerId
    if (!customerId || !accountId) return
    available_balance = await getAvailableBalance(customerId, accountId)
  }

  const loans = grouped[1]

  await Promise.allSettled(
    loans.map(async (loan) => {
      return balanceReportAlert(
        loan,
        company,
        await LMS.getLoanInfo(loan.lms_id),
        available_balance,
      )
    }),
  )
}

async function checkDay15() {
  const day_14 = moment().utc().endOf('day').subtract(14, 'days').toDate()
  const day_15 = moment().utc().endOf('day').subtract(15, 'days').toDate()

  const applications = await findLoanApplications(day_15, day_14)

  const grouped = groupBy(applications, (item) => item.company_id)
  const entries = Object.entries(grouped)

  await Promise.allSettled(entries.map(checkCompanyLoanBalances))
}

async function checkDay27() {
  const day_26 = moment().utc().endOf('day').subtract(26, 'days').toDate()
  const day_27 = moment().utc().endOf('day').subtract(27, 'days').toDate()

  const applications = await findLoanApplications(day_27, day_26)
  const grouped = groupBy(applications, (item) => item.company_id)
  const entries = Object.entries(grouped)

  await Promise.allSettled(entries.map(checkCompanyLoanBalances))
}

async function check3DaysBefore() {
  const day_2 = moment().utc().endOf('day').add(2, 'days').toDate()
  const day_3 = moment().utc().endOf('day').add(3, 'days').toDate()

  const applications = await LoanApplication.aggregate()
    .addFields({
      next_payment_date: {
        $convert: {
          input: '$nextPaymentDate',
          to: 'date',
          onError: null,
        },
      },
    })
    .match({
      $expr: {
        $and: [
          { $gt: ['$next_payment_date', day_2] },
          { $lte: ['$next_payment_date', day_3] },
        ],
      },
    })

  const grouped = groupBy(applications, (item) => item.company_id)
  const entries = Object.entries(grouped)

  await Promise.allSettled(entries.map(checkCompanyLoanBalances))
}

export default async function finicityBalanceReport() {
  await getEnvironmentVariables()
  await connectToDatabase()
  const results = await Promise.allSettled([
    checkDay15(),
    checkDay27(),
    check3DaysBefore(),
  ])
  const rejects = results
    .filter((r) => r.status === 'rejected')
    .map((r) => (r as PromiseRejectedResult).reason)

  if (rejects.length > 0) {
    return Promise.reject({ result: 'error', rejects })
  }

  return {
    result: 'ok',
  }
}

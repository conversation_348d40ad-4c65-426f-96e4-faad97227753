import { loanService } from '@linqpal/common-backend/src/services/loan.dotnet.service'
import { Request, Response } from 'express'
import {
  Company,
  User,
  UserRole,
  LMS,
  Operation,
  Transaction,
  GlobalLogger,
} from '@linqpal/common-backend'
import { IIhc<PERSON>oan } from './inHouseCreditInvoicesList'
import mongoose from 'mongoose'
import { groupBy } from 'lodash'
import { CriticalError } from '@linqpal/models/src/types/exceptions'

export const inHouseCreditInvoiceDetails = async (
  req: Request,
  res: Response,
) => {
  const loanId = req.query.id

  if (typeof loanId !== 'string') {
    throw new CriticalError('Loan ID was not specified', {
      searchParams: req.query,
    })
  }

  const loan: IIhcLoan = await loanService.getLoanById(loanId)

  if (!loan) {
    throw new CriticalError(`Loan with id ${loanId} was not found`, {
      loanId,
      loan,
    })
  }

  // Get user name for autoCollectionPausedBy if it exists
  if (loan.autoCollectionPausedBy) {
    try {
      const user = await User.findById(loan.autoCollectionPausedBy)
      if (user) {
        const userName =
          `${user.firstName || ''} ${user.lastName || ''}`.trim() ||
          user.login ||
          user.name
        if (userName) {
          loan.autoCollectionPausedBy = userName
        }
      }
    } catch (error) {
      console.warn(
        'Failed to fetch user for autoCollectionPausedBy:',
        loan.autoCollectionPausedBy,
        error,
      )
    }
  }

  GlobalLogger.info({ loan }, 'Got loan from LMS')

  const companyAggregateResult = await Company.aggregate<{
    owners: unknown[]
    name: string
  }>()
    .match({
      _id: new mongoose.Types.ObjectId(loan.companyId),
    })
    .addFields({
      id: {
        $toString: '$_id',
      },
    })
    .lookup({
      from: UserRole.collection.name,
      localField: 'id',
      foreignField: 'company_id',
      as: 'userRoles',
      pipeline: [
        {
          $match: {
            role: 'Owner',
          },
        },
      ],
    })
    .lookup({
      from: User.collection.name,
      localField: 'userRoles.sub',
      foreignField: 'sub',
      as: 'owners',
    })
    .project({
      owners: 1,
      name: 1,
    })
    .exec()

  const company = companyAggregateResult.at(0)

  if (!company) {
    throw new CriticalError(`Can't find company with id ${loan.companyId}`, {
      loan,
      company,
    })
  }

  GlobalLogger.info({ company }, 'Got company from pipeline')

  loan.companyOwners = company?.owners
  loan.companyName = company?.name

  const notes = await LMS.getNotes(loan.id)

  GlobalLogger.info({ notes }, 'Got notes from LMS')

  const noteUsers = await User.find({
    sub: mongoose.trusted({
      $in: notes?.map((n) => n.userId),
    }),
  })

  const updated =
    notes?.map((n) => {
      const user = noteUsers.find((u) => u.sub === n.userId)
      return {
        ...n,
        userName: user ? `${user.firstName} ${user.lastName}` : n.userId,
      }
    }) || []

  loan.notes = updated

  const credit = loan.creditId
    ? await loanService.getCreditById(loan.creditId)
    : undefined
  loan.credit = credit

  const payablesId = loan.loanPayables?.map((payable) => payable.payableId)

  const operations = await Operation.find({
    owner_id: mongoose.trusted({
      $in: payablesId ?? [],
    }),
  })

  const transactions = await Transaction.find({
    operation_id: mongoose.trusted({
      $in: operations.map((op) => op.id),
    }),
  })

  GlobalLogger.info(
    {
      payablesId,
      operations: operations.map((el) => el.toJSON()),
      transactions: transactions.map((el) => el.toJSON()),
    },
    'Got operations and transactions from MongoDB',
  )

  const groupedTransactions = groupBy(transactions, (el) => el.operation_id)

  loan.operations = await Promise.all(
    operations.map(async (operation) => ({
      ...operation.toObject(),
      transactions: groupedTransactions[operation.id],
    })),
  )

  res.send({ item: loan })
}

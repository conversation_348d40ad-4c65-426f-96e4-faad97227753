import React from 'react'
import Svg, { Path } from 'react-native-svg'

interface BtDropdownArrowProps {
  width?: number
  height?: number
  color?: string
  isOpen?: boolean
}

export const BtDropdownArrow: React.FC<BtDropdownArrowProps> = ({
  width = 24,
  height = 24,
  color = '#19262F',
  isOpen = false,
}) => {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      style={{
        transform: [{ rotate: isOpen ? '180deg' : '0deg' }],
      }}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.9998 15.5C11.7438 15.5 11.4878 15.402 11.2928 15.207L7.29276 11.207C6.90176 10.816 6.90176 10.184 7.29276 9.79301C7.68376 9.40201 8.31576 9.40201 8.70676 9.79301L12.0118 13.098L15.3048 9.91801C15.7038 9.53501 16.3348 9.54601 16.7188 9.94301C17.1028 10.34 17.0918 10.974 16.6948 11.357L12.6948 15.219C12.4998 15.407 12.2498 15.5 11.9998 15.5Z"
        fill={color}
      />
    </Svg>
  )
}

import React, { ReactNode } from 'react'
import { TouchableOpacity, Text } from 'react-native'
import { composeStyle } from '../../../../common-frontend/src/helpers'

type LinkProps = {
  onPress?: () => void
  title: string
  color?: string
  icon?: ReactNode
  textStyle?: any
  testID?: string
  style?: any
  disabled?: boolean
}

export const Link = ({
  onPress,
  title,
  color,
  icon,
  textStyle = {},
  testID = 'link',
  style = {},
  disabled = false,
}: LinkProps) => {
  return (
    <TouchableOpacity
      style={composeStyle(
        {
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
        },
        style,
      )}
      onPress={onPress}
      testID={testID}
      disabled={disabled}
    >
      <Text
        style={composeStyle(
          { color: color, fontSize: 14, fontWeight: '500' },
          textStyle,
        )}
      >
        {title}
      </Text>
      {icon}
    </TouchableOpacity>
  )
}

import React from 'react'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import StyledAlert from '../../molecules/StyledAlert'
import StyledMobileAlert from '../../molecules/StyledMobileAlert'
import AutoPayIHCEnabledWarning, {
  AutoPayIHCEnabledWarningOrigin,
} from './AutoPayIHCEnabledWarning'
import { IconAutoRenewSign, IconWarningSign } from '../../../assets/icons'
import { useStore } from '../../../store'
import useIsMobile from '../../../screens/Contractor/PayablesTab/hooks/useIsMobile'
import { Spacer } from '@linqpal/components/src/ui'

export const AutoPayIHCWarning: React.FC<{
  onActionPress: () => void
  origin?: AutoPayIHCEnabledWarningOrigin
  onClose?: () => void
  marginBottom?: number
  marginTop?: number
  isAutoPayIhcFlowOpened?: boolean
}> = observer(
  ({
    onActionPress,
    onClose,
    origin = AutoPayIHCEnabledWarningOrigin.Payables,
    marginBottom = 0,
    marginTop = 0,
    isAutoPayIhcFlowOpened,
  }) => {
    const { t } = useTranslation('global')
    const isMobile = useIsMobile()
    const {
      userStore,
      screensStore: {
        paymentMethodsStore: { paymentMethods, loading: paymentMethodsLoading },
      },
    } = useStore()

    const isUserInfoLoading = !userStore.userInfoReady
    const arePaymentMethodsLoading = paymentMethodsLoading
    const isLoading = isAutoPayIhcFlowOpened
      ? false
      : isUserInfoLoading || arePaymentMethodsLoading

    const isAnyFactoringEnabled =
      userStore.suppliers?.some((suppl) => suppl.isFactoringEnabled) || false

    const primaryIHCAutoPayMethod = paymentMethods.find(
      (method) => method.isPrimaryForIHCAutoPay,
    )

    const Alert = isMobile ? StyledMobileAlert : StyledAlert

    if (!isAnyFactoringEnabled) {
      return null
    }
    const inHouseSettings = userStore.company?.settings?.inHouseCredit
    const isAutoPayEnabled = inHouseSettings?.isAutoPayEnabledByCompanyUser
    const isAutoPayRequired = inHouseSettings?.isAutoPayRequired || false

    if (isAutoPayEnabled && primaryIHCAutoPayMethod) {
      return (
        <>
          {!!marginTop && <Spacer height={marginTop} />}
          <AutoPayIHCEnabledWarning
            onActionPress={onActionPress}
            primaryPaymentMethod={primaryIHCAutoPayMethod}
            loading={isLoading}
            origin={origin}
          />
          {!!marginBottom && <Spacer height={marginBottom} />}
        </>
      )
    }

    return (
      <>
        {!!marginTop && <Spacer height={marginTop} />}
        {isAutoPayRequired ? (
          <Alert
            icon={<IconWarningSign width={20} height={22} />}
            title={t('autoPayWarning.required.title')}
            message={t('autoPayWarning.required.message')}
            buttonText={t('autoPayWarning.required.buttonText')}
            onActionPress={onActionPress}
            status="error"
            testID="ihc_autopay_required_banner"
            loading={isLoading}
          />
        ) : (
          <Alert
            icon={<IconAutoRenewSign width={20} height={22} />}
            title={t('autoPayWarning.recommended.title')}
            message={t('autoPayWarning.recommended.message')}
            buttonText={t('autoPayWarning.recommended.buttonText')}
            onActionPress={onActionPress}
            onClose={onClose}
            status="info"
            testID="ihc_autopay_recommended_banner"
            loading={isLoading}
          />
        )}
        {!!marginBottom && <Spacer height={marginBottom} />}
      </>
    )
  },
)

export default AutoPayIHCWarning

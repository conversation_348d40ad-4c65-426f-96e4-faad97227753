import { dictionaries, exceptions, InvoicePaymentType } from '@linqpal/models'
import { processTransaction } from './processTransaction'
import checkSession from '../cbw/checkSession'
import {
  ACH_TRANSACTION_TYPE,
  BankAccount,
  Company,
  Invoice,
  Transaction,
} from '../../models'
import { ICompany, IOperation, ITransaction } from '../../models/types'
import { getSelectedCardPricingPackageDetails } from '../package.service'
import { generateTransactionObject } from './helpers'
import mongoose from 'mongoose'
import { invoicesService } from '../invoices.service'
import * as math from 'mathjs'
import { LedgerService } from '../../../index'
import moment from 'moment-timezone'
import { PaymentNotifications } from '../payment/paymentNotifications.service'
import { LogicalError } from '@linqpal/models/src/types/exceptions'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'
import { FactoringService } from '../factoring'
import { IPaymentInfo } from '../payment/types'
import { getCardFee } from '@linqpal/models/src/helpers/getCardFee'

export const cardPayment = async (
  operations: IOperation[],
  {
    transactionType,
    sourceAccountId,
    destinationAccountId,
    reason,
    supplierId,
    sendNotifications = true,
    userRequestedAmount,
    isAutoDebit,
  }: {
    transactionType: 'PULL' | 'PUSH'
    sourceAccountId: string
    destinationAccountId: string
    reason: string
    supplierId: string
    sendNotifications?: boolean
    userRequestedAmount?: number
    isAutoDebit?: boolean
  },
  session: mongoose.ClientSession,
): Promise<IPaymentInfo> => {
  checkSession(session)
  let totalAmount = 0,
    totalTax = 0,
    totalFee = 0

  const transactions: ITransaction[] = []

  const pricingPackage = await getSelectedCardPricingPackageDetails(
    supplierId,
    sourceAccountId,
  )
  if (!pricingPackage)
    throw new exceptions.LogicalError('Package for the payment type not found')

  for (const operation of operations) {
    if (
      !operation.metadata.payee_id ||
      (operation.metadata.paymentInitiator !== 'company' &&
        !operation.metadata.payer_id)
    ) {
      throw new exceptions.LogicalError('operation/payee-or-payer-missing')
    }

    const invoice = await Invoice.findById(operation.owner_id).session(session)
    if (!invoice) throw new Error('Invoice not found')

    const isFactoringInvoice =
      invoice.paymentDetails?.paymentType === InvoicePaymentType.FACTORING &&
      invoice.paymentDetails?.arAdvanceStatus === ArAdvanceStatus.Approved

    const supplierCompany = await Company.findById(
      operation.metadata.payee_id,
    ).session(session)
    if (!supplierCompany) throw new LogicalError('business-not-found')

    const baseAmount = isFactoringInvoice
      ? await FactoringService.calculateInvoiceUnpaidAmount(invoice)
      : invoice.total_amount

    const invoiceFees = isFactoringInvoice
      ? await FactoringService.calculateCustomerFees(invoice, supplierCompany)
      : 0

    let amount = baseAmount + invoiceFees

    if (userRequestedAmount && isFactoringInvoice) {
      const roundedUserRequestedAmount =
        Math.round(userRequestedAmount * 100) / 100
      if (roundedUserRequestedAmount > Math.round(amount * 100) / 100) {
        throw new exceptions.LogicalError(
          'Amount requested for payment cannot be larger than remaining amount',
        )
      } else {
        amount = roundedUserRequestedAmount
      }
    }

    const supplierFees: number = await getSupplierFees(
      operation.amount,
      sourceAccountId,
      supplierCompany,
    )

    const achOutAmount = math.round(operation.amount - supplierFees, 2)

    let fee = 0

    if (isFactoringInvoice) {
      fee = await calculateCardFee(amount, sourceAccountId)
    } else if (pricingPackage) {
      // prettier-ignore
      fee = (amount * pricingPackage.customer.percentage) / 100 + pricingPackage.customer.amount
    } else {
      fee = 0
    }

    totalAmount += amount
    totalTax += Number(invoice.tax_amount)
    totalFee += fee

    const transactionObject = generateTransactionObject({
      operation,
      amount,
      fee,
      reason,
      sourceAccountId,
      destinationAccountId,
      isAutoDebit: isFactoringInvoice ? isAutoDebit : undefined,
    })

    if (!operation.firstTransactionDate) {
      operation.firstTransactionDate = moment()
    }

    const [pullTransaction] = await Transaction.create([transactionObject], {
      session,
    })

    if (!isFactoringInvoice) {
      let [outTransaction] = await Transaction.find(
        {
          operation_id: operation.id,
          payee_id: operation.metadata.payee_id,
          'metadata.transactionType': ACH_TRANSACTION_TYPE.OUT,
        },
        null,
        { session },
      ).sort({ updatedAt: -1 })

      if (!outTransaction) {
        ;[outTransaction] = await Transaction.create(
          [
            {
              operation_id: operation.id,
              payee_id: operation.metadata.payee_id,
              amount: math.round(achOutAmount, 2),
              fee: math.round(supplierFees, 2),
              currency: 'USD',
              type: dictionaries.TRANSACTION_TYPES.ACH.TRANSFER,
              payment_method: dictionaries.PAYMENT_METHODS.ACH,
              status: dictionaries.TRANSACTION_STATUS.PENDING,
              reason,
              metadata: {
                transactionType: ACH_TRANSACTION_TYPE.OUT,
              },
            },
          ],
          { session },
        )
      } else if (
        outTransaction.status === dictionaries.TRANSACTION_STATUS.PENDING
      ) {
        outTransaction.amount = math.round(achOutAmount, 2)
        outTransaction.fee = math.round(supplierFees, 2)
        await outTransaction.save()
      }

      pullTransaction.metadata.nextTransaction = outTransaction._id
      pullTransaction.markModified('metadata')
    }
    await pullTransaction.save()

    operation.status = dictionaries.OPERATION_STATUS.PROCESSING
    await operation.save()

    // guest payment has its own notification to supplier
    if (sendNotifications) {
      await invoicesService.sendInvoicePaymentNotification(
        operation.owner_id,
        operation.status,
      )

      await PaymentNotifications.invoiceProcessingToCustomer(
        invoice,
        sourceAccountId,
        'card',
        fee,
        amount,
        amount + fee,
        session,
      )
    }

    transactions.push(pullTransaction)
  }

  if (!totalAmount || totalAmount <= 0) {
    throw new exceptions.LogicalError('Payment amount is incorrect')
  }

  await processTransaction(
    transactions,
    transactionType,
    sourceAccountId,
    destinationAccountId,
    totalAmount,
    totalTax,
    totalFee,
    supplierId,
    session,
  )

  await Promise.all(
    operations.map((op) => LedgerService.handlePaymentOrder(op.id)),
  )

  return {
    totalProcessingAmount: totalAmount,
  }
}

const getSupplierFees = async (
  invoiceAmount: number,
  sourceAccountId: string,
  supplierCompany: ICompany,
) => {
  const pricingPackage = await getSelectedCardPricingPackageDetails(
    supplierCompany.id,
    sourceAccountId,
  )
  if (pricingPackage) {
    return pricingPackage
      ? math.round(
          (invoiceAmount * pricingPackage.merchant.percentage) / 100 +
            pricingPackage.merchant.amount,
          2,
        )
      : 0
  }
  return 0
}

const calculateCardFee = async (amount: number, accountId: string) => {
  const bankAccount = await BankAccount.findOne({
    'cardMetadata.accountId': accountId,
  })

  const fee = bankAccount
    ? getCardFee({
        cardNetwork: bankAccount.cardMetadata.network,
        cardType: bankAccount.cardMetadata.type,
        isRegulated: bankAccount.cardMetadata.isRegulated,
      })
    : 0

  return math.round(amount * (fee / 100), 2)
}

import React, { useEffect, useRef } from 'react'
import { createStackNavigator } from '@react-navigation/stack'
import { NotificationsModal } from './NotificationsModal'
import { dictionaries } from '@linqpal/models'
import { useObserver } from 'mobx-react-lite'
import { useStore } from '../../../store'
import { toJS } from 'mobx'
import moment from 'moment'
import { headerMode, screenOptions } from '../../../utils/helpers/commonUtils'
import { paths } from '../../links'
import Home from '../../Console/Home'
import { LoanApprovedAlert } from './LoanApprovedAlert'
import { VirtualCardReadyAlert } from './VirtualCardReadyAlert'
import { PaymentCollectionSuccessAlert } from './PaymentCollectionSuccessAlert'
import { Modal as MakeApaymentModal } from '../CreditTab/MakePayment/Modal'
import { PaymentMethodModals } from '../CreditTab/modals/PaymentMethodModals'
import { LoanPastDueModal } from '../CreditTab/modals'
import { PaymentSuccess } from '../CreditTab/modals/PaymentSuccess'
import { PayModal } from '../CreditTab/modals/PayModal'
import { AutoTradeCreditApprovedAlert } from './AutoTradeCreditApprovedAlert'
import { InHouseCreditAutoPayAlert } from './InHouseCreditAutoPayAlert'

interface IContextType {
  requiredActionNotifications: Array<any>
  modalNotifications: Array<any>
}

const INCLUDED_NOTIFICATION_TYPES = [
  dictionaries.notificationTypes.invoice,
  dictionaries.notificationTypes.quotePlaced,
  dictionaries.notificationTypes.quoteAuthorized,
  dictionaries.notificationTypes.quoteInvoiced,
  dictionaries.notificationTypes.quoteRejected,
  dictionaries.notificationTypes.quoteExpired,
  dictionaries.notificationTypes.quoteCanceled,
  dictionaries.notificationTypes.loan,
  dictionaries.notificationTypes.payment,
  dictionaries.notificationTypes.virtualcard,
  dictionaries.notificationTypes.autoTradeCreditApproved,
]

const RootStack = createStackNavigator()

export const BuilderHomeContext = React.createContext<IContextType>({
  requiredActionNotifications: [],
  modalNotifications: [],
})

export const BuilderHomeContextProvider = ({ children }) => {
  const {
    screensStore: { notificationStore, requiredActionStore },
  } = useStore()

  const raNotifications = useRef<Array<any>>([])
  const mNotifications = useRef<Array<any>>([])

  useEffect(() => {
    notificationStore.fetchNotifications()
    requiredActionStore.fetchRequiredActionItems()
  }, [notificationStore, requiredActionStore])

  useObserver(() => {
    const notifications = notificationStore.notifications
    const requiredActionItems = requiredActionStore.items

    const result = notifications
      .map((item) => toJS(item))
      .filter((notification) =>
        INCLUDED_NOTIFICATION_TYPES.some((el) => el === notification.type),
      )

    result.sort((a, b) => moment(b.createdAt).diff(moment(a.createdAt)))

    raNotifications.current = requiredActionItems
    mNotifications.current = result
  })

  return (
    <BuilderHomeContext.Provider
      value={{
        requiredActionNotifications: raNotifications.current,
        modalNotifications: mNotifications.current,
      }}
    >
      {children}
    </BuilderHomeContext.Provider>
  )
}

export default ({ navigation }) => {
  return (
    <BuilderHomeContextProvider>
      <RootStack.Navigator
        headerMode={headerMode}
        initialRouteName={paths.Home._self}
        mode="modal"
        screenOptions={screenOptions}
      >
        <RootStack.Screen name={paths.Home._self} component={Home} />
        <RootStack.Screen
          name={paths.Console.Notifications._self}
          component={NotificationsModal}
        />
      </RootStack.Navigator>{' '}
      <AutoTradeCreditApprovedAlert navigation={navigation} />
      <LoanApprovedAlert navigation={navigation} />
      <VirtualCardReadyAlert navigation={navigation} />
      <LoanPastDueModal />
      <PaymentCollectionSuccessAlert />
      <MakeApaymentModal />
      <PaymentMethodModals />
      <PaymentSuccess />
      <PayModal />
      <InHouseCreditAutoPayAlert />
    </BuilderHomeContextProvider>
  )
}

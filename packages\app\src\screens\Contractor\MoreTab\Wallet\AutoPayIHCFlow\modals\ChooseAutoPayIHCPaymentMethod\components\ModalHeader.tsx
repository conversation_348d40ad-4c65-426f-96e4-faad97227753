import React from 'react'
import { StyleSheet, TouchableOpacity, View } from 'react-native'
import { BtText, Spacer } from '@linqpal/components/src/ui'
import { ModalCloseIcon } from '../../../../../../../../assets/icons'
import useIsMobile from '../../../../../../PayablesTab/hooks/useIsMobile'
import { AutoPayIHCMode } from './ModalContent'

interface ModalHeaderProps {
  title: string
  subtitle?: string
  onClose?: () => void
  mode?: AutoPayIHCMode
}

export const ModalHeader: React.FC<ModalHeaderProps> = ({
  title,
  subtitle,
  mode = AutoPayIHCMode.Enable,
  onClose,
}) => {
  const isMobile = useIsMobile()

  return (
    <View style={isMobile ? styles.mobileContainer : styles.container}>
      <View style={[styles.header, isMobile && styles.mobileHeader]}>
        <View
          style={[
            styles.textContainer,
            isMobile && styles.mobileTextContainer,
            isMobile && mode !== AutoPayIHCMode.Enable && { marginTop: 10 },
          ]}
        >
          <BtText
            style={[
              styles.title,
              isMobile && styles.mobileTitle,
              isMobile && mode !== AutoPayIHCMode.Enable && { maxWidth: '90%' },
            ]}
          >
            {title}
          </BtText>
          <Spacer
            height={isMobile ? (mode === AutoPayIHCMode.Enable ? 15 : 30) : 10}
          />
          {subtitle && (
            <BtText
              style={[styles.subtitle, isMobile && styles.mobileSubtitle]}
            >
              {subtitle}
            </BtText>
          )}
        </View>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <ModalCloseIcon width={40} height={40} />
        </TouchableOpacity>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 14,
  },
  container: {
    maxWidth: 540,
  },
  textContainer: {
    flex: 1,
    maxWidth: '85%',
  },
  closeButton: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  title: {
    fontFamily: 'Inter',
    fontWeight: '700',
    fontSize: 24,
    lineHeight: 34,
    letterSpacing: -0.5,
    color: '#001929',
  },
  subtitle: {
    fontFamily: 'Inter',
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 24,
    color: '#001929',
    letterSpacing: -0.5,
  },

  mobileContainer: {
    width: '100%',
    marginLeft: -30,
  },
  mobileTextContainer: {
    maxWidth: '100%',
  },
  mobileTitle: {
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: -0.3,
    maxWidth: '85%',
  },
  mobileSubtitle: {
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0,
  },
  mobileHeader: {
    paddingHorizontal: 20,
  },
})

import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { Image, Text, View } from 'react-native'
import bankAccountHeaderImage from '../../../../../assets/images/builder2.0/LinkBankAccount.svg'
import { Spacer } from '../../../../../ui/atoms'
import { AppMaskedInput } from '../../../../../ui/molecules/AppMaskedInput'
import RadioButton from '@linqpal/common-frontend/src/ui/RadioButton'
import { dynamicSize, useResponsive } from '../../../../../utils/hooks'
import { editableModel, routes } from '@linqpal/models'
import { types } from 'mobx-state-tree'
import { toJS } from 'mobx'
import { useStore } from '../../../../../store'
import { Observer } from 'mobx-react'
import Wrapper from '../../../Wrapper'
import { BackHeader } from '../../../../../assets/icons'
import { AutoCompleteInput } from '@linqpal/common-frontend/src/ui/AutoComplete'
import { TOP_TAB_NAVIGATOR_HEIGHT } from '../BankCardListing/ViewAddedMethods'
import { GetIcon } from '../../../../../ui/atoms/GetIcon'
import { BButton } from '../../../../../ui/atoms/builder-2.0/Button'
import useFileInput from '../../../../../utils/hooks/useFileInput'
import { useInvoiceDetailsContext } from '../../../TabInvoice/InvoiceDetailsContext'
import { Toolbar } from '../../../../../ui/molecules/Layout'
import { AppInput } from '@linqpal/common-frontend/src/ui'
import { useTranslation } from 'react-i18next'
import ConnectBankError from '../ConnectBankError'
import AddPaymentMethodStore, {
  Source,
} from '../../../../../store/AddPaymentMethodStore'
import { paths } from '../../../../links'

const LinkBankAccountHeader = ({ navigation, routeParams }) => {
  const { t } = useTranslation('global')
  const {
    screensStore: { updateScreenMenuTop },
  } = useStore()
  const { sm } = useResponsive()

  const backBehavior = () => {
    if (navigation.canGoBack()) {
      if (routeParams?.ihcAutoPayFlow === 'true') {
        if (sm) {
          navigation.navigate(paths.Console.Accounts._self, {
            screen: paths.Console.More.ListMethods,
            params: { showAutoPayFlow: 'true' },
          })
        } else {
          navigation.navigate(paths.Console.More._self, {
            screen: paths.Console.More.Wallet,
            params: {
              screen: paths.Console.More.ListMethods,
              params: { showAutoPayFlow: 'true' },
            },
          })
        }
      } else {
        navigation.goBack()
      }
    } else {
      navigation.navigate(paths.Home._self)
    }
  }

  return (
    <Toolbar
      centerComponent={
        <Text style={{ fontSize: 16, fontWeight: '500', lineHeight: 24 }}>
          {t('Wallet.link_a_bank')}
        </Text>
      }
      leftComponent={
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <GetIcon
            icon={<BackHeader height="20" width="20" />}
            onPress={() => {
              updateScreenMenuTop(false)
              backBehavior()
            }}
          />
        </View>
      }
      containerStyle={{ width: 420 }}
      rightComponent={<View style={{ width: 30 }} />}
    />
  )
}

const BankAccountModel = types.compose(
  types
    .model({
      _id: '',
      name: '',
      accountholderName: '',
      routingNumber: '',
      accountNumber: '',
      isManualEntry: true,
      accountType: types.optional(
        types.enumeration(['savings', 'checking']),
        'checking',
      ),
      voidedCheck: '',
      isSending: false,
      isPrimary: false,
    })
    .actions((self) => ({
      startSending: () => (self.isSending = true),
      stopSending: () => (self.isSending = false),
    }))
    .views((self) => ({
      get canSave() {
        return (
          !!self.name &&
          !!self.accountholderName &&
          self.routingNumber.length === 9 &&
          self.accountNumber.length >= 6 &&
          !!self.accountType &&
          self.voidedCheck &&
          self.voidedCheck !== ''
        )
      },
    })),
  editableModel(),
)

export default ({ navigation, route, source }) => {
  const fromInvoiceLink = source === Source.INVOICE_LINK
  const routeParams = route?.params || {}
  const { setBankSuccess, setPayInvoiceModal, setInvoicePaymentMethod } =
    useInvoiceDetailsContext()
  const { addingFromInvoice } = AddPaymentMethodStore
  const [expanded, setExpanded] = useState(false)
  const [errorVisible, setErrorVisible] = useState(false)
  const [model, setModel] = useState(
    BankAccountModel.create(
      toJS({
        _id: '',
        name: '',
        accountholderName: '',
        routingNumber: '',
        accountNumber: '',
        isManualEntry: true,
        accountType: 'checking',
        isPrimary: false,
      }),
    ),
  )
  const withInvoice = useMemo(() => {
    return addingFromInvoice(source)
  }, [source, addingFromInvoice])

  // const [primary, setPrimary] = useState(false)
  const apiSearchFn = useCallback(async (input) => {
    const res = await routes.company.allInstitutions({
      search: input,
      limit: 10,
      onlyFinicity: 'false',
    })
    return res
  }, [])
  const {
    userStore,
    screensStore: { paymentMethodsStore, updateScreenMenuTop },
    remoteConfig,
  } = useStore()
  const { t } = useTranslation('global')
  const { sm } = useResponsive()

  useEffect(() => {
    updateScreenMenuTop(true)
  }, [updateScreenMenuTop])

  // useEffect(() => {
  //   model.setValue('isPrimary', primary)
  // }, [primary, model])

  const { FileInput } = useFileInput({
    label: t('Wallet.upload_bank_statements'),
    subLabel: t('Wallet.upload_bank_statements_extensions'),
    accept: [
      'application/pdf',
      'application/msword',
      'image/*',
      'application/zip',
    ],
    maxFiles: 2, // need to check
    disabled: false,
    model: model,
    name: 'voidedCheck',
    uploadCheck: true,
    documentName: '',
  })

  const onAction = () => {
    if (!model.isSending) {
      model.startSending()
      routes.company
        .addBankAccount(JSON.parse(JSON.stringify(model)))
        .then((response) => {
          if (response.result === 'ok') {
            userStore.setLocalSettings({ onBoarded: true })
            userStore.fetchUser()
            paymentMethodsStore.fetchPaymentMethods()
            setModel(BankAccountModel.create({}))
            model.stopSending()
            if (withInvoice) {
              setInvoicePaymentMethod({
                ...JSON.parse(JSON.stringify(model)),
                id: response.id,
                newlyAdded: true,
                paymentMethodType: 'bank',
              })
              setPayInvoiceModal(true)
              if (navigation.canGoBack()) {
                navigation.goBack()
              } else {
                navigation.navigate(paths.Home._self)
              }
            } else {
              setBankSuccess(true)
              if (navigation.canGoBack()) {
                if (routeParams.ihcAutoPayFlow === 'true') {
                  if (sm) {
                    navigation.navigate(paths.Console.Accounts._self, {
                      screen: paths.Console.More.ListMethods,
                      params: { showAutoPayFlow: 'true' },
                    })
                  } else {
                    navigation.navigate(paths.Console.More._self, {
                      screen: paths.Console.More.Wallet,
                      params: {
                        screen: paths.Console.More.ListMethods,
                        params: { showAutoPayFlow: 'true' },
                      },
                    })
                  }
                } else {
                  navigation.goBack()
                }
              } else {
                navigation.navigate(paths.Home._self)
              }
            }
            updateScreenMenuTop(false)
          }
        })
        .catch((e) => {
          model.stopSending()
          if (e.code === 'operation/denied') {
            setErrorVisible(true)
          }
        })
    }
  }
  return (
    <Wrapper
      containerStyle={
        fromInvoiceLink ? { width: '100%', alignItems: 'center' } : undefined
      }
      contentContainerStyle={{ paddingHorizontal: 0 }}
      toolbar={
        <LinkBankAccountHeader
          navigation={navigation}
          routeParams={routeParams}
        />
      }
    >
      <View
        style={{
          backgroundColor: '#F8F9F9',
          width: '100%',
          padding: 20,
          alignItems: 'center',
        }}
      >
        <Image
          source={{ uri: bankAccountHeaderImage }}
          resizeMode="contain"
          style={{ width: '100%', height: dynamicSize(200) }}
        />
      </View>

      <View
        style={{
          paddingHorizontal: dynamicSize(25),
          paddingVertical: dynamicSize(20),
          backgroundColor: 'white',
        }}
      >
        <View>{FileInput}</View>
        <View style={{ marginTop: 6 }}>
          <Text style={{ fontSize: 20, fontWeight: '500', color: '#3E4756' }}>
            {t('Wallet.account_type')}
          </Text>
        </View>
        <View
          style={{ display: 'flex', flexDirection: 'column', marginTop: 8 }}
        >
          <Observer>
            {() => (
              <>
                <RadioButton
                  label={t('Wallet.checking')}
                  checked={model.accountType === 'checking'}
                  onPress={() => model.setValue('accountType', 'checking')}
                  radioProps={{
                    uncheckedColor: '#99ADBA',
                    checkedColor: '#33B3F5',
                  }}
                />
                <RadioButton
                  label={t('Wallet.saving')}
                  checked={model.accountType === 'savings'}
                  onPress={() => model.setValue('accountType', 'savings')}
                  radioProps={{
                    uncheckedColor: '#99ADBA',
                    checkedColor: '#33B3F5',
                  }}
                />
              </>
            )}
          </Observer>
        </View>
        <Spacer height={24} />
        <AppInput
          label={t('Wallet.name_an_account')}
          model={model}
          name="accountholderName"
        />
        <Spacer height={24} />
        <AutoCompleteInput
          apiSearchFn={apiSearchFn}
          label={t('Wallet.search_bank')}
          value={model.name}
          displayKey="name"
          valueKey="name"
          onChangeText={(val) => model.setValue('name', val || '')}
          onSelection={(val) => {
            model.setValue('name', val?.name || '')
            if (val && val.isBlocked) {
              setErrorVisible(true)
            }
          }}
          setExpanded={setExpanded}
          expanded={expanded}
        />
        <Spacer height={24} />
        <AppMaskedInput
          label={t('Wallet.routing_number')}
          model={model}
          name="routingNumber"
          inputMask="*********"
          onChange={(val) => {
            const isBlocked = remoteConfig.checkIfRoutingNumberIsBlocked(val)
            if (isBlocked) {
              setErrorVisible(true)
            }
          }}
        />
        <Spacer height={24} />
        <AppMaskedInput
          label={t('Wallet.account_number')}
          model={model}
          name="accountNumber"
          inputLength={18}
          inputMask="*********99999999"
        />
        <Spacer height={24} />
        {/* <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <RadioButton
            checked={primary}
            onPress={() => {
              setPrimary(!primary)
            }}
            radioProps={{
              style: { borderRadius: 2 },
              innerStyle: { borderRadius: 2 },
              uncheckedColor: '#99ADBA',
              checkedColor: '#33B3F5',
            }}
          />
          <Text style={{ fontSize: 16 }}>
            {t('Wallet.set_primary_account')}
          </Text>
        </View> */}
        <Spacer height={24} />
        <Observer>
          {() => (
            <BButton
              block
              onPress={onAction}
              disabled={!model.canSave || model.isSending}
              buttonStyle={null}
            >
              {t('Wallet.agree')}
            </BButton>
          )}
        </Observer>
        <Spacer height={55 + TOP_TAB_NAVIGATOR_HEIGHT} />
      </View>
      <ConnectBankError
        visible={errorVisible}
        onClose={() => {
          model.setValue('name', '')
          model.setValue('routingNumber', '')
          setErrorVisible(false)
        }}
      />
    </Wrapper>
  )
}

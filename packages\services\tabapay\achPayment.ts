import csv from 'csvtojson'
import {
  ACH_TRANSACTION_TYPE,
  AwsService,
  connectToDatabase,
  emailService,
  LedgerService,
  LMS,
  LoanApplication,
  Logger,
  Operation,
  Transaction,
} from '@linqpal/common-backend'
import mongoose, { ClientSession } from 'mongoose'
import { dictionaries, exceptions } from '@linqpal/models'
import { internal as achInternal } from '@linqpal/common-backend/src/services/cbw/ach.internal.controller'
import { LoanServicingAccounts } from '@linqpal/common-backend/src/services/loan'
import moment from 'moment-timezone'
import { S3EventRecord } from 'aws-lambda'
import {
  IOperation,
  ITransaction,
} from '@linqpal/common-backend/src/models/types'
import xlsx from 'xlsx'
import EmailBuilder from '@linqpal/common-backend/src/helpers/EmailBuilder'
import { Slack } from '@linqpal/common-backend/src/services/slack.service'
import { toCurrency } from '@linqpal/models/src/helpers/formatter'
import { logPromiseErrors } from '@linqpal/common-backend/src/helpers/logging'

const logger = new Logger({
  module: 'ACH Payment service',
})

export const achPayment = async (record: S3EventRecord) => {
  logger.info({ record }, 'Received S3 event')

  await connectToDatabase()

  if (
    record.s3.object.key.includes(
      process.env.LP_TABAPAY_CHARGEBACK_FILE_SUFFIX!,
    )
  ) {
    await processChargebacks(record.s3.bucket.name, record.s3.object.key)
    return { result: 'ok' }
  }

  // check to process transactions file, exclude chargebacks, exceptions, interchange and summary files
  if (
    !record.s3.object.key.includes(
      process.env.LP_TABAPAY_TRANSACTIONS_FILE_SUFFIX!,
    )
  ) {
    logger.warn(
      `${record.s3.object.key} is not transactions report. skipping it`,
    )
    return { result: 'ok' }
  }

  logger.info(`processing report ${record.s3.object.key}`)

  // read tabapay's transaction report from S3 and convert to json
  const dailyTransactionReportJson = await readCsv(
    record.s3.bucket.name,
    record.s3.object.key,
  )

  for (const transactionRecord of dailyTransactionReportJson) {
    const transactionId = transactionRecord['Transaction ID']
    logger.info(
      { transactionRecord },
      `processing transaction ${transactionId}`,
    )

    // process only transactions of type 'Purchase' and status 'Complete'
    if (
      transactionRecord.Type !== 'Purchase' ||
      transactionRecord.Status !== 'Complete'
    ) {
      logger.warn(
        { type: transactionRecord.Type, status: transactionRecord.Status },
        `skipping transaction ${transactionId}`,
      )
      continue
    }

    // get supplier company from DB
    const session = await mongoose.startSession()
    await session.startTransaction()
    try {
      const transactions = await Transaction.find({
        'metadata.transactionID': transactionId,
      }).session(session)

      // prettier-ignore
      logger.info(`found ${transactions?.length} related transactions for ${transactionId}`)

      for (const transaction of transactions) {
        const operation = await Operation.findById(
          transaction.operation_id,
        ).session(session)

        if (!operation) {
          throw new exceptions.LogicalError('Operation does not exist')
        }

        logger.info({ operation }, `processing operation ${operation.id}`)

        if (operation.type === dictionaries.OPERATION_TYPES.LOAN.REPAYMENT) {
          await performInternalTransfer(
            transactionRecord,
            transaction,
            operation,
            session,
          )
        } else {
          operation.metadata.pullResult = moment().toDate()
          operation.markModified('metadata')

          logger.info({ operation }, 'adding pullResult to operation')

          await operation.save()
          await LedgerService.handleCardPullSuccess(transaction)
        }
      }

      await session.commitTransaction()
    } catch (err) {
      console.log(
        `ERROR: skipping transaction id ${transactionRecord['Transaction ID']}`,
      )
      console.log(err)
      await session.abortTransaction()
      continue
    }

    await session.endSession()
  }

  if (dailyTransactionReportJson?.length) {
    try {
      logger.info('sending tabapay report to accounting team')
      await notifyAccountingTeam(dailyTransactionReportJson)
    } catch (err) {
      console.log(
        'ERROR: something went wrong when sending tabapay report to accounting team',
      )
      console.log(err)
    }
  }
  return { result: 'ok' }
}

const readCsv = async (bucketName: string, fileName: string) => {
  console.log('retrieving file from S3')

  let csvFile: any[]

  try {
    const file = await AwsService.getS3File(bucketName, fileName)
    csvFile = await csv().fromString(file)
  } catch (err) {
    logger.error({ bucketName, fileName, err }, 'Unable to read S3 file')
    return []
  }

  console.log('successfully retrieved file from S3')

  return csvFile
}

const notifyAccountingTeam = async (dailyTransactionReportJson: any[]) => {
  const attachments: any[] = []
  const dt = moment().format('YYYYMMDD')
  const emailMessage = EmailBuilder.getSubjectAndBody({
    key: 'sendTabapayReport',
  })

  const xls_final = xlsx.utils.json_to_sheet(dailyTransactionReportJson)
  const name_final = `TABAPAY_REPORT_${dt}.xlsx`
  const book_final = xlsx.utils.book_new()
  xlsx.utils.book_append_sheet(book_final, xls_final)

  const buff_final = xlsx.write(book_final, {
    type: 'base64',
    bookType: 'xlsx',
  })

  attachments.push({ filename: name_final, content: buff_final })

  await emailService.send({
    to: '<EMAIL>',
    attachments,
    html: emailMessage.body,
    subject: emailMessage.subject,
  })
}

const performInternalTransfer = async (
  transactionRecord: any,
  transaction: ITransaction,
  operation: IOperation,
  session: ClientSession,
) => {
  logger.info(
    { transactionRecord, transaction, operation },
    `processing internal transfer for transaction ${transaction.id}, operation ${operation.id}`,
  )

  // check if ACH INTERNAL has already been created
  if (transaction.metadata.isAchInternalCreated === true) {
    logger.warn(
      {
        transaction,
        operation,
      },
      `ACH INTERNAL has already been created for transaction ${transaction.id}, skipping it.`,
    )
    throw new exceptions.LogicalError(
      'ACH INTERNAL has already been created for transaction',
    )
  }

  const app = await LoanApplication.findById(operation.owner_id)
  if (!app) throw new exceptions.LogicalError('Loan application not found')

  logger.info(`found app ${app.id}, funding source is ${app.fundingSource}`)

  const accounts = LoanServicingAccounts[app.fundingSource || 'cbw']
  const accountId =
    process.env.LP_MODE === 'prod' ? accounts.prod : accounts.nonProd

  await achInternal(
    operation,
    {
      sender: process.env.LP_TABAPAY_ACH_IDENTIFICATION,
      recipient: accountId.COLLECTION,
      reason: 'Transfer from Tabapay account to servicing account',
      transactionType: dictionaries.TRANSACTION_TYPES.ACH.INTERNAL_TRANSFER,
      achTransactionType: ACH_TRANSACTION_TYPE.INTERNAL,
    },
    session,
  )

  // update transaction to capture ach internal created status
  transaction.metadata.isAchInternalCreated = true
  transaction.metadata.tabapayBin = transactionRecord.BIN
  transaction.markModified('metadata')

  // prettier-ignore
  logger.info({transaction}, `updating metadata of transaction ${transaction.id}`)

  await transaction.save({ session: session })

  if (operation.metadata.lms_paymentId) {
    // prettier-ignore
    logger.info({operation}, `approving LMS payment for operation ${operation.id}`)

    await LMS.approvePayment(operation.metadata.lms_paymentId)
  }
}

const processChargebacks = async (bucket: string, fileName: string) => {
  const excludedTransactions = [
    'yx4IbdIFQYvqotB9K05WwQ',
    'j8Y7hqdEMaNwQRiAsTNnTA',
  ]

  const originalChargebacks = await readCsv(bucket, fileName)

  const chargebacks = originalChargebacks.filter(
    (cb) => !excludedTransactions.includes(cb['Original Transaction ID']),
  )

  if (!chargebacks?.length) {
    logger.info('no chargebacks found')
    return
  }

  logger.info(`processing chargeback file ${bucket}/${fileName}`)

  let message = 'Tabapay Chargeback: \n\n'

  for (const chargeback of chargebacks) {
    const transactionId = chargeback['Original Transaction ID']

    const transaction = await Transaction.aggregate([
      { $match: { 'metadata.transactionID': transactionId } },
      {
        $lookup: {
          from: 'companies',
          let: {
            payerId: {
              $convert: {
                input: '$payer_id',
                to: 'objectId',
                onError: null,
                onNull: null,
              },
            },
          },
          pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$payerId'] } } }],
          as: 'company',
        },
      },
      {
        $unwind: {
          path: '$company',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'operations',
          let: {
            operationId: {
              $convert: {
                input: '$operation_id',
                to: 'objectId',
                onError: null,
                onNull: null,
              },
            },
          },
          pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$operationId'] } } }],
          as: 'operation',
        },
      },
      {
        $unwind: {
          path: '$operation',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'invoices',
          let: {
            invoiceId: {
              $convert: {
                input: '$operation.owner_id',
                to: 'objectId',
                onError: null,
                onNull: null,
              },
            },
          },
          pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$invoiceId'] } } }],
          as: 'invoice',
        },
      },
      {
        $unwind: {
          path: '$invoice',
          preserveNullAndEmptyArrays: true,
        },
      },
    ]).exec()

    const companyName =
      transaction[0]?.company?.legalName || transaction[0]?.company?.name || ''
    const invoiceNumber = transaction[0]?.invoice?.invoice_number || ''

    message += `Transaction ID: ${transactionId}\n`
    message += `Description: ${chargeback['Exception Description']}\n`
    message += `Action Status: ${chargeback['Action-Status']}\n`
    message += `Amount: ${toCurrency(chargeback['Original Settled Amount'])}\n`
    message += `Payer Company Name: ${companyName}\n`
    message += `Invoice Number: ${invoiceNumber}\n`
    message += '\n\n'
  }

  const emailMessage = message.replace(/\n/g, '<br>')
  const email =
    process.env.LP_MODE === 'prod'
      ? emailService.EMAILS.INVOICE_PROCESSING
      : emailService.EMAILS.PRODUCT_OPERATION_TEAM_TEST

  const results = await Promise.allSettled([
    Slack.notifyError(
      'achPayment',
      `${bucket}/${fileName}`,
      message,
      Slack.Channels.Backoffice,
    ),

    emailService.sendTo(email, {
      subject: `Tabapay Chargeback ${moment().format('MM/DD/YYYY HH:mm:ss')}`,
      html: `<div>${emailMessage}</div>`,
    }),
  ])

  logPromiseErrors(results, logger)
}

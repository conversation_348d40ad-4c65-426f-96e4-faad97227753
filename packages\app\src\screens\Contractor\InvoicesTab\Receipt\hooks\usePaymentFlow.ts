import { useCallback } from 'react'
import { paths } from '../../../../links'
import { InvoicePaymentType } from '@linqpal/models'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'

export enum FLOWS {
  CHOOSE_PAYMENT_METHOD,
  PAY_WITH_BANK_ACCOUNT_FLOW,
  PAY_WITH_CARD_FLOW,
  ADD_NEW_BANK_ACCOUNT,
  CONNECT_NEW_BANK_ACCOUNT,
  ADD_NEW_CREDIT_CARD,
  ADD_NEW_PAYMENT_METHOD,
  PAY_IHC_INVOICE,
}

export interface IPaymentMethod {
  id: string
  paymentMethodType: string
  name: string
  accountNumber: string
  accountType: string
  cardMetadata?: any
}

export const usePaymentFlow = (
  setFlow: (flow: FLOWS) => void,
  setPaymentMethod: (method: IPaymentMethod | null) => void,
  refetch: () => void,
  navigation: any,
  invoice: any,
  onClose: () => void,
) => {
  const handleAddNewBankAccount = useCallback(() => {
    setFlow(FLOWS.ADD_NEW_BANK_ACCOUNT)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleAddNewCreditCard = useCallback(() => {
    setFlow(FLOWS.ADD_NEW_CREDIT_CARD)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleAddNewPaymentMethod = useCallback(() => {
    setFlow(FLOWS.ADD_NEW_PAYMENT_METHOD)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handlePayWithBankOrCard = useCallback(
    (selectedPaymentMethod: IPaymentMethod) => {
      setPaymentMethod(selectedPaymentMethod)
      if (
        invoice.paymentDetails?.paymentType === InvoicePaymentType.FACTORING &&
        invoice.paymentDetails?.arAdvanceStatus === ArAdvanceStatus.Approved
      ) {
        setFlow(FLOWS.PAY_IHC_INVOICE)
        return
      }
      switch (selectedPaymentMethod.paymentMethodType) {
        case 'card':
          setFlow(FLOWS.PAY_WITH_CARD_FLOW)
          break
        case 'bank':
          setFlow(FLOWS.PAY_WITH_BANK_ACCOUNT_FLOW)
          break
        default:
          setFlow(FLOWS.CHOOSE_PAYMENT_METHOD)
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  )

  const handleChoosePaymentMethod = useCallback(() => {
    setFlow(FLOWS.CHOOSE_PAYMENT_METHOD)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleResetFlow = useCallback(() => {
    setFlow(FLOWS.CHOOSE_PAYMENT_METHOD)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleNewPaymentMethodAdded = useCallback(async () => {
    setFlow(FLOWS.CHOOSE_PAYMENT_METHOD)
    refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetch])

  const handleFinishPaymentMethodAdded = useCallback(() => {
    setFlow(FLOWS.CHOOSE_PAYMENT_METHOD)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleLinkBankManually = useCallback(() => {
    onClose()
    navigation.navigate(paths.LinkBank, {
      id: invoice._id,
    })
  }, [navigation, invoice, onClose])

  return {
    handleAddNewBankAccount,
    handleAddNewCreditCard,
    handleAddNewPaymentMethod,
    handlePayWithBankOrCard,
    handleResetFlow,
    handleNewPaymentMethodAdded,
    handleLinkBankManually,
    handleChoosePaymentMethod,
    handleFinishPaymentMethodAdded,
  }
}

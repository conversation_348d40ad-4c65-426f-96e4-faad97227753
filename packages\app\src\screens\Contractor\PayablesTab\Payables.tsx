import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react-lite'
import { TabNames } from './enums'
import { Vendors } from './Store/VendorsStore'
import { InvoicesList } from './Tabs/Invoices/Invoices'
import { QuotesList } from './Tabs/Quotes/Quotes'
import { VendorsList } from './Tabs/Vendors/Vendors'
import { EmptyResult } from './Components/EmptyResult'
import { InfoInCircle } from '../../../assets/icons'
import { TabNavigatorStyles } from './commonStyles'
import {
  TradeCreditOverview,
  ValueKind,
} from '../../TradeCredit/Components/TradeCreditOverview'
import { View } from 'react-native'
import { TabPayablesNavigator } from './Components/TabPayablesNavigator'
import { ScreenScrollWrapper } from './Components/ScreenScrollWrapper'
import { useNavigation } from '@react-navigation/core'
import { paths } from '../../links'
import AutoPayIHCWarning from '../../../ui/organisms/AutoPayIHCWarning/AutoPayIHCWarning'
import { AutoPayIHCFlow } from '../MoreTab/Wallet/AutoPayIHCFlow'
import {
  useAutoPayIHCReducer,
  AutoPayIHCActionType,
} from '../MoreTab/Wallet/AutoPayIHCFlow/useAutoPayIHCReducer'

export const Payables = observer(() => {
  const { t } = useTranslation('payables')
  const navigation = useNavigation()
  const [autoPayIHCState, dispatchAutoPayIHC] = useAutoPayIHCReducer()

  const store = Vendors

  useEffect(() => {
    if (
      store.dataLoaded &&
      store.vendorsLength &&
      (!store.invoicesPayables.dataLoaded ||
        !store.quotesPayables.dataLoaded) &&
      store.vendorsLength !== 1
    ) {
      store.refreshData()
      store.refreshQuotesData()
    }
  }, [store.dataLoaded, store.vendorsLength, store])

  useEffect(() => {
    if (store.dataLoaded && store.vendorsLength === 1) {
      navigation.navigate(paths.Console.Payables.Vendor)
    }
  }, [store.vendorsLength, navigation, store.dataLoaded])

  /*const onPaymentSuccess = () => {
    store.refreshData()
    store.refreshQuotesData()
  }*/

  const Tabs = {
    invoices: {
      name: TabNames.InvoicesTab,
      options: {
        title: t('payables.tabs.invoices'),
      },
      component: observer(() => (
        <ScreenScrollWrapper>
          <InvoicesList store={store} />
        </ScreenScrollWrapper>
      )),
    },
    quotes: {
      name: TabNames.QuotesTab,
      options: {
        title: t('payables.tabs.quotes'),
      },
      component: observer(() => (
        <ScreenScrollWrapper>
          <QuotesList store={store} />
        </ScreenScrollWrapper>
      )),
    },
    vendors: {
      name: TabNames.VendorsTab,
      options: {
        title: t('payables.tabs.vendors'),
      },
      component: observer(() => (
        <ScreenScrollWrapper>
          <VendorsList />
        </ScreenScrollWrapper>
      )),
    },
  }

  if (!store.vendorsLength) {
    return (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: 120,
        }}
      >
        <EmptyResult
          header={t('payables.emptyResult.header')}
          text={t('payables.emptyResult.text')}
          icon={<InfoInCircle />}
        />
      </View>
    )
  }

  return (
    <>
      <View
        style={{
          width: 740,
          maxWidth: '65%',
          minWidth: '60%',
          justifyContent: 'flex-start',
          marginLeft: 36,
        }}
      >
        <TradeCreditOverview
          items={[
            {
              label: t('payables.header.totalBalanceDue'),
              value: store.vendorsStatistics.totalDueSum,
              isLoading: store.isStatisticsLoading,
            },
            {
              label: t('payables.header.numberOfDueInvoices'),
              value: store.vendorsStatistics.totalDueInvoices,
              isLoading: store.isStatisticsLoading,
              valueKind: ValueKind.Text,
            },
          ]}
        />
      </View>{' '}
      {autoPayIHCState.showAutoPayWarning && (
        <View
          style={[
            TabNavigatorStyles.style,
            { marginTop: -20, marginBottom: 30 },
          ]}
        >
          <AutoPayIHCWarning
            key={autoPayIHCState.refreshKey}
            onActionPress={() => {
              dispatchAutoPayIHC({
                type: AutoPayIHCActionType.SHOW_IHC_SETUP_FLOW,
              })
            }}
            onClose={() =>
              dispatchAutoPayIHC({ type: AutoPayIHCActionType.HIDE_WARNING })
            }
            isAutoPayIhcFlowOpened={autoPayIHCState.showAutoPayFlow}
          />
        </View>
      )}
      <TabPayablesNavigator
        initialTab={TabNames.InvoicesTab}
        styles={TabNavigatorStyles}
        tabs={[Tabs.invoices, Tabs.quotes, Tabs.vendors]}
      />
      {autoPayIHCState.showAutoPayFlow && (
        <AutoPayIHCFlow
          onClose={() =>
            dispatchAutoPayIHC({
              type: AutoPayIHCActionType.HIDE_IHC_SETUP_FLOW,
            })
          }
          navigation={navigation}
          onSuccess={() =>
            dispatchAutoPayIHC({
              type: AutoPayIHCActionType.AUTO_PAY_IHC_SETUP_SUCCESS,
            })
          }
        />
      )}
    </>
  )
})

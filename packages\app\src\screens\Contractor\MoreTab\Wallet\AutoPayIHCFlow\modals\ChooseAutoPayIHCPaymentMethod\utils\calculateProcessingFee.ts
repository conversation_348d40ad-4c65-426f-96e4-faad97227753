import { BankAccountPaymentMethods } from '@linqpal/models/src/dictionaries'
import { getCardFee } from '@linqpal/models/src/helpers/getCardFee'

/**
 * Calculates the processing fee percentage for a given payment method
 * @param selectedPaymentMethod - The selected payment method object
 * @returns Processing fee percentage as a string (e.g., "3.5", "0")
 */
export const calculateProcessingFee = (selectedPaymentMethod: any): string => {
  if (
    !selectedPaymentMethod ||
    selectedPaymentMethod.paymentMethodType === BankAccountPaymentMethods.Bank
  ) {
    return ''
  }

  const cardFeePercentage = getCardFee({
    cardNetwork: selectedPaymentMethod.cardMetadata?.network || '',
    cardType: selectedPaymentMethod.cardMetadata?.type || '',
    isRegulated: selectedPaymentMethod.cardMetadata?.isRegulated || false,
  })

  if (!cardFeePercentage) return ''

  return cardFeePercentage.toString()
}

import React, { useEffect, useMemo, useState } from 'react'
import { Text } from 'react-native'
import { dictionaries } from '@linqpal/models'
import { Divider } from 'react-native-paper'

import Loading from '../../Loading'
import { Spacer } from '../../../ui/atoms'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import Wrapper from '../../Contractor/Wrapper'
import {
  BankAccountPaymentMethods,
  IntegrationErrorCodesType,
  IntegrationRedirectStatusesType,
  LOAN_APPLICATION_STATUS,
  LoanApplicationStatuses,
  unexpectedErrorCode,
} from '@linqpal/models/src/dictionaries'
import {
  APPROVED_STATUSES,
  PROCESSING_STATUSES,
} from '@linqpal/models/src/dictionaries'
import { CompanyHeader_v2 } from '../InvoiceDetails/components/CompanyHeader'
import { OtherInvoiceInfo_v2 } from '../InvoiceDetails/components/OtherInvoiceInfo'
import { ApplyBTCreditItem_v2 } from '../InvoiceDetails/components/ApplyBTCreditItem'
import { AddNewPaymentMethodModal } from '../InvoiceDetails/components/AddNewPaymentMethodModal'
import { ConnectBankModal } from '../InvoiceDetails/components/ConnectBankModal'
import { AddNewBankAccountFlow } from '../InvoiceDetails/flows/AddNewBankAccountFlow'
import { PayWithBluetapeCreditFlow } from '../InvoiceDetails/flows/PayWithBluetapeCreditFlow'
import { PayWithBankAccountFlow } from '../InvoiceDetails/flows/PayWithBankAccountFlow'
import { PayWithCardFlow } from '../InvoiceDetails/flows/PayWithCreditCardFlow'
import { AddNewCreditCardFlow } from '../InvoiceDetails/flows/AddNewCreditCardFlow'
import { PaymentMethodItem } from '../../Contractor/InvoicesTab/Invoice/components'
import { AddNewButton } from '../../../ui/add-payment-method-components'
import { ScreenWrapper } from '../InvoiceDetails/components/ScreenWrapper'
import { LogOutButton } from '../InvoiceDetails/components/TopBarButtons'
import { InvoicePaymentProcessingInfo } from '../InvoiceDetails/components/InvoicePaymentProcessingInfo'
import { InvoiceIsPaidInfo } from '../InvoiceDetails/components/InvoiceIsPaidInfo'
import { PAGE_MODE } from '../InvoiceDetails/utils/pageMode'
import { EVENT_TYPE } from '../InvoiceDetails/utils/eventTypes'
import { useGetPaymentPlans } from './hooks/useGetPaymentPlans'
import { styles } from '../InvoiceDetails/styles'
import { IntegrationPaymentErrorModal } from './components/IntegrationErrorModal'
import { useGetUserInfo } from '../InvoiceDetails/hooks/useGetUserInfo'
import { useGetPaymentMethods } from '../InvoiceDetails/hooks/useGetPaymentMethods'
import { useGetUserProjects } from '../InvoiceDetails/hooks/useGetUserProjects'
import { useGetInvoice } from './hooks/useGetInvoice'
import { FLOWS } from '../InvoiceDetails/utils/flows'
import { usePayWithBlueTapeCredit } from './hooks/usePayWithBlueTapeCredit'
import {
  useHandleAchCardPaymentSuccess,
  useHandleBtcPaymentSuccess,
  useHandlePaymentFailure,
} from './hooks/usePaymentStatusHandlers'
import { usePaymentFlow } from '../InvoiceDetails/hooks/usePaymentFlows'
import { PaymentMethod } from '../../Contractor/InvoicesTab/Invoice/InvoiceDetails'
import { determinePaymentStatus } from './helpers/determinePaymentStatus'
import { FooterRedirectBackButton } from './components/FooterRedirectBackButton'

interface JwtInvoiceDetailsProps {
  invoice: any
  minBnplAmount: number
  onLogOut: () => void
  onRedirectBack: (status: IntegrationRedirectStatusesType) => Promise<void>
  isWeb: boolean
  mode?: PAGE_MODE
  redirectUrl?: string
  invoicePaymentStatus: string | null
  invoiceCreditStatus: string | null
  isInvoicePaid: boolean
  jwtToken: string
}

export const JwtInvoiceDetails: React.FC<JwtInvoiceDetailsProps> = observer(
  ({
    invoice,
    minBnplAmount = 5,
    onLogOut,
    isWeb,
    mode,
    invoicePaymentStatus,
    invoiceCreditStatus,
    isInvoicePaid,
    onRedirectBack,
    jwtToken,
  }) => {
    const { t } = useTranslation('global')
    const [flow, setFlow] = useState<FLOWS>(FLOWS.NONE)
    const [popupWindow, setPopupWindow] = useState<Window | null>(null)
    const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(
      null,
    )
    const [errorCode, setErrorCode] =
      useState<null | IntegrationErrorCodesType>(null)

    let windowFeatures: string | undefined
    if (mode && mode === PAGE_MODE.POPUP)
      windowFeatures = 'width=800,height=600,scrollbars=yes,resizable=yes'

    const { invoiceMetadata, loading: getInvoiceLoading } = invoice?.id
      ? useGetInvoice(invoice.id, setErrorCode)
      : { invoiceMetadata: null, loading: false }

    const { userInfo, loading: getUserInfoLoading } =
      useGetUserInfo(setErrorCode)

    const {
      paymentMethods,
      refetch,
      loading: getPaymentMethodsLoading,
    } = useGetPaymentMethods(setErrorCode)

    const { userProjects, loading: getUserProjectLoading } =
      useGetUserProjects(setErrorCode)

    const { paymentPlans, loading: getPaymentPlansLoading } =
      useGetPaymentPlans(invoice, setErrorCode)

    const loading = useMemo(
      () =>
        getUserInfoLoading ||
        getPaymentMethodsLoading ||
        getUserProjectLoading ||
        getPaymentPlansLoading ||
        getInvoiceLoading,
      [
        getUserInfoLoading,
        getPaymentMethodsLoading,
        getUserProjectLoading,
        getPaymentPlansLoading,
        getInvoiceLoading,
      ],
    )
    const handlePayWithBlueTapeCredit = usePayWithBlueTapeCredit(
      userInfo,
      paymentMethods,
      windowFeatures,
      setPopupWindow,
      setFlow,
      setPaymentMethod,
      setErrorCode,
    )

    const expectedOrigin = useMemo(() => window.location.origin, [])

    useEffect(() => {
      const handleMessage = (event: any) => {
        if (event.origin === expectedOrigin) {
          if (
            event.data.type === EVENT_TYPE.CREDIT_APPLICATION_SUBMITTED &&
            popupWindow
          ) {
            popupWindow.close()
            setPopupWindow(null)
            window.location.reload()
          }
        } else {
          console.warn('Received message from unexpected origin:', event.origin)
        }
      }
      window.addEventListener('message', handleMessage)
      return () => {
        window.removeEventListener('message', handleMessage)
      }
    }, [popupWindow, expectedOrigin])

    const handleAchCardPaymentSuccess =
      useHandleAchCardPaymentSuccess(onRedirectBack)
    const handleBtcPaymentSuccess = useHandleBtcPaymentSuccess(onRedirectBack)
    const handlePaymentFailure = useHandlePaymentFailure(onRedirectBack)

    const {
      handleAddNewBankAccount,
      handleAddNewCreditCard,
      handleAddNewPaymentMethod,
      handlePayWithBankOrCard,
      handleResetFlow,
      handleNewPaymentMethodAdded,
      handlePrimaryForCreditChanged,
      handleLinkBankManually,
      handleFinishPaymentMethodAdded,
    } = usePaymentFlow(setFlow, setPaymentMethod, setErrorCode, refetch)

    const email = userInfo?.user?.email
      ? userInfo.user.email
      : userInfo?.user?.login
      ? userInfo.user.login
      : ''

    if (loading) {
      return <Loading />
    }

    if (!invoice || errorCode)
      return (
        <IntegrationPaymentErrorModal
          errorCode={errorCode ?? unexpectedErrorCode}
          onClose={(status: IntegrationRedirectStatusesType) => {
            onRedirectBack(status)
          }}
        />
      )

    const isInvoiceEligible =
      invoice.company.bnplPaymentAvailable &&
      invoiceMetadata?.loanApplicationStatus !==
        LOAN_APPLICATION_STATUS.REJECTED &&
      invoiceMetadata?.currentInvoiceCreditStatus !==
        LOAN_APPLICATION_STATUS.REJECTED &&
      !!paymentPlans.length

    const canAddNewPaymentMethod =
      invoiceMetadata?.acceptAchPayment ||
      invoice.company.acceptAchPayment ||
      invoice.company.cardPaymentAvailable

    const isUserInvoiceSupplier = userInfo?.company?._id === invoice.company?.id

    const shouldShowChooseMethod =
      (canAddNewPaymentMethod || isInvoiceEligible) &&
      !isInvoicePaid &&
      !isUserInvoiceSupplier

    const filteredPaymentMethods = paymentMethods.filter((acc) => {
      if (
        acc.paymentMethodType === BankAccountPaymentMethods.Bank &&
        !(
          invoiceMetadata?.acceptAchPayment || invoice.company?.acceptAchPayment
        )
      ) {
        return false
      }
      if (
        acc.paymentMethodType !== BankAccountPaymentMethods.Bank &&
        !invoice.company?.cardPaymentAvailable
      ) {
        return false
      }
      return true
    })

    const isAmountLessThanMin = Number(invoice.total_amount) < minBnplAmount
    const isInvoiceCreditExist = [
      ...PROCESSING_STATUSES,
      ...APPROVED_STATUSES,
    ].includes(invoiceCreditStatus as LoanApplicationStatuses)

    const renderFooterButton = () => {
      if (isInvoicePaid) {
        const paymentStatus = determinePaymentStatus(
          invoicePaymentStatus,
          invoiceCreditStatus,
        )
        return (
          <FooterRedirectBackButton
            onPress={() => onRedirectBack(paymentStatus)}
            styles={{
              paddingHorizontal: 24,
              paddingVertical: 32,
              justifyContent: 'center',
            }}
          />
        )
      }
      return null
    }

    return (
      <ScreenWrapper
        email={email}
        isWeb={isWeb}
        button={<LogOutButton onLogOut={onLogOut} />}
      >
        <Wrapper
          containerStyle={{ alignSelf: 'center' }}
          contentContainerStyle={{ height: '100%' }}
          footer={renderFooterButton()}
        >
          <CompanyHeader_v2
            name={invoice.company?.name || ''}
            phone={invoice.company?.phone || ''}
            totalAmount={Number(invoice.total_amount)}
            style={{
              paddingVertical: 20,
              flexDirection: 'row',
            }}
          />

          <Divider style={{ marginBottom: 14 }} />
          <OtherInvoiceInfo_v2
            type={invoice.type}
            invoice_number={invoice.invoice_number}
            invoice_due_date={invoice.invoice_due_date}
          />
          {((isInvoicePaid &&
            invoicePaymentStatus === dictionaries.OPERATION_STATUS.SUCCESS) ||
            invoiceCreditStatus === LOAN_APPLICATION_STATUS.CLOSED) && (
            <InvoiceIsPaidInfo isWeb={isWeb} />
          )}
          {((isInvoiceCreditExist &&
            invoiceCreditStatus !== LOAN_APPLICATION_STATUS.CLOSED) ||
            (isInvoicePaid &&
              invoicePaymentStatus ===
                dictionaries.OPERATION_STATUS.PROCESSING)) && (
            <InvoicePaymentProcessingInfo isWeb={isWeb} />
          )}

          {shouldShowChooseMethod && (
            <Text style={styles.chooseMethodText}>
              {t('InvoiceDetails.choose-method')}
            </Text>
          )}
          {isInvoiceEligible && !isInvoicePaid && !isUserInvoiceSupplier && (
            <>
              <ApplyBTCreditItem_v2
                currentInvoiceCreditStatus={invoiceCreditStatus}
                amountLessThanMin={Number(invoice.total_amount) < minBnplAmount}
                onPress={handlePayWithBlueTapeCredit}
                showInfo
                disabled={isInvoiceCreditExist || isAmountLessThanMin}
              />
              <Spacer height={23} />
              {/* {!isBTCreditDisabled && <OrDivider />} */}
            </>
          )}

          {!isInvoicePaid && canAddNewPaymentMethod && !isUserInvoiceSupplier && (
            <>
              {filteredPaymentMethods.map((acc, i) => (
                <PaymentMethodItem
                  key={i}
                  item={acc}
                  disabled={false}
                  onPaymentSelection={handlePayWithBankOrCard}
                />
              ))}

              <AddNewButton
                onPress={handleAddNewPaymentMethod}
                disabled={false}
              />

              <Spacer height={43} />
            </>
          )}

          {flow === FLOWS.ADD_NEW_PAYMENT_METHOD && (
            <AddNewPaymentMethodModal
              cardPaymentAvailable={invoice.company.cardPaymentAvailable}
              acceptAchPayment={
                invoiceMetadata?.acceptAchPayment ||
                invoice.company.acceptAchPayment
              }
              onLinkCard={handleAddNewCreditCard}
              onConnectBank={handleAddNewBankAccount}
              onClose={handleResetFlow}
            />
          )}
          {flow === FLOWS.ADD_NEW_BANK_ACOUNT && (
            <AddNewBankAccountFlow
              onClose={handleResetFlow}
              onSuccess={handleNewPaymentMethodAdded}
              onLinkBankAccountManually={handleLinkBankManually}
              onFinish={handleFinishPaymentMethodAdded}
            />
          )}
          {flow === FLOWS.ADD_NEW_CREDIT_CARD && (
            <AddNewCreditCardFlow
              onClose={handleResetFlow}
              onSuccess={handleNewPaymentMethodAdded}
              invoiceId={invoice.id}
            />
          )}
          {flow === FLOWS.PAY_WITH_BLUETAPE_CREDIT_FLOW &&
            paymentMethod &&
            invoice &&
            userProjects &&
            userInfo &&
            paymentPlans && (
              <PayWithBluetapeCreditFlow
                invoiceId={invoice.id}
                invoice={invoice}
                jwtToken={jwtToken}
                totalAmount={Number(invoice.total_amount)}
                companyName={invoice.company.name}
                paymentMethodName={paymentMethod.name}
                onClose={handleResetFlow}
                onSuccess={handleBtcPaymentSuccess}
                onFail={handlePaymentFailure}
                userProjects={userProjects}
                paymentPlans={paymentPlans}
                invoice_connector_project={invoice.connector?.project}
                invoice_connector_integration_id={
                  invoice.connector?.integration_id
                }
                company_credit_purchaseType={
                  userInfo.company?.credit?.purchaseType
                }
                // document_loanApplicationId={userInfo.do}
              />
            )}
          {flow === FLOWS.PAY_WITH_BANK_ACCOUNT_FLOW &&
            paymentMethod &&
            invoice && (
              <PayWithBankAccountFlow
                invoiceId={invoice.id}
                invoiceCompanyName={invoice.company.name}
                paymentMethod={paymentMethod}
                jwtToken={jwtToken}
                invoice={invoice}
                onClose={handleResetFlow}
                onSuccess={handleAchCardPaymentSuccess}
                onFail={handlePaymentFailure}
              />
            )}
          {flow === FLOWS.PAY_WITH_CARD_FLOW && paymentMethod && invoice && (
            <PayWithCardFlow
              invoiceId={invoice.id}
              invoiceCompanyName={invoice.company.name}
              paymentMethod={paymentMethod}
              jwtToken={jwtToken}
              invoice={invoice}
              onClose={handleResetFlow}
              onSuccess={handleAchCardPaymentSuccess}
              onFail={handlePaymentFailure}
            />
          )}
          {flow === FLOWS.CONNECT_NEW_BANK_ACCOUNT && (
            <ConnectBankModal
              onClose={handleResetFlow}
              onPrimaryForCreditChanged={handlePrimaryForCreditChanged}
            />
          )}
        </Wrapper>
      </ScreenWrapper>
    )
  },
)

import React, { useCallback, useEffect, useState } from 'react'
import { StyleSheet, View, Text } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import Spacer from '../../../ui/atoms/Spacer'
import BuilderBottomModal from '../../../ui/molecules/BuilderBottomModal'
import { paths } from '../../links'
import { IconErrorOutlined, IconAutoRenew } from '../../../assets/icons'
import { observer } from 'mobx-react'
import { useStore } from '../../../store'
import SessionStorage from '../../../utils/helpers/SessionStorage'
import { useTranslation } from 'react-i18next'
import { useResponsive } from '../../../utils/hooks'
import Button from '../../IntegrationPayment/RegularIntegration/components/Button'
import { routes, dictionaries } from '@linqpal/models'

interface AutoPayAlertContentProps {
  icon: React.ReactNode
  title: string
  message: string
  buttonText: string
  onActionPress: () => void
  sm: any
}

const STORAGE_KEY = 'ihc_autopay_required_alert_shown'

const AutoPayAlertContent = React.memo<AutoPayAlertContentProps>(
  ({ icon, title, message, buttonText, onActionPress, sm }) => (
    <View style={styles.alertContainer}>
      <View
        style={
          sm
            ? styles.iconContainer
            : [styles.iconContainer, styles.mobileIconContainer]
        }
      >
        {icon}
      </View>
      <Spacer height={sm ? 25 : 35} />

      <Text style={sm ? styles.title : [styles.title, styles.mobileTitle]}>
        {title}
      </Text>
      <Spacer height={10} />

      <Text style={sm ? styles.text : [styles.text, styles.mobileText]}>
        {message}
      </Text>
      <Spacer height={30} />

      {sm && (
        <Button
          buttonStyle={styles.desktopButton}
          textStyle={styles.desktopButtonText}
          onPress={onActionPress}
          testID="setup_auto_pay_btn"
          label={buttonText}
        />
      )}
    </View>
  ),
)

export const InHouseCreditAutoPayAlert: React.FC = observer(() => {
  const navigation = useNavigation()
  const { t } = useTranslation('global')
  const { sm } = useResponsive()
  const { userStore } = useStore()

  const [visible, setVisible] = useState(false)
  const [hasShownAlert, setHasShownAlert] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const createRecommendedNotification = useCallback(async () => {
    const isAutoPayRequired =
      userStore.company?.settings?.inHouseCredit?.isAutoPayRequired || false
    if (!isAutoPayRequired) {
      try {
        await routes.notification.createNotificationByType(
          dictionaries.notificationTypes.ihcAutoPayRecommended,
          dictionaries.notificationText.ihcAutoPayRecommended,
        )
      } catch (err) {
        console.error('Error creating IHC AutoPay notification:', err)
      }
    }
  }, [userStore.company?.settings?.inHouseCredit?.isAutoPayRequired])

  useEffect(() => {
    const checkAndShowAlert = async () => {
      setIsLoading(true)
      setError(null)

      try {
        if (
          !userStore.inHouseCreditApplications?.length ||
          !userStore.company?.settings ||
          hasShownAlert
        ) {
          setIsLoading(false)
          return
        }

        const isAnyFactoringEnabled =
          userStore.suppliers.some((suppl) => suppl.isFactoringEnabled) || false

        const inHouseSettings = userStore.company?.settings?.inHouseCredit
        const isAutoPayDisabled =
          inHouseSettings && !inHouseSettings.isAutoPayEnabledByCompanyUser
        const isAutoPayRequired = inHouseSettings?.isAutoPayRequired || false

        if (isAnyFactoringEnabled && isAutoPayDisabled) {
          if (isAutoPayRequired) {
            const alertShown = SessionStorage.get(STORAGE_KEY)
            if (!alertShown) {
              setVisible(true)
              setHasShownAlert(true)
              SessionStorage.set(STORAGE_KEY, true)
            }
          } else {
            try {
              const response =
                await routes.notification.checkNotificationExists(
                  dictionaries.notificationTypes.ihcAutoPayRecommended,
                )
              if (!response.exists) {
                setVisible(true)
                setHasShownAlert(true)
              }
            } catch (err) {
              console.error('Error checking IHC AutoPay notification:', err)
              setError(err instanceof Error ? err : new Error('Unknown error'))
            }
          }
        }
      } catch (e) {
        setError(e instanceof Error ? e : new Error('Unknown error'))
      } finally {
        setIsLoading(false)
      }
    }

    if (userStore?.isAuthenticated) {
      checkAndShowAlert()
    }
  }, [userStore, hasShownAlert])

  const onClose = useCallback(async () => {
    setVisible(false)
    await createRecommendedNotification()
  }, [createRecommendedNotification])

  const navigateToSettings = useCallback(async () => {
    setVisible(false)
    await createRecommendedNotification()

    if (sm) {
      navigation.navigate(paths.Console.Accounts._self)
    } else {
      navigation.navigate(paths.Console.More._self, {
        screen: paths.Console.More.Wallet,
      })
    }
  }, [navigation, sm, createRecommendedNotification])

  const alertContent = {
    required: {
      title: t('autoPayAlert.required.title'),
      message: t('autoPayAlert.required.message'),
      buttonText: t('autoPayAlert.required.buttonText'),
    },
    recommended: {
      title: t('autoPayAlert.recommended.title'),
      message: t('autoPayAlert.recommended.message'),
      buttonText: t('autoPayAlert.recommended.buttonText'),
    },
  }
  const isAutoPayRequired =
    userStore.company?.settings?.inHouseCredit?.isAutoPayRequired || false

  const renderAlertContent = React.useMemo(() => {
    const content = isAutoPayRequired
      ? alertContent.required
      : alertContent.recommended

    return (
      <AutoPayAlertContent
        icon={
          isAutoPayRequired ? (
            <IconErrorOutlined width={72} height={72} />
          ) : (
            <IconAutoRenew width={72} height={72} />
          )
        }
        title={content.title}
        message={content.message}
        buttonText={content.buttonText}
        onActionPress={navigateToSettings}
        sm={sm}
      />
    )
  }, [
    isAutoPayRequired,
    navigateToSettings,
    alertContent.required,
    alertContent.recommended,
    sm,
  ])

  const mobileFooter = React.useMemo(() => {
    const content = isAutoPayRequired
      ? alertContent.required
      : alertContent.recommended
    return (
      <View style={styles.footer}>
        <Button
          buttonStyle={styles.mobileButton}
          onPress={navigateToSettings}
          testID="setup_auto_pay_btn"
          label={content.buttonText}
        />
      </View>
    )
  }, [
    navigateToSettings,
    isAutoPayRequired,
    alertContent.required,
    alertContent.recommended,
  ])

  if (isLoading || error || !visible) {
    return null
  }
  return (
    <BuilderBottomModal
      visible={visible}
      onClose={onClose}
      testID={
        isAutoPayRequired
          ? 'inhousecredit_autopay_required_alert'
          : 'inhousecredit_autopay_recommended_alert'
      }
      height={sm ? 430 : '95%'}
      width={sm ? 600 : undefined}
      footer={sm ? null : mobileFooter}
    >
      {renderAlertContent}
    </BuilderBottomModal>
  )
})

const styles = StyleSheet.create({
  iconContainer: {
    alignItems: 'center',
    marginTop: -25,
  },
  mobileIconContainer: {
    marginTop: -70,
  },
  alertContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  footer: {
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopColor: '#DEE5EB',
    borderTopWidth: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    lineHeight: 32,
    textAlign: 'center',
    color: '#19262F',
  },
  mobileTitle: {
    fontSize: 18,
    lineHeight: 24,
    letterSpacing: -0.3,
  },
  text: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '400',
    textAlign: 'center',
    paddingHorizontal: 20,
    color: '#19262F',
  },
  mobileText: {
    lineHeight: 20,
    fontWeight: '500',
  },
  mobileButton: {
    width: '100%',
    borderRadius: 8,
  },
  desktopButton: {
    height: 40,
    width: 230,
    alignSelf: 'center',
  },
  desktopButtonText: {
    fontSize: 14,
    lineHeight: 24,
    fontWeight: '700',
    fontFamily: 'Inter',
    letterSpacing: 0,
  },
})

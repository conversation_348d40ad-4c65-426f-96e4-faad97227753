import React, { memo } from 'react'
import { useTranslation } from 'react-i18next'
import { Text, View } from 'react-native'
import { BankIcon, CircleArrowIcon } from '../../../../assets/icons'
import { Link } from '../../../../ui/atoms/Link'
import BuilderBottomModal from '../../../../ui/molecules/BuilderBottomModal'
import { ActivityIndicator } from 'react-native-paper'
import { ListItem } from '../../../../ui/atoms/builder-2.0/ListItem'

interface AddNewBankAccountFlow_IDLEProps {
  loading: boolean
  onClose: () => void
  onCreateToken: () => void
  onLinkBankManually: () => void
}

export const AddNewBankAccountFlow_IDLE: React.FC<AddNewBankAccountFlow_IDLEProps> =
  memo(({ loading, onClose, onCreateToken, onLinkBankManually }) => {
    const { t } = useTranslation()

    return (
      <>
        <BuilderBottomModal
          visible={true}
          onClose={onClose}
          height={300}
          testID={'add-new-bank-account-modal-idle'}
        >
          <View
            style={{ alignItems: 'center' }}
            testID={'add-bank-account-methods-list'}
          >
            <ListItem
              icon={<BankIcon />}
              title={t('ConnectYourBank')}
              subtitle={t('forFasterCheckout')}
              testID={'connect-bank-via-plaid-item'}
              onPress={onCreateToken}
              style={{ marginTop: 18, backgroundColor: '#F5F7F8' }}
              disabled={loading}
              endIcon={loading ? <ActivityIndicator /> : <CircleArrowIcon />}
            />
            <View
              style={{ height: 72, justifyContent: 'center' }}
              testID={'link-bank-manually-container'}
            >
              <Link
                title={t('linkBankManually')}
                testID={'link-bank-manually-item'}
                onPress={onLinkBankManually}
                disabled={loading}
                color={'#5F788A'}
                textStyle={{
                  textDecorationLine: 'underline',
                  fontWeight: '500',
                  textAlign: 'center',
                }}
              />
              <Text
                style={{ color: '#5F788A', textAlign: 'center' }}
                testID={'take-2-business-days-title'}
              >
                {t('takes2BusinessDays')}
              </Text>
            </View>
          </View>
        </BuilderBottomModal>
      </>
    )
  })

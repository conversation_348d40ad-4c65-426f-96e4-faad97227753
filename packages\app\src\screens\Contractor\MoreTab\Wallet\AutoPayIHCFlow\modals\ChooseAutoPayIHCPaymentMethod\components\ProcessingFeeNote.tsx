import React from 'react'
import { StyleSheet, View } from 'react-native'
import { BtText } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'
import { IconWarningSignBlue } from '../../../../../../../../assets/icons'
import useIsMobile from '../../../../../../PayablesTab/hooks/useIsMobile'

interface ProcessingFeeNoteProps {
  processingFee: string
}

export const ProcessingFeeNote = ({
  processingFee,
}: ProcessingFeeNoteProps) => {
  const isMobile = useIsMobile()
  const { t } = useTranslation('global')

  if (!processingFee) return null

  return (
    <View style={[styles.container, isMobile && styles.mobileContainer]}>
      <View style={styles.iconContainer}>
        <IconWarningSignBlue width={20} height={20} />
      </View>
      <BtText style={[styles.noteText, isMobile && styles.mobileNoteText]}>
        {t('autoPayIHCFlow.processingFeeNote', { processingFee })}
      </BtText>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F4F9FD',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginTop: 35,
  },
  mobileContainer: {
    marginTop: 40,
  },
  iconContainer: {
    marginRight: 10,
    paddingTop: 2,
    alignSelf: 'flex-start',
  },
  noteText: {
    flex: 1,
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '700',
    fontFamily: 'Inter',
    color: '#001929',
  },
  mobileNoteText: {
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: -0.3,
  },
})

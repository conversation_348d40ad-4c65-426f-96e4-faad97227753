import { post } from '../helpers/caller'

export const bff = {
  domain: 'bff',

  loanPayment(data: {
    drawId: string
    amount: number
    bankAccountId: string
    autoDebit?: boolean
  }) {
    return post(bff.domain, bff.loanPayment, data)
  },

  invoicePayment(data: {
    invoiceIds: string[]
    paymentMethod: string
    accountId: string
    companyId: string
    autoDebit?: boolean
    requestedAmount?: number
  }) {
    return post(bff.domain, bff.invoicePayment, data)
  },
}

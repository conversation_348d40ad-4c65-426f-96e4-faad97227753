import React, { useState, useCallback } from 'react'
import { observer } from 'mobx-react'
import { View } from 'react-native'
import { AddNewBankAccountFlow } from '../../../../IntegrationPayment/InvoiceDetails/flows/AddNewBankAccountFlow'
import { AddNewCreditCardFlow } from '../../../../IntegrationPayment/InvoiceDetails/flows/AddNewCreditCardFlow'

import {
  FLOWS,
  IPaymentMethod,
  useHandleAutoPayIHCFlow,
} from './hooks/useHandleAutoPayIHCFlow'
import { ChooseAutoPayIHCPaymentMethodBottomModal } from './modals/ChooseAutoPayIHCPaymentMethod'
import { AutoPayIHCMode } from './modals/ChooseAutoPayIHCPaymentMethod/components'
import {
  AddNewPaymentMethodModal,
  AddNewPaymentMethodMode,
} from './modals/AddNewPaymentMethod'
import { useStore } from '../../../../../store'
import { TurnOffAutoPayIHCFlow } from './flows/TurnOffAutoPayIHCFlow'
import { AutoPayIHCSetupSuccess } from './modals/AutoPayIHCSetupSuccess'
import BuilderBottomModal from '../../../../../ui/molecules/BuilderBottomModal'
import { Spinner } from '../../../../../ui/atoms/Spinner'

interface IAutoPayIHCFlowProps {
  onClose: () => void
  navigation: any
  onSuccess: () => void
}

interface IPaymentFlowHandlers {
  handleAddNewBankAccount: () => void
  handleAddNewCreditCard: () => void
  handleAddNewPaymentMethod: () => void
  handleResetFlow: () => void
  handleNewPaymentMethodAdded: () => void
  handleFinishPaymentMethodAdded: () => void
  handleLinkBankManually: () => void
  handleChoosePaymentMethod: () => void
  handleCloseFlow: () => void
  handleSuccess: () => void
  handleTurnOffAutoPay: () => void
  handleSaveAutoPaymentMethod: (paymentMethodId: string) => Promise<boolean>
  isSubmitting: boolean
}

const AutoPayIHCFlowContent = observer(
  ({
    flow,
    initialPaymentMethod,
    paymentMethods,
    arePaymentMethodsLoading,
    handlers,
    mode,
    selectedPaymentMethod,
  }: {
    flow: FLOWS
    initialPaymentMethod: IPaymentMethod | null
    paymentMethods: any[]
    arePaymentMethodsLoading: boolean
    handlers: IPaymentFlowHandlers
    mode: AutoPayIHCMode
    selectedPaymentMethod: IPaymentMethod | null
  }) => {
    switch (flow) {
      case FLOWS.NONE:
        return (
          <BuilderBottomModal
            visible={true}
            onClose={handlers.handleCloseFlow}
            height={400}
          >
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                flex: 1,
                marginTop: -50,
              }}
            >
              <Spinner size="large" status="primary" />
            </View>
          </BuilderBottomModal>
        )

      case FLOWS.CHOOSE_PAYMENT_METHOD:
        return (
          <ChooseAutoPayIHCPaymentMethodBottomModal
            onAddNewPaymentMethod={handlers.handleAddNewPaymentMethod}
            onSavePaymentMethod={handlers.handleSaveAutoPaymentMethod}
            onTurnOffAutoPay={handlers.handleTurnOffAutoPay}
            mode={mode}
            paymentMethods={paymentMethods}
            arePaymentMethodsLoading={arePaymentMethodsLoading}
            onClose={handlers.handleCloseFlow}
            initialPaymentMethod={initialPaymentMethod}
            isSubmitting={handlers.isSubmitting}
          />
        )

      case FLOWS.ADD_FIRST_PAYMENT_METHOD:
        return (
          <AddNewPaymentMethodModal
            onLinkCard={handlers.handleAddNewCreditCard}
            onConnectBank={handlers.handleAddNewBankAccount}
            onClose={handlers.handleCloseFlow}
            mode={AddNewPaymentMethodMode.AddFirstMethod}
          />
        )

      case FLOWS.ADD_ADDITIONAL_PAYMENT_METHOD:
        return (
          <AddNewPaymentMethodModal
            onLinkCard={handlers.handleAddNewCreditCard}
            onConnectBank={handlers.handleAddNewBankAccount}
            onClose={handlers.handleResetFlow}
            onBack={handlers.handleChoosePaymentMethod}
            mode={AddNewPaymentMethodMode.AddAdditionalMethod}
          />
        )

      case FLOWS.ADD_NEW_BANK_ACCOUNT:
        return (
          <AddNewBankAccountFlow
            onClose={handlers.handleCloseFlow}
            onSuccess={handlers.handleNewPaymentMethodAdded}
            onLinkBankAccountManually={handlers.handleLinkBankManually}
            onFinish={handlers.handleFinishPaymentMethodAdded}
          />
        )

      case FLOWS.ADD_NEW_CREDIT_CARD:
        return (
          <AddNewCreditCardFlow
            onClose={handlers.handleCloseFlow}
            onSuccess={handlers.handleNewPaymentMethodAdded}
            invoiceId={''}
          />
        )

      case FLOWS.TURN_OFF_AUTO_PAY:
        return (
          <TurnOffAutoPayIHCFlow
            onClose={handlers.handleCloseFlow}
            onSuccess={handlers.handleSuccess}
            onKeepAutoPayEnabled={handlers.handleResetFlow}
          />
        )

      case FLOWS.AUTO_PAY_CONFIGURATION_SUCCESS:
        return (
          <AutoPayIHCSetupSuccess
            onClose={handlers.handleSuccess}
            paymentMethod={
              selectedPaymentMethod ||
              paymentMethods.find((m) => m.isPrimaryForIHC)
            }
            mode={mode}
          />
        )

      default:
        return null
    }
  },
)

export const AutoPayIHCFlow = observer(
  ({ onClose, navigation, onSuccess }: IAutoPayIHCFlowProps) => {
    const { userStore, screensStore } = useStore()
    const { paymentMethodsStore } = screensStore
    const inHouseSettings = userStore.company?.settings?.inHouseCredit
    const isAutoPayRequired = inHouseSettings?.isAutoPayRequired || false

    const [paymentMethod, setPaymentMethod] = useState<IPaymentMethod | null>(
      null,
    )

    const rawPaymentMethods = paymentMethodsStore.paymentMethods
    const arePaymentMethodsLoading = paymentMethodsStore.loading

    const getInitialFlow = () => {
      if (arePaymentMethodsLoading) return FLOWS.NONE
      if (!rawPaymentMethods?.length) return FLOWS.ADD_FIRST_PAYMENT_METHOD
      return FLOWS.CHOOSE_PAYMENT_METHOD
    }

    const [flow, setFlow] = useState<FLOWS>(getInitialFlow())
    const [
      hasInitiallyFetchedPaymentMethods,
      setHasInitiallyFetchedPaymentMethods,
    ] = useState(false)

    const paymentMethods = React.useMemo(() => {
      if (arePaymentMethodsLoading) return []
      return rawPaymentMethods.map((method) => ({
        ...method,
        isDeactivated: method.isDeactivated ?? false,
        isPrimaryForIHC: method.isPrimaryForIHCAutoPay ?? false,
      })) as IPaymentMethod[]
    }, [rawPaymentMethods, arePaymentMethodsLoading])

    React.useEffect(() => {
      if (!hasInitiallyFetchedPaymentMethods && !arePaymentMethodsLoading) {
        console.log(
          'AutoPayIHCFlow: fetching payment methods via hasInitiallyFetchedPaymentMethods',
        )
        paymentMethodsStore.fetchPaymentMethods()
        setHasInitiallyFetchedPaymentMethods(true)
      }
    }, [
      hasInitiallyFetchedPaymentMethods,
      arePaymentMethodsLoading,
      paymentMethodsStore,
    ])

    const initialPaymentMethod = React.useMemo(() => {
      if (!paymentMethods?.length) return null
      return (
        paymentMethods.find(
          (method) => method.isPrimaryForIHC && !method.isDeactivated,
        ) || null
      )
    }, [paymentMethods])

    const mode = React.useMemo(() => {
      if (!initialPaymentMethod) {
        return AutoPayIHCMode.Enable
      } else if (isAutoPayRequired) {
        return AutoPayIHCMode.UpdateAutoPayRequired
      } else {
        return AutoPayIHCMode.UpdateAutoPayNotRequired
      }
    }, [initialPaymentMethod, isAutoPayRequired])

    const initialFlow = React.useMemo(() => {
      if (!paymentMethods?.length) {
        return FLOWS.ADD_FIRST_PAYMENT_METHOD
      }
      return FLOWS.CHOOSE_PAYMENT_METHOD
    }, [paymentMethods])

    React.useEffect(() => {
      if (!arePaymentMethodsLoading) {
        if (
          flow === FLOWS.NONE ||
          (flow === FLOWS.ADD_FIRST_PAYMENT_METHOD &&
            paymentMethods?.length > 0) ||
          (flow === FLOWS.CHOOSE_PAYMENT_METHOD && !paymentMethods?.length)
        ) {
          setFlow(initialFlow)
        }
      }
    }, [initialFlow, arePaymentMethodsLoading, flow, paymentMethods?.length])

    React.useEffect(() => {
      if (initialPaymentMethod && !paymentMethod) {
        setPaymentMethod(initialPaymentMethod)
      }
    }, [initialPaymentMethod, paymentMethod])

    const handleCloseFlow = useCallback(() => {
      setFlow(FLOWS.NONE)
      setPaymentMethod(null)
      onClose()
    }, [onClose])

    const refetchPaymentMethods = useCallback(() => {
      paymentMethodsStore.fetchPaymentMethods()
    }, [paymentMethodsStore])

    const handleSuccess = useCallback(() => {
      refetchPaymentMethods()
      userStore.fetchUser()
      setHasInitiallyFetchedPaymentMethods(false)

      if (onSuccess) {
        onSuccess()
      } else {
        handleCloseFlow()
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [handleCloseFlow, onSuccess, userStore])

    const handlers = useHandleAutoPayIHCFlow(
      setFlow,
      setPaymentMethod,
      refetchPaymentMethods,
      navigation,
      onClose,
      mode,
      paymentMethods,
    )

    const allHandlers = {
      ...handlers,
      handleCloseFlow,
      handleSuccess,
    }

    return (
      <AutoPayIHCFlowContent
        flow={flow}
        initialPaymentMethod={initialPaymentMethod}
        paymentMethods={paymentMethods || []}
        arePaymentMethodsLoading={arePaymentMethodsLoading}
        handlers={allHandlers}
        mode={mode}
        selectedPaymentMethod={paymentMethod}
      />
    )
  },
)

import React, { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import {
  LOAN_REPAYMENT_NAME,
  LOAN_REPAYMENT_SCHEDULE_STATUS,
  LOAN_REPAYMENT_STATUS,
  LOAN_REPAYMENT_TYPE,
} from '@linqpal/models/src/dictionaries/loanStatuses'
import { Text } from 'react-native-paper'
import { StyleSheet, View } from 'react-native'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'
import { InfoIcon } from '../../../../../assets/icons'
import TooltipView from '../../../../../ui/molecules/TooltipView'
import { currencyMask, dateMask } from '../../../../../utils/helpers/masking'
import { roundVal } from '../../../CreditTab/LoanSchedule/roundVal'
import { VerticalPaymentProgress } from './VerticalPaymentProgress'

export interface ITimelineItem {
  type: string
  scheduleStatus: string
  status: string
  expectedDate: string
  expectedAmount: number
  processingAmount: number
  processingDate: string
  paidAmount: number
  paidDate: string
}

interface TimelineItemProps {
  installment: ITimelineItem
  isLastPayment: boolean
  oneInstallment: boolean
}

const { CURRENT } = LOAN_REPAYMENT_SCHEDULE_STATUS
const { LATE_FEE, EXTENSION_FEE, MANUAL_LATE_FEE } = LOAN_REPAYMENT_TYPE

const redHighlight = '#DB2424'
const defaultColor = '#19262F'
const disabled = 'rgba(25, 38, 47, 0.3)'

const Label: React.FC<{ status: string }> = ({ status }) => {
  const { t } = useTranslation('global')
  const PaymentStatus = {
    [LOAN_REPAYMENT_STATUS.PAID]: t(
      'PaymentHistoryInvoice.paymentSchedule.paid',
    ),
    [LOAN_REPAYMENT_STATUS.PROCESSING]: t(
      'PaymentHistoryInvoice.paymentSchedule.processing',
    ),
    [LOAN_REPAYMENT_STATUS.PENDING]: t(
      'PaymentHistoryInvoice.paymentSchedule.due',
    ),
    [LOAN_REPAYMENT_STATUS.RESCHEDULED]: t(
      'PaymentHistoryInvoice.paymentSchedule.rescheduled',
    ),
    [LOAN_REPAYMENT_STATUS.DUENEXT]: t(
      'PaymentHistoryInvoice.paymentSchedule.due',
    ),
    [LOAN_REPAYMENT_STATUS.PASTDUE]: t(
      'PaymentHistoryInvoice.paymentSchedule.pastDue',
    ),
    [LOAN_REPAYMENT_STATUS.LATE]: t(
      'PaymentHistoryInvoice.paymentSchedule.pastDue',
    ),
    [LOAN_REPAYMENT_STATUS.FAILED]: t(
      'PaymentHistoryInvoice.paymentSchedule.pastDue',
    ),
  }
  const labelColor = [
    LOAN_REPAYMENT_STATUS.PASTDUE,
    LOAN_REPAYMENT_STATUS.FAILED,
    LOAN_REPAYMENT_STATUS.LATE,
  ].includes(status)
    ? redHighlight
    : defaultColor
  const label = PaymentStatus[status]
  return (
    <Text
      style={{
        fontWeight:
          label === PaymentStatus[LOAN_REPAYMENT_STATUS.PAID] ? '500' : '400',
        fontSize: 16,
        color: labelColor,
        lineHeight: 24,
        fontFamily: 'Inter',
      }}
    >
      {label}
    </Text>
  )
}

const ItemDate: React.FC<{ date: string; scheduleStatus }> = ({
  date,
  scheduleStatus,
}) => {
  const color = scheduleStatus !== CURRENT ? disabled : defaultColor
  return (
    <Text style={composeStyle(styles.date, { color })}>
      {dateMask(date)} {'  '}
    </Text>
  )
}

const Amount: React.FC<{ status: string; expectedAmount: number }> = ({
  status,
  expectedAmount,
}) => {
  const amtColor = useMemo(() => {
    let color = defaultColor
    if (
      [LOAN_REPAYMENT_STATUS.LATE, LOAN_REPAYMENT_STATUS.PASTDUE].includes(
        status,
      )
    )
      color = redHighlight
    else if (status === LOAN_REPAYMENT_STATUS.RESCHEDULED) color = disabled
    return color
  }, [status])

  return (
    <Text style={composeStyle(styles.amount, { color: amtColor })}>
      {currencyMask(expectedAmount)}
    </Text>
  )
}

const Info: React.FC<{
  status: string
}> = ({ status }) => {
  const { t } = useTranslation('global')
  return status === LOAN_REPAYMENT_STATUS.PROCESSING ? (
    // eslint-disable-next-line i18next/no-literal-string
    <TooltipView helper={t('ViewLoan.processing-info')} position="right">
      <InfoIcon style={{ marginLeft: 5, marginTop: 5 }} />
    </TooltipView>
  ) : null
}

const Fee: React.FC<{
  type: string
  status: string
}> = ({ status, type }) => {
  const color = useMemo(() => {
    if (status === LOAN_REPAYMENT_STATUS.RESCHEDULED) return disabled
    return redHighlight
  }, [status])
  return (
    <Text style={composeStyle(styles.latefee, { color })}>
      {LOAN_REPAYMENT_NAME[type]}
    </Text>
  )
}

export const PaymentScheduleTimelineItem: React.FC<TimelineItemProps> = ({
  installment,
  isLastPayment,
  oneInstallment,
}) => {
  const { t } = useTranslation('global')
  const {
    type,
    scheduleStatus,
    status,
    expectedDate,
    expectedAmount,
    processingAmount,
    processingDate,
    paidAmount,
    paidDate,
  } = installment
  const isCompleted = status === LOAN_REPAYMENT_STATUS.PAID
  const isFee = [LATE_FEE, EXTENSION_FEE, MANUAL_LATE_FEE].includes(type)

  const paymentStatus = useMemo(() => {
    const day = 24 * 3600 * 1000
    if (scheduleStatus !== CURRENT) return LOAN_REPAYMENT_STATUS.RESCHEDULED
    if (
      ![
        LOAN_REPAYMENT_STATUS.PASTDUE,
        LOAN_REPAYMENT_STATUS.FAILED,
        LOAN_REPAYMENT_STATUS.LATE,
      ].includes(status) &&
      scheduleStatus === CURRENT &&
      !paidAmount &&
      !processingAmount &&
      new Date(expectedDate).getTime() + 5 * day >= new Date().getTime()
    )
      return LOAN_REPAYMENT_STATUS.DUENEXT
    if (
      [
        LOAN_REPAYMENT_STATUS.PROCESSING,
        LOAN_REPAYMENT_STATUS.PENDING,
        LOAN_REPAYMENT_STATUS.LATE,
      ].includes(status) &&
      (!roundVal(expectedAmount - processingAmount) ||
        !roundVal(expectedAmount - (processingAmount + paidAmount)))
    )
      return LOAN_REPAYMENT_STATUS.PROCESSING

    return status
  }, [
    scheduleStatus,
    status,
    processingAmount,
    paidAmount,
    expectedAmount,
    expectedDate,
  ])

  const showProcessingCaption = useMemo(
    () =>
      [
        LOAN_REPAYMENT_STATUS.LATE,
        LOAN_REPAYMENT_STATUS.PASTDUE,
        LOAN_REPAYMENT_STATUS.PROCESSING,
        LOAN_REPAYMENT_STATUS.PENDING,
      ].includes(paymentStatus) &&
      expectedAmount - processingAmount &&
      processingAmount,

    [processingAmount, expectedAmount, paymentStatus],
  )

  const showPaidOffCaption = useMemo(
    () =>
      [
        LOAN_REPAYMENT_STATUS.LATE,
        LOAN_REPAYMENT_STATUS.PASTDUE,
        LOAN_REPAYMENT_STATUS.PROCESSING,
        LOAN_REPAYMENT_STATUS.PENDING,
      ].includes(paymentStatus) &&
      expectedAmount - paidAmount &&
      paidAmount,

    [paidAmount, expectedAmount, paymentStatus],
  )

  return (
    <View style={{ flexDirection: 'row' }}>
      {!oneInstallment && (
        <VerticalPaymentProgress
          isCompleted={isCompleted}
          shouldHideLine={isLastPayment}
          status={paymentStatus}
        />
      )}
      <View
        style={{
          flex: 1,
          paddingLeft: !oneInstallment ? 15 : 0,
          paddingBottom: isFee ? 45 : 33,
          flexDirection: 'row',
          marginTop: -5,
          justifyContent: 'space-between',
        }}
      >
        <View style={{ marginRight: 10 }}>
          <ItemDate date={expectedDate} scheduleStatus={scheduleStatus} />
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Label status={paymentStatus} />
            <Info status={paymentStatus} />
          </View>
          {!!showProcessingCaption && (
            <Text style={composeStyle(styles.processing, { marginTop: 5 })}>
              {t('LoanSchedule.processing-caption', {
                amount: currencyMask(processingAmount),
                partialAmountCoveredOn: dateMask(processingDate),
              })}
            </Text>
          )}
          {!!showPaidOffCaption && (
            <Text style={styles.processing}>
              {t('LoanSchedule.paid-off-caption', {
                amount: currencyMask(paidAmount),
                partialAmountCoveredOn: dateMask(paidDate),
              })}
            </Text>
          )}
        </View>
        <View style={{ marginTop: -3 }}>
          <Amount status={paymentStatus} expectedAmount={expectedAmount} />
          {isFee && <Fee type={type} status={paymentStatus} />}
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  latefee: {
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 24,
    fontFamily: 'Inter',
    color: redHighlight,
    textAlign: 'right',
  },
  date: {
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 5,
    fontFamily: 'Inter',
  },
  amount: {
    fontWeight: '700',
    fontSize: 16,
    textAlign: 'right',
    lineHeight: 24,
    marginBottom: 8,
    fontFamily: 'Inter',
  },
  processing: {
    fontWeight: '500',
    fontFamily: 'Inter',
    fontSize: 12,
    lineHeight: 16,
    color: 'rgba(25, 38, 47, 0.7)',
  },
})

import { Invoice, Notification, Operation } from '@linqpal/common-backend'
import {
  OPERATION_STATUS,
  OPERATION_TYPES,
} from '@linqpal/models/src/dictionaries'
import mongoose from 'mongoose'
import { authRequired } from '../../services/auth.service'
import { ControllerItem } from '../controllerItem'
import { WebNotificationsService } from '@linqpal/common-backend/src/services/webNotificationsService'

export const middlewares = { pre: [authRequired()] }

export const markAllAsRead: ControllerItem = {
  get: async (req, res, next) => {
    const { type = 'INVOICES' } = req.query
    await Notification.updateMany(
      { 'receiver.company_id': req.company!.id, type },
      { isRead: true },
    )
    return next()
  },
}

export const markAsViewedById: ControllerItem = {
  post: async (req, res, next) => {
    await WebNotificationsService.markAsViewedById(req.body.notificationId)
    return next()
  },
}

export const markAllAsViewed: ControllerItem = {
  get: async (req, res, next) => {
    await Notification.updateMany(
      {
        'receiver.company_id': req.company!._id,
        type: mongoose.trusted({ $in: ['LOAN', 'SUPPLIER'] }),
      },
      { isViewed: true },
      { timestamps: false },
    )
    await Notification.updateMany(
      {
        'receiver.company_id': req.company!._id,
        type: 'INVOICES',
        $or: [
          { 'metadata.alertType': 'cancelled' },
          {
            $and: [
              { 'metadata.alertType': 'expiring' },
              { 'metadata.isExpired': true },
            ],
          },
        ],
      },
      { isViewed: true },
      { timestamps: false },
    )
    return next()
  },
}
export const markAsViewed: ControllerItem = {
  post: async (req, res, next) => {
    const { key, value, types, alertTypes } = req.body
    const query = {
      'receiver.company_id': req.company!.id,
      [key]: new mongoose.Types.ObjectId(value),
      'metadata.alertType': mongoose.trusted({ $in: alertTypes }),
      type: mongoose.trusted({ $in: types }),
    }
    await Notification.findOneAndUpdate(query, { isViewed: true })
    return next()
  },
}

export const list: ControllerItem = {
  post: async (req, res, next) => {
    const pipline = [
      {
        $match: {
          $and: [{ 'receiver.company_id': req.company!.id }, { isRead: false }],
        },
      },
      {
        $lookup: {
          from: Invoice.collection.name,
          localField: 'metadata.invoice_id',
          foreignField: '_id',
          as: 'invoice',
        },
      },
      {
        $lookup: {
          from: Operation.collection.name,
          as: 'operation',
          let: { invoice_id: { $toString: '$metadata.invoice_id' } },
          pipeline: [
            { $match: { $expr: { $eq: ['$owner_id', '$$invoice_id'] } } },
            {
              $match: {
                status: {
                  $in: [
                    OPERATION_STATUS.PLACED,
                    OPERATION_STATUS.PROCESSING,
                    OPERATION_STATUS.SUCCESS,
                    OPERATION_STATUS.FAIL,
                  ],
                },
                type: OPERATION_TYPES.INVOICE.PAYMENT,
              },
            },
          ],
        },
      },
      { $unwind: { path: '$operation', preserveNullAndEmptyArrays: true } },
      { $addFields: { invoice: { $arrayElemAt: ['$invoice', 0] } } },
      { $addFields: { createdAt: { $ifNull: ['$createdAt', '$updatedAt'] } } },
    ]
    const invoiceUnreadNotifications = await Notification.aggregate(pipline)
    res.locals.result = { notifications: invoiceUnreadNotifications }
    return next()
  },
}

export const markAsRead: ControllerItem = {
  post: async (req, res, next) => {
    const { invoiceId, type, alertType } = req.body

    await WebNotificationsService.markAsRead(
      req.company!.id,
      invoiceId,
      type,
      alertType,
    )

    return next()
  },
}

export const checkNotificationExists: ControllerItem = {
  get: async (req, res, next) => {
    const { notificationType } = req.query

    if (!notificationType) {
      res.status(400).json({ error: 'notificationType is required' })
      return
    }

    const existingNotification = await Notification.findOne({
      'receiver.company_id': req.company!.id,
      type: notificationType,
    })

    res.locals.result = { exists: !!existingNotification }
    return next()
  },
}

export const createNotificationByType: ControllerItem = {
  post: async (req, res, next) => {
    const { notificationType, content, metadata = {} } = req.body

    if (!notificationType) {
      res.status(400).json({ error: 'notificationType is required' })
      return
    }

    const existingNotification = await Notification.findOne({
      'receiver.company_id': req.company!.id,
      type: notificationType,
    })

    if (existingNotification) {
      res.locals.result = { result: 'already_exists' }
      return next()
    }

    const notification = new Notification({
      receiver: { company_id: req.company!.id },
      type: notificationType,
      content: content || `Notification of type ${notificationType}`,
      metadata: {
        ...metadata,
        companyId: new mongoose.Types.ObjectId(req.company!.id),
      },
      isRead: true,
      isViewed: true,
    })

    await notification.save()
    res.locals.result = { result: 'ok' }
    return next()
  },
}

import { useReducer } from 'react'

export enum AutoPayIHCActionType {
  HIDE_WARNING = 'HIDE_WARNING',
  SHOW_IHC_SETUP_FLOW = 'SHOW_IHC_SETUP_FLOW',
  HIDE_IHC_SETUP_FLOW = 'HIDE_IHC_SETUP_FLOW',
  AUTO_PAY_IHC_SETUP_SUCCESS = 'AUTO_PAY_IHC_SETUP_SUCCESS',
}

export interface AutoPayIHCState {
  showAutoPayWarning: boolean
  showAutoPayFlow: boolean
  refreshKey: number
}

export type AutoPayIHCAction =
  | { type: AutoPayIHCActionType.HIDE_WARNING }
  | { type: AutoPayIHCActionType.SHOW_IHC_SETUP_FLOW }
  | { type: AutoPayIHCActionType.HIDE_IHC_SETUP_FLOW }
  | { type: AutoPayIHCActionType.AUTO_PAY_IHC_SETUP_SUCCESS }

const autoPayIHCReducer = (
  state: AutoPayIHCState,
  action: AutoPayIHCAction,
): AutoPayIHCState => {
  switch (action.type) {
    case AutoPayIHCActionType.HIDE_WARNING:
      return { ...state, showAutoPayWarning: false }
    case AutoPayIHCActionType.SHOW_IHC_SETUP_FLOW:
      return { ...state, showAutoPayFlow: true }
    case AutoPayIHCActionType.HIDE_IHC_SETUP_FLOW:
      return { ...state, showAutoPayFlow: false }
    case AutoPayIHCActionType.AUTO_PAY_IHC_SETUP_SUCCESS:
      return {
        ...state,
        showAutoPayFlow: false,
        refreshKey: state.refreshKey + 1,
      }
    default:
      return state
  }
}

const initialAutoPayIHCState: AutoPayIHCState = {
  showAutoPayWarning: true,
  showAutoPayFlow: false,
  refreshKey: 0,
}

export const useAutoPayIHCReducer = () => {
  return useReducer(autoPayIHCReducer, initialAutoPayIHCState)
}

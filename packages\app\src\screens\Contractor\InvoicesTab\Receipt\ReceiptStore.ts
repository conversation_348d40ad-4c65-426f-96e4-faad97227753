import { types } from 'mobx-state-tree'
import { editableModel, routes } from '@linqpal/models'
import makeInspectable from 'mobx-devtools-mst'

const props = {
  invoice: types.maybeNull(types.frozen()),
  isPaidWithCredit: types.optional(types.boolean, false),
  loading: types.optional(types.boolean, false),
  successTransaction: types.maybeNull(types.frozen()),
  loanReceivables: types.optional(types.array(types.frozen()), []),
  paymentScheduleLoading: types.optional(types.boolean, false),
}

function views(self) {
  return {}
}

function actions(self) {
  return {
    setInvoice: (inv) => {
      self.invoice = inv
    },
    setLoading: (value) => {
      self.loading = value
    },
    setIsPaidWithCredit: (value) => {
      self.isPaidWithCredit = value
    },
    setSuccessTransaction: (data) => {
      self.successTransaction = data
    },
    setLoanReceivables: (receivables) => {
      self.loanReceivables = receivables
    },
    setPaymentScheduleLoading: (value) => {
      self.paymentScheduleLoading = value
    },
    getInvoice: (id) => {
      self.setLoading(true)
      routes.invoices
        .showReceipt(id)
        .then((res) => {
          const { invoice: inv } = res
          self.setInvoice({
            ...inv,
            amountPaid: inv?.total_amount,
            achDiscount: inv.operation.metadata?.achDiscount,
          })
          self.setIsPaidWithCredit(res.isPaidWithCredit)
          self.setSuccessTransaction(res.invoice.operation?.transactions[0])
        })
        .catch((err) => {
          console.log(err.message)
        })
        .finally(() => {
          self.setLoading(false)
        })
    },
    getPaymentSchedule: (id) => {
      self.setPaymentScheduleLoading(true)
      routes.invoices
        .paymentSchedule(id)
        .then((res) => {
          if (res.result === 'ok') {
            self.setLoanReceivables(res.loanReceivables || [])
          }
        })
        .catch((err) => {
          console.log('Error fetching payment schedule:', err.message)
          self.setLoanReceivables([])
        })
        .finally(() => {
          self.setPaymentScheduleLoading(false)
        })
    },
  }
}

export const ReceiptStoreModel = types
  .compose(types.model(props).views(views).actions(actions), editableModel())
  .named('ReceiptStore')
const store = ReceiptStoreModel.create({})
makeInspectable(store)
export default store

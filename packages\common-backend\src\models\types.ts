import {
  IAccountingSettingsModel,
  IBankAccountModel,
  IBrandingModel,
  ICompanyCreditInfo,
  ICompanyModel,
  IConnectorModel,
  ICustomerAccountModel,
  IEncrypted,
  IInstitutionModel,
  IInvitationModel,
  IInvoiceDetailsModel,
  IInvoiceModel,
  ILoanApplicationModel,
  ILoanPaymentPlanModel,
  ILoanPriceModel,
  ILoanPricingPackageModel,
  IOperationModel,
  ISettingsModel,
  ISupplierInvitationDetailsModel,
  ITransactionModel,
  IUserModel,
  IUserRoleModel,
} from '@linqpal/models'
import mongoose from 'mongoose'
import { PlaidAssetsReportStatusResponse } from '../services/plaid/types'
import {
  IArAdvanceSettings,
  IAutomatedDrawApprovalSettings,
  ICustomerSettings,
  IDepositDetailsSettings,
  IDirectTermsSettings,
  IDownPaymentSettings,
  IInHouseCreditCompanySettings,
} from '@linqpal/models/src/types/routes'
import { CustomerAccountType } from '@linqpal/models/src/dictionaries/customerAccountType'

export interface IMongoose {
  createdAt: Date
  updatedAt: Date
}

export interface IUser extends mongoose.Document, IMongoose, IUserModel {
  sub: string
  firebaseId: string
  hubspotId?: string
  hubspotLastSyncDate?: Date
}

export interface IBankAccount
  extends mongoose.Document,
    IMongoose,
    Omit<IBankAccountModel, '_id' | 'finicity' | 'plaid'> {
  id: string
  accountNumber: IEncrypted | string
  cardMetadata: any
  accountType: string
  giact: any
  ew:
    | {
        answer?: string
        error?: {
          code: number
          message: string
        }
        result?: {
          status: string
        }
      }
    | null
    | undefined
  finicity?: {
    syncState?: number
    accountId?: string
  }
  plaid?: {
    account_id?: string
    item_id?: string
    institution_id?: string
    access_token?: any
  }
}

export interface IInvoiceLoanPlan {
  minAmount: number
  maxAmount?: number
  plans: [string]
}

export interface IInvoiceReceiver {
  receivers: any[]
  link: string
}

export type CustomerSuppliers = Partial<ICompany> & {
  customerType: CustomerAccountType
}

export interface ICompany
  extends mongoose.Document,
    IMongoose,
    Omit<ICompanyModel, 'id' | '_id'> {
  ein: IEncrypted | string
  bankAccounts?: IBankAccount[]
  type: 'supplier' | 'contractor'
  settings: {
    approveRead?: boolean
    acceptAchPayment?: boolean
    achDelayDisabled?: boolean
    accountAdded?: boolean
    canUploadInvoice?: boolean
    canPostTransactions: boolean
    canEditAuthorization: boolean
    sendFinalPaymentWhenLoanIsPaid: boolean
    tutorialViewed?: boolean
    welcomeViewed?: boolean
    cardPricingPackageId?: string
    loanPricingPackageId?: string
    lastPurchaseDate?: Date
    onboardingType?: string
    businessType?: string
    OCRModelId?: string
    dueDay?: number
    loanPlans?: Array<string>
    supplierCanPay?: boolean
    defaultDebtInvestorTradeCredit?: string
    invitedBy?: string
    repayment?: {
      repays: 'borrower' | 'supplier'
      nameOnAccount: string
      routingNumber: string
      accountNumber: string
      autoApprove?: boolean
      paymentPlan: string
      maxInvoiceAmount: number
    }
    arAdvance: IArAdvanceSettings
    depositDetails?: IDepositDetailsSettings
    directTerms?: IDirectTermsSettings
    downPaymentDetails?: IDownPaymentSettings
    inHouseCredit?: IInHouseCreditCompanySettings
    automatedDrawApproval: IAutomatedDrawApprovalSettings
    customerSettings?: {
      source?: string
      sourceCampaign?: string
      startLOCAfterSignUp?: boolean
    }
    email: {
      senderEmail?: string
      sendInvoiceTemplate?: string
      sendInHouseCreditInvitationTemplate?: string
      sendInvitationTemplate?: string
      logoUrl?: string
    }
    achDiscount?: {
      percentage: number
      validityInDays: number
    }
    test?: boolean
    invoiceLoanPlans: IInvoiceLoanPlan[]
  }
  tabapay: any
  finicity?: {
    customerId?: string
    lastImport?: number
    transactionImportCompleted?: boolean
    transactionsImportedCount: number
  }
  plaid?: {
    assetsReportStatus?: Required<
      Pick<PlaidAssetsReportStatusResponse, 'reportRequestId'>
    > &
      Pick<
        PlaidAssetsReportStatusResponse,
        'daysAvailableFrom' | 'daysAvailableTo'
      >
  }
  cbw?: any
  onBoarding: any
  draft: any
  credit: ICompanyCreditInfo
  connectorReferences?: ICompanyConnectorReference[]
  newDECreditApplicationExecutionArn?: string
}

export interface IOperation
  extends mongoose.Document,
    Omit<IOperationModel, 'date' | 'id'> {
  transactions?: ITransaction[]
  date: Date
}

export interface ITransaction
  extends mongoose.Document,
    IMongoose,
    ITransactionModel {}

export interface IInvoiceDetails extends IInvoiceDetailsModel {}

export interface ILoanApplicationMetadata {
  creditPolicyVersion: string
  supplierName: string
  routingNumber: string
  accountNumber: string
  loanPackage: ILoanPriceModel & { name?: string }
  paymentPlan: ILoanPaymentPlanModel
  payment_cancelled?: boolean
  payment_cancelled_by?: string
  payment_cancelled_date?: Date
  skip_final_payment?: boolean
  fundingSources: { name: string }[]
  deductFeeFromFinalPayment?: boolean
  repayment: {
    autoTradeCreditEnabled?: boolean
    disbursementPause?: {
      enabled?: boolean
      updatedBy?: string
      updatedAt?: Date
    }
    autoTradeCreditType?: 'all-customers' | 'selected-customers'
    loanPaymentCollection?: 'borrower' | 'supplier'
  }
}

export interface ILoanApplication
  extends mongoose.Document,
    IMongoose,
    Omit<ILoanApplicationModel, 'invoiceDetails'> {
  invoiceDetails: IInvoiceDetails
  fundingSource?: 'arcadia' | 'cbw'
  useDate?: ''
  prevOutputs?: any[]
  metadata?: Partial<ILoanApplicationMetadata>
  agreementTemplateId?: string
  company?: ICompany
}

export interface IUserRole extends IMongoose, IUserRoleModel {
  company?: ICompany
  user?: IUser
}

export interface ISettings extends IMongoose {
  key: string
  value: any
}

export interface IAccountingSettings
  extends IMongoose,
    Omit<IAccountingSettingsModel, 'id' | 'pdfUrls'> {
  pdfUrls: string[]
}

export interface ICompanyConnectorReference {
  integrationId: string
  customerId: string
}

export interface IInvoice
  extends mongoose.Document,
    Omit<
      IInvoiceModel,
      'id' | 'total_amount' | 'supplierInvitationDetails' | 'customer'
    >,
    IMongoose {
  total_amount: number
  company: ICompany
  supplierInvitationDetails: ISupplierInvitationDetailsModel
  project?: any
  connector: IConnectorModel
  seen: boolean
  operation?: IOperation
  customer: ICustomerAccount
}

export interface ICustomerAccount
  extends mongoose.Document,
    Omit<ICustomerAccountModel, 'id' | 'bankAccounts' | 'settings'> {
  user?: IUser | null
  parent_id?: string
  connector?: any
  bankAccounts?: IBankAccount[] | null
  settings: ICustomerSettings
}

export interface ILoanPricingPackage
  extends mongoose.Document,
    Omit<ILoanPricingPackageModel, 'metadata'> {
  _id: string
  metadata: ILoanPriceModel & mongoose.Document
}

export interface ILoanPaymentPlan
  extends mongoose.Document,
    ILoanPaymentPlanModel {
  _id: string
}

export interface IInvitation
  extends mongoose.Document,
    IMongoose,
    IInvitationModel {}

export interface IExperianRequest extends mongoose.Document, IMongoose {
  bin_hash: string
  requestType: string
  data: any
  inputData: any
}

export interface IInstitution extends mongoose.Document, IInstitutionModel {}

export interface ISettings
  extends mongoose.Document,
    IMongoose,
    ISettingsModel {}

export interface IBranding extends mongoose.Document, IBrandingModel {}

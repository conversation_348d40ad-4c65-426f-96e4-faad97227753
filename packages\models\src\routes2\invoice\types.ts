import { invoiceStatus } from '../../dictionaries'
import { IResponse } from '../types'

export type InvoiceStatus = typeof invoiceStatus[keyof typeof invoiceStatus]

export interface IInvoiceFilters {
  category: 'receivable' | 'payable'
  status?: InvoiceStatus | 'EXCEPT_DRAFT'
  project_id?: string | null
}

export interface Package {
  fee: number
  invoiceAmount: number
  totalAmount: number
  pricingPackage: {
    merchant: {
      percentage: number
      min: number
      max: number
    }
    customer: {
      percentage: number
      amount: number
    }
  }
  discount?: number
}

export interface PricingPackageResponse extends IResponse {
  pricingPackage?: Package
  lateFee?: number
  customerFee?: number // fees do not related to pricing package, corresponding method to be replaced with getInvoicePaymentInfo/Options, which would return fees, invoice itself, package etc.
}

export interface PricingPackageRequest {
  companyId?: string
  accountId: string
  invoiceIds?: string[]
  totalAmount?: string | number
}

export interface PricingPackageForIntegrationRequest {
  companyId: string
  accountId: string
  totalAmount: string | number
}

export interface PaymentRequest {
  invoiceIds: string[]
  paymentMethod: string
  accountId: string
  account_id: string
  requestedAmount?: number
  autoDebit?: boolean
}

export interface StatementRequest {
  year: string | number
  month: string | number
}

export interface StatementResponse extends IResponse {
  downloadLink: string
}

export interface InvoiceProjectResponce {
  data: any
}

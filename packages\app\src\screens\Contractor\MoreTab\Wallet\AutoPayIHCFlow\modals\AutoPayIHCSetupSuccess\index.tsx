import React from 'react'
import { StyleSheet, View, Text } from 'react-native'
import Button from '../../../../../../IntegrationPayment/RegularIntegration/components/Button'
import { useTranslation, Trans } from 'react-i18next'
import BuilderBottomModal from '../../../../../../../ui/molecules/BuilderBottomModal'
import { IPaymentMethod } from '../../hooks/useHandleAutoPayIHCFlow'
import { AutoPayIHCMode } from '../ChooseAutoPayIHCPaymentMethod/components'
import { useResponsive } from '@linqpal/components/src/hooks'
import Spacer from '../../../../../../../ui/atoms/Spacer'
import { IconCheckDone } from '../../../../../../../assets/icons'

interface AutoPayIHCSetupSuccessProps {
  onClose: () => void
  paymentMethod?: IPaymentMethod | null
  mode?: AutoPayIHCMode
}

export const AutoPayIHCSetupSuccess: React.FC<AutoPayIHCSetupSuccessProps> = ({
  onClose,
  paymentMethod,
  mode = AutoPayIHCMode.Enable,
}) => {
  const { t } = useTranslation('global')
  const { sm } = useResponsive()
  const getDisplayText = (method: any) => {
    const isCard = method?.paymentMethodType === 'card'

    if (isCard) {
      return t('autoPayIHCFlow.success.cardTitle', {
        typeOfCard: method?.cardMetadata?.type ?? '',
        accountNumber: method?.cardMetadata?.lastFour ?? '****',
      })
    }
    return t('autoPayIHCFlow.success.bankAccountTitle', {
      accountNumber: method?.accountNumber?.slice(-4) ?? '****',
    })
  }

  const renderSubtitle = (translationKey: string) => {
    return (
      <Trans
        i18nKey={translationKey as any}
        t={t}
        components={{
          bold: <Text style={styles.boldText} />,
        }}
        values={{
          paymentMethod: getDisplayText(paymentMethod),
        }}
      />
    )
  }

  const updateConfig = {
    title: t('autoPayIHCFlow.success.updated.title'),
    subtitle: renderSubtitle('autoPayIHCFlow.success.updated.subtitle'),
    additionalText: t('autoPayIHCFlow.success.updated.additionalInfo'),
  }

  const modeConfig = {
    [AutoPayIHCMode.Enable]: {
      title: t('autoPayIHCFlow.success.enabled.title'),
      subtitle: renderSubtitle('autoPayIHCFlow.success.enabled.subtitle'),
      additionalText: t('autoPayIHCFlow.success.enabled.additionalInfo'),
    },
    [AutoPayIHCMode.UpdateAutoPayNotRequired]: updateConfig,
    [AutoPayIHCMode.UpdateAutoPayRequired]: updateConfig,
  }
  const currentConfig = modeConfig[mode]
  const { title, subtitle: subtitleText, additionalText } = currentConfig

  return (
    <BuilderBottomModal
      visible={true}
      onClose={onClose}
      height={sm ? 470 : '100%'}
      width={sm ? 600 : undefined}
      paddingHorizontal={0}
      footer={
        <View style={sm ? styles.footer : styles.mobileFooter}>
          <Button
            buttonStyle={sm ? styles.desktopButton : styles.mobileButton}
            textStyle={styles.desktopButtonText}
            onPress={onClose}
            testID="done_setup_auto_pay_btn"
            label={t('autoPayIHCFlow.success.doneButton')}
          />
        </View>
      }
    >
      <View
        style={
          sm
            ? styles.alertContainer
            : [styles.alertContainer, styles.mobileAlertContainer]
        }
      >
        <View
          style={
            sm
              ? styles.iconContainer
              : [styles.iconContainer, styles.mobileIconContainer]
          }
        >
          <IconCheckDone width={72} height={72} />
        </View>
        <Spacer height={sm ? 25 : 35} />
        <Text style={sm ? styles.title : [styles.title, styles.mobileTitle]}>
          {title}
        </Text>
        <Spacer height={sm ? 15 : 8} />
        <Text style={sm ? styles.text : [styles.text, styles.mobileText]}>
          {subtitleText}
        </Text>
        <Spacer height={20} />
        <Text style={sm ? styles.text : [styles.text, styles.mobileText]}>
          {additionalText}
        </Text>
      </View>
    </BuilderBottomModal>
  )
}

const styles = StyleSheet.create({
  mobileIconContainer: {
    marginTop: -120,
  },
  iconContainer: {
    alignItems: 'center',
    marginTop: -10,
  },
  alertContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 30,
  },
  mobileAlertContainer: {
    paddingHorizontal: 20,
  },
  footer: {
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mobileFooter: {
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopColor: '#DEE5EB',
    borderTopWidth: 1,
    paddingHorizontal: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    lineHeight: 32,
    fontFamily: 'Inter',
    textAlign: 'center',
    color: '#19262F',
  },
  mobileTitle: {
    fontSize: 18,
    lineHeight: 24,
    letterSpacing: -0.3,
  },
  text: {
    fontSize: 16,
    lineHeight: 24,
    fontFamily: 'Inter',
    fontWeight: '500',
    textAlign: 'center',
    paddingHorizontal: 20,
    color: '#19262F',
  },
  mobileText: {
    lineHeight: 20,
    fontWeight: '500',
    paddingHorizontal: 0,
  },
  mobileButton: {
    width: '100%',
    borderRadius: 8,
  },
  desktopButton: {
    height: 48,
    width: 89,
    alignSelf: 'center',
  },
  desktopButtonText: {
    fontSize: 14,
    lineHeight: 24,
    fontWeight: '700',
    fontFamily: 'Inter',
    letterSpacing: 0,
  },
  boldText: {
    fontWeight: '700',
  },
})

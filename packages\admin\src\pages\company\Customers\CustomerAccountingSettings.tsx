import {
  CButton,
  CFormGroup,
  CInput,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
} from '@coreui/react'
import {
  IAccountingSettingsModel,
  ICompanyModel,
  routes,
} from '@linqpal/models'
import { AccountingSettingsStatus } from '@linqpal/models/src/dictionaries'
import { observer } from 'mobx-react'
import React, { useCallback, useEffect, useState } from 'react'
import { DirectTermSettings } from './DirectTermSettings'
import { ICustomerCompanySettings } from '@linqpal/models/src/types/routes'
import { DepositDetailsSettings } from './DepositDetailsSettings'
import { AutoPayIHCSettings } from './AutoPayIHCSettings'

interface IProps {
  company: ICompanyModel & { accountingSettings?: IAccountingSettingsModel } & {
    settings?: any
  }

  onClose(): void
  onChangeSettings(): void
}

export default observer(function CustomerAccountingSettings(props: IProps) {
  const { company, onClose, onChangeSettings } = props
  const {
    email: _email = '',
    credit_expire_days: _credit_expire_days = 0,
    status,
  } = company?.accountingSettings || {}

  const [companySettings, setCompanySettings] =
    useState<ICustomerCompanySettings>(
      company.settings
        ? {
            canUploadInvoice: company.settings.canUploadInvoice ?? false,
            directTerms: company.settings.directTerms ?? null,
            downPaymentDetails: company.settings.downPaymentDetails ?? null,
            depositDetails: company.settings.depositDetails ?? null,
            inHouseCredit: company.settings.inHouseCredit ?? null,
          }
        : {
            canUploadInvoice: false,
            directTerms: null,
            downPaymentDetails: null,
            depositDetails: null,
            inHouseCredit: null,
          },
    )

  const [email, setEmail] = useState(_email)
  const [credit_expire_days, setCreditExpireDays] =
    useState(_credit_expire_days)

  useEffect(() => setEmail(_email), [_email])

  useEffect(
    () => setCreditExpireDays(_credit_expire_days),
    [_credit_expire_days],
  )

  const onChangeEmail = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => setEmail(e.target.value),
    [],
  )

  const onChangeCreditExpireDays = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) =>
      setCreditExpireDays(e.target.valueAsNumber),
    [],
  )

  const isValid = () => {
    const isDepositDetailsValid =
      !companySettings.depositDetails?.isSecured ||
      companySettings.depositDetails.depositAmount > 0

    const isDownPaymentValid =
      !companySettings.downPaymentDetails?.isRequired ||
      (companySettings.downPaymentDetails?.downPaymentPercentage > 0 &&
        companySettings.downPaymentDetails?.downPaymentPercentage <= 100)

    const isDirectTermValid =
      !companySettings.canUploadInvoice ||
      (!!companySettings.directTerms?.loanPlans?.length && isDownPaymentValid)

    return isDepositDetailsValid && isDirectTermValid
  }

  const approve = useCallback(() => {
    const { accountingSettings } = company
    Promise.all([
      routes.admin.saveCustomerAccountingSettings(company?.id, {
        ...accountingSettings,
        email,
        credit_expire_days,
        status: AccountingSettingsStatus.APPROVED,
      }),
      routes.admin.saveCustomerCompanySettings(company.id, companySettings),
    ])
      .then(onChangeSettings)
      .then(onClose)
  }, [
    company,
    email,
    credit_expire_days,
    companySettings,
    onChangeSettings,
    onClose,
  ])

  return (
    <CModal show={!!company?.id} onClose={onClose} size="lg">
      <CModalHeader>
        <CModalTitle>{company?.name} - Accounting Settings</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <DepositDetailsSettings
          settings={companySettings.depositDetails}
          onUpdate={(val) => {
            console.log('here')
            setCompanySettings({ ...companySettings, depositDetails: val })
          }}
        />
        <DirectTermSettings
          settings={companySettings}
          onUpdate={(val) => setCompanySettings({ ...companySettings, ...val })}
        />

        <AutoPayIHCSettings
          settings={companySettings}
          onUpdate={(val) => setCompanySettings({ ...companySettings, ...val })}
        />

        <div
          style={{
            height: '1px',
            backgroundColor: '#E1E1E1',
            marginBottom: '14px',
          }}
          className="flex-grow-1"
        />

        <CFormGroup>
          <CLabel>Email</CLabel>
          <CInput value={email} onChange={onChangeEmail} />
        </CFormGroup>
        <CFormGroup>
          <CLabel>Credit expire days</CLabel>
          <CInput
            type="number"
            value={credit_expire_days}
            onChange={onChangeCreditExpireDays}
            min={0}
            step={1}
          />
        </CFormGroup>
        <CFormGroup>
          <CLabel>Status</CLabel>
          <CInput value={status} readOnly />
        </CFormGroup>
      </CModalBody>
      <CModalFooter>
        <CButton onClick={onClose}>Close</CButton>
        <CButton color="primary" onClick={approve} disabled={!isValid()}>
          Approve
        </CButton>
      </CModalFooter>
    </CModal>
  )
})

import React, { useCallback, useEffect } from 'react'
import styled from 'styled-components'
import { ScrollView, View, StyleSheet } from 'react-native'
import { SafeAreaView } from 'react-native'
import { observer } from 'mobx-react'
import { BtText } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'

interface IPaymentMethodScreenWrapperProps {
  children: React.ReactNode
}

const BreadCrumbs = observer(() => {
  const { t } = useTranslation('global')
  return (
    <View style={styles.breadcrumbsContainer}>
      <BtText style={styles.breadcrumbText}>{t('Menu.Accounts')}</BtText>
    </View>
  )
})

const TopBar = observer(() => {
  const { t } = useTranslation('global')
  return (
    <View style={styles.topBarView}>
      <View style={styles.topBar}>
        <BreadCrumbs />
        <BtText style={styles.topBarTitle}>{t('Wallet.header')}</BtText>
      </View>
    </View>
  )
})

export const PaymentMethodScreenWrapper = observer(
  ({ children }: IPaymentMethodScreenWrapperProps) => {
    let scrollView: ScrollView | null = null
    const toTopScroller = true

    const scrollToTop = useCallback(() => {
      if (!scrollView) return
      scrollView.scrollTo({ x: 0, y: 0, animated: true })
    }, [scrollView])

    useEffect(() => {
      scrollToTop()
    }, [toTopScroller, scrollToTop])

    return (
      <SafeAreaView style={styles.rootView}>
        <TopBar />
        <ScrollView
          style={[{ flex: 1 }]}
          contentContainerStyle={{ flexGrow: 1 }}
          ref={(ref) => {
            scrollView = ref
          }}
        >
          <View style={{ flex: 1 }}>
            <Wrapper>{children}</Wrapper>
          </View>
        </ScrollView>
      </SafeAreaView>
    )
  },
)

const Wrapper = styled.div`
  margin: 24px 36px 31px 36px;
  font-family: 'Inter', sans-serif;
  flex: 1;
  display: flex;
  flex-direction: column;
`

const styles = StyleSheet.create({
  topBarView: {
    height: 77,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: '#E6EBEE',
    paddingHorizontal: 36,
  },
  rootView: {
    backgroundColor: 'white',
    flex: 1,
    width: '100%',
  },
  topBar: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-around',
    alignItems: 'flex-start',
    height: 52,
  },
  topBarTitle: {
    fontSize: 16,
    fontWeight: '700' as const,
    lineHeight: 22.4,
    color: '#19262F',
    fontFamily: 'Inter',
  },
  breadcrumbsContainer: {
    flexDirection: 'row',
  },
  breadcrumbText: {
    fontFamily: 'Inter',
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 18,
    color: '#758590',
  },
})

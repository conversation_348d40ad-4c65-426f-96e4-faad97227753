{"GetPaidApplication": "Get Paid Application", "CreditRequest": "Credit Request", "IhHouseCreditForm": "Credit Form", "InHouseCreditApplication": "{{- supplierName}} Credit Form", "SetUpAccount": "Set Up Account", "BusinessDetails": "Business Details", "BusinessFinancialDetails": "Business Financial Details", "BusinessOwner": "Business Owner", "BusinessCoOwners": "Business Co-owners", "BankDetails": "Business Bank Details", "ToComplyWithFederalLaws": "To comply with federal laws, BlueTape needs to collect some information to verify your business. Your credit will not be affected and your information is safe and secure with us.", "BySubmittingYourApplication": "By submitting your application you verify that all the information you provided is complete and correct. You further agree to provide written authorization to verify your business information and agree with <BCA>BlueTape Customer Agreement for Seller</BCA>, <GSA>Guaranty and Security Agreement</GSA> and <IPA>Invoice Purchase and Security Agreement.</IPA>", "TermsAndConditionsMessage": "By continuing, you agree to our <terms>Terms & Conditions</terms>, <privacy>Privacy Policy</privacy>, <PGA>Commercial Sale Personal and Corporate Guaranty Agreement</PGA> and <TCA>BlueTape Commercial Installment Sale Account Materials and Services Agreement</TCA>. You understand that by clicking on the “AGREE & SUBMIT” button immediately following this notice you are providing ‘written instruction’ to BlueTape under Regulation B (Equal Credit Opportunity Act) authorizing BlueTape to obtain information from your business, personal credit profile, or other information from credit bureaus. You authorize BlueTape to obtain Such information solely to prequalify your business for business credit.", "InHouseCreditConsentStatement": "By continuing, you agree to our <terms>Terms & Conditions</terms>, and <privacy>Privacy Policy</privacy>. You understand that by clicking on the “AGREE & SUBMIT” Button immediately following this notice you are providing ‘written instruction’ to BlueTape under the Fair Credit Reporting Act authorizing BlueTape to obtain information from your personal credit profile or other information from credit bureaus. You authorize BlueTape to obtain such information solely to prequalify your business for business credit.", "Back": "Back", "SkipForNow": "Skip for now", "Next": "Next", "View": "View", "Skip": "<PERSON><PERSON>", "Show": "Show", "Hide": "<PERSON>de", "Close": "Close", "Continue": "Continue", "GoToReviewPage": "Go to review page", "PageNotFound": "Page Not Found", "Business": {"Name": "What is the registered business name?", "NameLabel": "Business Name", "NameDescription": "Please enter the legal name this business was registered with.", "DbaLabel": "DBA", "DbaTitle": "If you operate under a DBA, please enter it:", "Category": "What describes this business category?", "CategoryLabel": "Business Category", "CategoryPlaceholder": "Select Category", "CityLabel": "City", "Email": "What's your email address?", "EmailLabel": "Email Address", "Trade": "What's the trade or specialty?", "TradeDescription": "You can select multiple categories", "TradePlaceholder": "Select Trade or Specialty", "TradeLabel": "Trade or Speciality", "Phone": "What is the business phone number?", "PhoneLabel": "Business Phone Number", "Address": "What is the business legal address?", "AddressDescription": "Please enter the address your business was registered with.", "Start": "When was the business registered?", "StartLabel": "Business Registration Date", "StateLabel": "State", "HomeAddressLabel": "Home Address", "StreetAddressLabel": "Street Address", "Type": "What type of entity is this business?", "TypePlaceholder": "Select Type of Entity", "TypeLabel": "Type of Entity", "OtherTypeLabel": "Enter type of entity", "TaxId": "What is the business Tax ID? (e.g. EIN, TIN)", "TaxIdLabel": "Tax ID (e.g. EIN, TIN)", "TaxIdPlaceholder": "Enter your Tax ID", "TaxIdEncrypted": "Encrypted & Secure Data", "TaxIdShow": "Show", "TaxIdHide": "<PERSON>de", "TaxIdInvalid": "Please enter the following format XXXXXXXXX.", "ZipLabel": "ZIP Code"}, "Finance": {"AnnualRevenue": "What is the annual revenue of the business?", "RevenueLabel": "Annual Revenue", "Debt": "How much debt does the business currently have?", "DebtLabel": "Debt", "ArAdvanceRequestedLimit": "How much In House Credit do you want to offer to your customers?", "ArAdvanceRequestedLimitDescription": "What’s the total amount of ARs you’d like to accelerate with the BlueTape factoring program?", "ArAdvanceRequestedLimitLabel": "Please, enter amount", "Authorised": "Are you an authorized signer for this business?", "AuthorizedReviewTitle": "Authorized signer of the business", "AuthDetailsReviewTitle": "Authorised signer details", "Owner": "Are you the business owner or a significant shareholder?", "OwnerReviewTitle": "Owner of the business", "OwnerLabelYes": "Yes", "OwnerLabelNo": "No", "Credit": "How much credit is this business requesting from BlueTape?", "CreditLabel": "BlueTape Credit Request", "AddressLabel": "home address", "SSNlabel": "Social Security Number", "FirstnameLabel": "First Name", "LastnameLabel": "Last Name", "PhoneLabel": "Phone Number", "PhonePlaceholder": "Enter phone number", "EmailLabel": "Email Address", "AuthorisedDetails": "Invite the authorized representative of this business.", "AuthorisedInfo": "This person will be invited to submit the application. Please make sure you’re providing correct data.", "FirstNameError": "Invalid first name", "LastNameError": "<PERSON>valid last name"}, "Owner": {"OwnerPercentage": "What percentage of the business do you own?", "PercentageLabel": "Ownership Percentage", "HomeAddress": "What is your home address?", "Birthdate": "What is your date of birth?", "SSN": "What is your Social Security Number?", "AuthorizedHomeAddress": "What is the home address of the authorized user?", "AuthorizedBirthdate": "What is the date of birth of the authorized user?", "AuthorizedSSN": "What is the Social Security Number of the authorized user?", "BirthdayLabel": "Date of Birth", "AuthorisedLabelYes": "Yes, I am", "AuthorisedLabelNo": "No, I'm not"}, "CoOwners": {"Title": "Add all co-owners with 25% or more ownership:", "CoOwnerDetailsTitle": "Tell us more about the co-owner", "OwnerPercentagePostfix": "Ownership", "TotalPercentage": "Total Ownership Percentage - ", "Edit": "Edit", "Delete": "Delete", "AddCoOwner": "Add Co-owner", "OwnershipPercentage": "Ownership Percentage", "IndividualOwner": "Individual Owner", "EntityOwner": "Entity", "EntityName": "Entity Name", "FirstName": "First Name", "LastName": "Last Name", "AuthorizedRepresentativeFirstName": "First Name of Authorized Representative", "AuthorizedRepresentativeLastName": "Last Name of Authorized Representative", "HomeAddress": "Home Address", "EntityAddress": "Entity Legal Address", "Birthday": "Date of Birth", "SSN": "SSN", "EIN": "Tax ID (e.g. EIN, TIN)", "PhoneNumber": "Phone Number", "EmailAddress": "E-mail Address", "AgreementWarning": "<styled>All co-owners will be asked to follow the link and agree to the Terms & Conditions.</styled> Notification will be sent to the co-owner by email. Please make sure the details are entered correctly. ", "Save": "Save", "Cancel": "Cancel"}, "Bank": {"GetPaid": {"None": {"Title": "Add your business bank account to receive funds.", "Description": "<bold>Note:</bold> This account will also be used to charge any applicable automated subscription fees. Make sure to select the correct <bold>business</bold> account you want to use for both receiving payments and paying fees."}, "Selected": {"Title": "Log in to your bank account for an expedited process.", "Description": "Complete this step for a faster approval process and to receive customer payments promptly. Failure to do so may result in delays."}, "Connected": {"Title": "Congratulations, your business bank account(s) are connected.", "Description": "Connected accounts are shown below."}, "SearchBankLabel": "Start typing to find and connect your business bank"}, "IHC": {"None": {"Title": "Add a Payment Method for Invoice Payment", "Description": "Connect or add a bank account now, you can turn on Auto Pay later when you're ready.\nSome invoices may require Auto Pay in the future.", "Benefits": {"0": "Secure and convenient, pay invoices directly from your bank", "1": "Helps avoid late or missed payments when you enable Auto Pay", "2": "Update or change your payment method anytime", "3": "Add a card later if you prefer"}}, "Selected": {"Title": "Log in to your bank account for an expedited process.", "Description": "For faster approval and higher credit limit consideration, connect all your financial institutions and bank accounts. Failure to do so may result in delays."}, "Connected": {"Title": "Congratulations, your business bank account(s) are connected.", "Description": "Connected accounts are shown below."}}, "LOC": {"None": {"Title": "Connect the bank account(s) associated with your business.", "Description": "We need to review your business cash flow and transaction history for underwriting purposes."}, "Selected": {"Title": "Log in to your bank account for an expedited process.", "Description": "For faster approval and higher credit limit consideration, connect all your financial institutions and bank accounts. Failure to do so may result in delays."}, "Connected": {"Title": "Congratulations, your business bank account(s) are connected.", "Description": "Connected accounts are shown below."}}, "DetailsPlaceHolder": "Search for bank name", "SearchBankLabel": "Search Bank", "SearchAnotherBankTitle": "Or search one:", "LoginToBankAccount": "Log in to bank account", "EnterBankDetailsManually": "Enter bank details manually", "ConfirmationLoanTitle": "If you skip you will require to submit most recent tax return, minimum 6 months of bank statement and $100 account setup fee maybe applied.", "ConfirmationLoanSubTitle": "In order to avoid it, please connect your bank account", "ConfirmationTitle": "This may delay your account approval and payment processing", "ConfirmationSubTitle": "For faster approval go back and connect your account", "ConfirmationContinue": "Continue", "ConfirmationGoBack": "Go back", "Erp": "What ERP or accounting software does your business use?", "ErpDescription": "You can select multiple categories", "ErpPlaceholder": "Select ERP or accounting software", "ErpLabel": "ERP or accounting software", "BankName": "Bank Name", "BankNameInvalid": "Invalid bank name", "HolderName": "Account Holder Name", "HolderNameInvalid": "Invalid account holder name", "AccountNumber": "Bank Account Number", "AccountNumberInvalid": "Invalid account number", "RoutingNumber": "Routing Number", "RoutingNumberInvalid": "Invalid routing number", "BusinessChecking": "Business checking", "BusinessSaving": "Business saving", "BankListLoanSelectTitle": "Select a primary account for your loan repayment.", "BankListGetPaidSelectTitle": "Select a primary account for receiving payments.", "BankListLoanSelectDescription": "This account will be used for autodebit.", "BankListGetPaidSelectDescription": "This account will be used as primary.", "Connected": "Connected", "SavingAccount": "Saving account", "CheckingAccount": "Checking account", "UnsupportedAccountsWarning": "The account type you have selected is not correct. Please select a checking account and / or savings account.", "ConnectedViaOnlineBanking": "Securely Connected", "Disconnected": "Disconnected - <PERSON><PERSON> to try again", "SkipConnectTitle": "Are you sure?", "SkipConnectDescription": "Entering your business bank details manually will delay your account approval and set up. For a faster approval process, and to receive customer payments promptly, please connect your business bank account(s).", "SkipConnectLoanDescription": "If you skip you will require to submit most recent tax return, minimum 6 months of bank statement and $100 account setup fee maybe applied. In order to avoid it, please connect your bank account", "SkipConnectEnterManually": "Enter bank details manually", "SearchBank": "Search Bank", "AddManually": "Add Manually", "SkipConnectLoginToBank": "Log in to bank account", "CancelManual": "Cancel", "AcceptManual": "Add Account", "SelectedAsPrimaryCaption": "*Selected as Primary account", "DeleteManualButton": "Remove", "DeleteModalTitle": "Are you sure you want to delete bank account?", "DeleteModalDescription": "We will remove the account from the list.", "DeleteModalCancel": "Keep bank account", "DeleteModalCancelShort": "Keep account", "DeleteModalConfirm": "Delete bank account", "DeleteModalConfirmShort": "Delete account"}, "ValidationErrors": {"InvalidEmail": "Invalid email", "DuplicatedCoOwnerEmail": "This email has already been entered. Please provide a different email", "InvalidPhone": "Please enter a valid US phone number", "InvalidDate": "Invalid Date", "InvalidSSN": "Invalid SSN", "InvalidTaxID": "Please enter the following format XXXXXXXXX.", "InvalidMinimumPercentage": "The minimum % should be 25%.", "InvalidMaximumPercentage": "The remaining possible amount of % should not exceed {{maxPercent}}%."}, "Review": {"SectionTitle": "Review", "Heading": "You have filled in the following section of the application", "IncompleteSectionText": "You have blank fields of the form. Click edit to complete the section", "CompleteApplication": "Complete Application  🡪", "PersonalInformationPrompt": "Please provide us with your personal information to finish the application. \nAll data is protected by BlueTape Security System.", "InviteSent": "<simple>An invitation has been sent to <styled>{{name}}</styled> for an authorized signature.</simple>", "Edit": "Edit", "ReviewButton": "Review", "AgreeSubmit": "Agree & Submit", "Continue": "Continue"}, "Agreement": {"Heading": "Please, read the agreement and click on Agree & Submit button in the end of the page."}, "Preview": {"AuthorizedSignerTitle": "Authorized signer for this business", "OwnerTitle": "Business owner or a significant shareholder", "CoOwner": "Co-owner", "Owner": "Owner", "Percentage": "{{percentage}}% Ownership", "EIN": "Tax ID", "LegalAddress": "Legal Address", "AuthorizedRepresentative": "Authorized Representative", "Heading": "Please review your information before submitting your application"}, "CloseAlert": {"Title": "Are you sure?", "SaveForLater": "Save for later", "ContinueApplication": "Continue application", "Continue": "Continue", "CreditRequestMessage": "Your credit request is not complete and will not be submitted. Don't worry! Your information is saved and you can complete your request later.", "GetPaidApplicationMessage": "Your Get Paid application is not complete and will not be submitted. Don't worry! Your information is saved and you can complete your request later.", "InHouseCreditMessage": "Your {{- supplierName}} Credit Form is not complete and will not be submitted. Don't worry! Your information is saved and you can complete your request later."}, "LoCAgreementAlert": {"Title": "Great News!", "NewAgreementMessage": "We are excited to announce that we are launching our new Trade Credit program as business lines of credit. Beginning July 5th 2023, all BlueTape credit provided will be processed and governed under this <agreementLink>Line of Credit Agreement</agreementLink>", "UpdatedAgreementMessage": "We have updated our Line of Credit agreement. Please review the <agreementLink>Line of Credit Agreement</agreementLink>. If you have any questions please contact us <NAME_EMAIL>", "Agree&Continue": "Agree & Continue"}, "LoCAgreementAlertClose": {"Title": "Are you sure?", "Message": "In order to use BlueTape trade credit, you must agree to the new Line of Credit agreement.", "GoToHome": "Go to Home Page", "GoBack": "Go Back & Agree to Terms"}, "VerifyEmailAlert": {"Title": "Please verify your email to submit the application", "Message": "We’ve sent you a link to <styled>{{email}}</styled>", "SendAgain": "Send Again", "Ok": "Okay, Thanks"}, "CreditSubmitSuccessWithInvoice": {"title": "Draw request is processing", "message": "Thank you for submitting your draw request. Your request is currently under review by our underwriting team.\n\nIf additional information is required, we will contact you."}, "CreditSubmitSuccessNoInvoice": {"title": "Application Submitted", "message": "Thank you for submitting your application. Your application is processing based on the applicable underwriting process of the credit policy. We are reviewing and validating all data provided.\n\nThe underwriting team will review your application and will contact you if additional information is required. We will update your status soon, check back later."}, "GetPaidSubmittedSuccessAlert": {"title": "Application Submitted", "message": "We will review your application, if additional information is required, we will reach out. Otherwise, you will be notified of a decision soon."}, "InHouseCreditSubmittedAlert": {"title": "Thanks for creating your account!", "message": "We will reach out if we need any further details."}, "SubmitSuccessButton": "Okay, Thanks", "CreditRequestPaymentPlan": {"title": "Payment Plan", "mobileTitle": "Choose Payment Plan", "NoPaymentDueFirst30days": "<simple>No payment due <styled>first 30 days</styled><simple>", "Continue": "Continue"}, "ChooseAPaymentPlan": {"Heading": "Choose a Payment Plan", "SubHeading": "No payment due first 30 days, No fee if paid within 30 days"}}
import React from 'react'
import { StyleSheet, TouchableOpacity, View } from 'react-native'
import { BtText } from '@linqpal/components/src/ui'
import { ModalCloseIcon, BackIcon } from '../../../../../../../../assets/icons'
import { AddNewPaymentMethodMode } from '../index'
import { useTranslation } from 'react-i18next'
import useIsMobile from '../../../../../../PayablesTab/hooks/useIsMobile'

interface AddNewPaymentMethodHeaderProps {
  mode: AddNewPaymentMethodMode
  onClose?: () => void
  onBack?: () => void
}

export const AddNewPaymentMethodHeader: React.FC<AddNewPaymentMethodHeaderProps> =
  ({ mode, onClose, onBack }) => {
    const { t } = useTranslation('global')
    const isMobile = useIsMobile()

    const getModalTitle = () => {
      if (mode === AddNewPaymentMethodMode.AddFirstMethod) {
        return t('autoPayIHCFlow.addNewMethod.title')
      } else {
        return t('autoPayIHCFlow.addOrSelectPaymentMethod.addNewPaymentMethod')
      }
    }
    const showBackButton = mode === AddNewPaymentMethodMode.AddAdditionalMethod

    return (
      <View style={isMobile ? styles.mobileContainer : styles.container}>
        <View style={styles.header}>
          {showBackButton && onBack && (
            <TouchableOpacity onPress={onBack} style={styles.backButton}>
              <BackIcon width={40} height={40} />
            </TouchableOpacity>
          )}
          <View
            style={[
              styles.textContainer,
              !showBackButton && styles.textContainerFullWidth,
            ]}
          >
            <BtText
              style={[
                styles.title,
                isMobile && styles.mobileTitle,
                !showBackButton && styles.titleFullWidth,
              ]}
            >
              {getModalTitle()}
            </BtText>
          </View>
          <TouchableOpacity
            onPress={onClose}
            style={[
              styles.closeButton,
              !showBackButton && { alignSelf: 'flex-start' },
            ]}
          >
            <ModalCloseIcon width={40} height={40} />
          </TouchableOpacity>
        </View>
        {mode === AddNewPaymentMethodMode.AddFirstMethod && (
          <BtText style={[styles.subtitle, isMobile && styles.mobileSubtitle]}>
            {t('autoPayIHCFlow.addNewMethod.subtitle')}
          </BtText>
        )}
      </View>
    )
  }

const styles = StyleSheet.create({
  container: {
    width: 540,
    marginBottom: 16,
  },

  mobileContainer: {
    width: '100%',
    marginLeft: -30,
    paddingHorizontal: 18,
    marginTop: 5,
    marginBottom: 15,
  },
  header: {
    flexDirection: 'row',
    display: 'flex',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginBottom: 10,
  },
  backButton: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  textContainer: {
    flex: 1,
    maxWidth: '80%',
  },
  textContainerFullWidth: {
    maxWidth: '85%',
  },
  closeButton: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  title: {
    fontFamily: 'Inter',
    fontWeight: '700',
    fontSize: 24,
    lineHeight: 34,
    letterSpacing: -0.5,
    textAlign: 'center',
    color: '#001929',
  },
  titleFullWidth: {
    textAlign: 'left',
  },
  mobileTitle: {
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: -0.3,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 24,
    color: '#001929',
    letterSpacing: -0.5,
    textAlign: 'left',
    maxWidth: 520,
    marginLeft: 10,
  },
  mobileSubtitle: {
    marginLeft: 0,
  },
})

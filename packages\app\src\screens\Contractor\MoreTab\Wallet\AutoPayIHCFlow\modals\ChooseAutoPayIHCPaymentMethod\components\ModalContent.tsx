import React from 'react'
import { StyleSheet, View } from 'react-native'
import { ProcessingFeeNote } from './ProcessingFeeNote'
import { PaymentMethodDropdown } from './PaymentMethodDropdown'
import { BankAccountPaymentMethods } from '@linqpal/models/src/dictionaries'
import { Spacer } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'
import { AddNewPaymentLink } from '../../../../../../../IntegrationPayment/InvoiceDetails/components/PayFacoringInvoiceModal/components/AddNewPaymentLink'
import { calculateProcessingFee } from '../utils/calculateProcessingFee'
import useIsMobile from '../../../../../../PayablesTab/hooks/useIsMobile'

export enum AutoPayIHCMode {
  Enable = 'enable',
  UpdateAutoPayNotRequired = 'update_not_required',
  UpdateAutoPayRequired = 'update_required',
}

interface ModalContentProps {
  paymentMethods: any[]
  selectedPaymentMethod: any | null
  onSelectPaymentMethod: (paymentMethod: any) => void
  onAddNewPaymentMethod: () => void
}

export const ModalContent = ({
  paymentMethods,
  selectedPaymentMethod,
  onSelectPaymentMethod,
  onAddNewPaymentMethod,
}: ModalContentProps) => {
  const { t } = useTranslation('global')
  const isMobile = useIsMobile()
  const showProcessingFeeNote =
    selectedPaymentMethod &&
    selectedPaymentMethod.paymentMethodType !== BankAccountPaymentMethods.Bank

  return (
    <View style={isMobile ? styles.mobileContainer : styles.container}>
      <View style={styles.contentContainer}>
        <PaymentMethodDropdown
          paymentMethods={paymentMethods}
          selectedPaymentMethod={selectedPaymentMethod}
          onSelect={onSelectPaymentMethod}
        />

        <AddNewPaymentLink
          onPress={onAddNewPaymentMethod}
          label={t('autoPayIHCFlow.addNewPaymentMethod')}
        />

        {showProcessingFeeNote && (
          <ProcessingFeeNote
            processingFee={calculateProcessingFee(selectedPaymentMethod)}
          />
        )}
      </View>

      <Spacer height={24} />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    width: 540,
    alignSelf: 'center',
  },
  mobileContainer: {
    marginTop: 6,
    width: '100%',
    paddingHorizontal: 20,
  },
  contentContainer: {
    flex: 1,
  },
})

import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react-lite'
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { ScreenScrollWrapper } from '../Components/ScreenScrollWrapper'
import { TabPayablesNavigator } from '../Components/TabPayablesNavigator'
import { SingleVendor, Vendors } from '../Store/VendorsStore'
import { InvoicesList } from '../Tabs/Invoices/Invoices'
import { TabNavigatorStyles } from '../commonStyles'
import { TabNames } from '../enums'
import { Modal } from '../../CreditTab/MakePayment/Modal'
import { QuotesList } from '../Tabs/Quotes/Quotes'
import DataModel from '../../CreditTab/MakePayment/DataModel'
import {
  TradeCreditOverview,
  ValueKind,
} from '../../../TradeCredit/Components/TradeCreditOverview'
import { SupplierHeader } from '../Components/SupplierHeader'
import { css } from 'styled-components'
import { IconArrowBack } from '../../../../assets/icons'
import { dispatcher, pathFactory } from '../../../links'
import AutoPayIHCWarning from '../../../../ui/organisms/AutoPayIHCWarning/AutoPayIHCWarning'
import { AutoPayIHCFlow } from '../../MoreTab/Wallet/AutoPayIHCFlow'
import {
  useAutoPayIHCReducer,
  AutoPayIHCActionType,
} from '../../MoreTab/Wallet/AutoPayIHCFlow/useAutoPayIHCReducer'

interface Props {
  navigation: any
  route: any
  id
  supplierName
  supplierPhone
}

export const VendorDetails = observer(({ navigation }: Props) => {
  const { t } = useTranslation('payables')
  const [autoPayIHCState, dispatchAutoPayIHC] = useAutoPayIHCReducer()

  const store = SingleVendor
  const vendorsStore = Vendors

  const { id, supplierName, supplierPhone, supplierIsInvited } =
    store.currentVendor
  const isFactoring = store.isFactoring

  const { showMakePayment } = DataModel

  const onBackPress = () => {
    store.resetVendor()
    if (navigation.canGoBack()) {
      navigation.goBack()
    } else {
      const action = dispatcher(pathFactory('Console.Payables.home'))
      navigation.replace(action.name, action.params)
    }
  }

  useEffect(() => {
    store.refreshData()
    if (!isFactoring && !supplierIsInvited) {
      store.refreshQuotesData()
    }
  }, [store, isFactoring, id, supplierIsInvited])

  /*const onPaymentSuccess = () => {
    store.refreshData()
    if (isFactoring) {
      store.refreshQuotesData()
    }
  }*/

  const Tabs = {
    invoices: {
      name: TabNames.InvoicesTab,
      options: {
        title: t('payables.tabs.invoices'),
      },
      component: observer(() => (
        <ScreenScrollWrapper>
          <InvoicesList store={store} />
        </ScreenScrollWrapper>
      )),
    },
    quotes: {
      name: TabNames.QuotesTab,
      options: {
        title: t('payables.tabs.quotes'),
      },
      component: observer(() => (
        <ScreenScrollWrapper>
          <QuotesList store={store} />
        </ScreenScrollWrapper>
      )),
    },
  }

  const overviewItems = isFactoring
    ? [
        {
          label: t('payables.header.totalBalanceDue'),
          value: store.vendorsStatistics.totalDueSum,
          isLoading: store.isStatisticsLoading,
        },
        {
          label: t('payables.header.availableCredit'),
          value: store.vendorsStatistics.availableCredit,
          isLoading: store.isStatisticsLoading,
          tooltip: t('payables.header.availableCreditTooltip'),
        },
        {
          label: t('payables.header.inHouseCreditLimit'),
          value: store.vendorsStatistics.creditLimit || 100,
          isLoading: store.isStatisticsLoading,
        },
        {
          label: t('payables.header.numberOfDueInvoices'),
          value: store.vendorsStatistics.totalDueInvoices,
          isLoading: store.isStatisticsLoading,
          valueKind: ValueKind.Text,
        },
      ]
    : [
        {
          label: t('payables.header.totalBalanceDue'),
          value: store.vendorsStatistics.totalDueSum,
          isLoading: store.isStatisticsLoading,
        },
        {
          label: t('payables.header.numberOfDueInvoices'),
          value: store.vendorsStatistics.totalDueInvoices,
          isLoading: store.isStatisticsLoading,
          valueKind: ValueKind.Text,
        },
      ]
  if (!id || !supplierName) return null

  return (
    <>
      <View style={styles.overviewLayout}>
        {vendorsStore.vendorsLength > 1 && (
          <TouchableOpacity onPress={onBackPress}>
            <View style={styles.goBackContainer}>
              <IconArrowBack height="20" width="20" />
              <Text style={styles.goBackTitle}>
                {t('payables.details.vendor.goBack')}
              </Text>
            </View>
          </TouchableOpacity>
        )}
        <SupplierHeader name={supplierName} phone={supplierPhone} />
      </View>
      <View
        style={
          isFactoring ? styles.factoringOverviewLayout : styles.overviewLayout
        }
      >
        <TradeCreditOverview items={overviewItems} />
      </View>
      {autoPayIHCState.showAutoPayWarning && isFactoring && (
        <View
          style={[
            TabNavigatorStyles.style,
            { marginTop: -25, marginBottom: 10 },
          ]}
        >
          <AutoPayIHCWarning
            key={autoPayIHCState.refreshKey}
            onActionPress={() => {
              dispatchAutoPayIHC({
                type: AutoPayIHCActionType.SHOW_IHC_SETUP_FLOW,
              })
            }}
            onClose={() =>
              dispatchAutoPayIHC({ type: AutoPayIHCActionType.HIDE_WARNING })
            }
            isAutoPayIhcFlowOpened={autoPayIHCState.showAutoPayFlow}
          />
        </View>
      )}
      {isFactoring || supplierIsInvited ? (
        <ScreenScrollWrapper
          margin={css`
            margin: 0px 36px 31px 36px;
          `}
        >
          <InvoicesList store={store} />
        </ScreenScrollWrapper>
      ) : (
        <TabPayablesNavigator
          initialTab={TabNames.InvoicesTab}
          styles={TabNavigatorStyles}
          tabs={[Tabs.invoices, Tabs.quotes]}
          id={id}
        />
      )}
      {showMakePayment ? <Modal onClose={() => store.enableTable()} /> : null}
      {autoPayIHCState.showAutoPayFlow && (
        <AutoPayIHCFlow
          onClose={() =>
            dispatchAutoPayIHC({
              type: AutoPayIHCActionType.HIDE_IHC_SETUP_FLOW,
            })
          }
          navigation={navigation}
          onSuccess={() =>
            dispatchAutoPayIHC({
              type: AutoPayIHCActionType.AUTO_PAY_IHC_SETUP_SUCCESS,
            })
          }
        />
      )}
    </>
  )
})

const styles = StyleSheet.create({
  goBackContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginTop: 8,
  },
  goBackTitle: {
    fontWeight: '600',
    fontFamily: 'Inter',
    fontSize: 16,
    lineHeight: 30,
    color: '#00A0F3',
    marginLeft: 7,
  },
  overviewLayout: {
    width: 740,
    maxWidth: '65%',
    minWidth: '60%',
    justifyContent: 'flex-start',
    marginHorizontal: 36,
  },
  factoringOverviewLayout: {
    minWidth: '60%',
    justifyContent: 'flex-start',
    marginHorizontal: 36,
  },
})

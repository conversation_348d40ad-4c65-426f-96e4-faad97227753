import moment from 'moment'
import { dictionaries, exceptions } from '@linqpal/models'
import { ITransactionResponse } from './types'
import { IOperation } from '../../models/types'
import * as math from 'mathjs'

export function handleTabapayReturnedError(response: ITransactionResponse) {
  const networkRC = response.networkRC
  switch (networkRC) {
    case '54':
    case '33':
      throw new exceptions.LogicalError(`Your card has been expired.`)
    case '51':
      throw new exceptions.LogicalError(`Your account has insufficient funds`)
    case '61':
      throw new exceptions.LogicalError(
        `Withdrawal limit exceeded, use another card`,
      )
  }
  if (response.status !== 'COMPLETED') {
    console.log(response)
    throw new exceptions.LogicalError(
      'Unable to process the transaction. Please try again.',
    )
  }
  return true
}

export function generateTransactionObject({
  operation,
  amount,
  fee,
  reason,
  createTransactionResponse,
  sourceAccountId,
  destinationAccountId,
  isAutoDebit,
}: {
  operation: IOperation
  amount: number
  fee: number
  reason: string | undefined
  createTransactionResponse?: ITransactionResponse
  sourceAccountId: string | undefined
  destinationAccountId: string
  isAutoDebit?: boolean
}) {
  const transactionObject = {
    operation_id: operation.id,
    type: dictionaries.TRANSACTION_TYPES.ACH.TRANSFER,
    payer_id: operation.metadata.payer_id,
    amount: math.round(amount, 2),
    fee: math.round(fee, 2),
    date: moment().format('YYYY-MM-DD HH:mm:ss'),
    payment_method: dictionaries.PAYMENT_METHODS.CARD,
    reason: reason,
    status: dictionaries.TRANSACTION_STATUS.SUCCESS,
    metadata: {
      transactionID: createTransactionResponse?.transactionID,
      transactionNumber: createTransactionResponse?.transactionID,
      transactionType: dictionaries.TABAPAY_TRANSACTION_TYPES.PULL,
      network: createTransactionResponse?.network,
      networkRC: createTransactionResponse?.networkRC,
      networkID: createTransactionResponse?.networkID,
      approvalCode: createTransactionResponse?.approvalCode,
      sourceAccountId: sourceAccountId,
      destinationAccountId: destinationAccountId,
      avsResponseCode: '',
      securityCodeResponseCode: '',
      interchangeFees: '',
      networkFees: '',
      tabapayFees: '',
      cardLastFour: '',
      cardExpirationDate: '',
      isAutoDebit,
    },
  }
  if (createTransactionResponse?.AVS) {
    transactionObject.metadata.avsResponseCode =
      createTransactionResponse?.AVS.codeAVS
    transactionObject.metadata.securityCodeResponseCode =
      createTransactionResponse?.AVS.codeSecurityCode
  }
  if (createTransactionResponse?.fees) {
    transactionObject.metadata.interchangeFees =
      createTransactionResponse?.fees.interchange
    transactionObject.metadata.networkFees =
      createTransactionResponse?.fees.network
    transactionObject.metadata.tabapayFees =
      createTransactionResponse?.fees.tabapay
  }
  if (createTransactionResponse?.card) {
    transactionObject.metadata.cardLastFour =
      createTransactionResponse?.card.last4
    transactionObject.metadata.cardExpirationDate =
      createTransactionResponse?.card.expirationDate
  }

  return transactionObject
}

import Wrapper from '../../Wrapper'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { Text, View } from 'react-native'
import { FilterIcon } from '../../../../assets/icons'
import { useFocusEffect } from '@react-navigation/native'
import { useDropzone } from 'react-dropzone'
import { useMessage } from '../../../../utils/helpers/MessageProvider'
import {
  builderInvoiceUpload,
  dropzoneOptions,
  invoiceUploadError,
} from '../../../../utils/helpers/builderInvoiceUpload'
import { useInvoicesToPay } from '../../../../store/ScreensStore/InvoicesToPayStore'
import Loading from '../../../Loading'
import CreditStore from '../../CreditTab/CreditStore'
import { ReadytoPayWithVirtualCard } from '../../CreditTab/PayNowWithVirtualCard'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react'
import {
  EmptyInvoicePageMessage,
  FabButton,
  Filter,
  GroupedInvoices,
  ISwitchType,
  Switcher,
  SwitchType,
} from './components'
import RootStore from '../../../../store'
import { IPayable } from '@linqpal/models/src/dictionaries'
import InvoicesListDisplay from './InvoicesListDisplay_v2'
import { SingleVendor, Vendors } from '../../PayablesTab/Store/VendorsStore'
import CompanyHeader from '../Invoice/CompanyHeader'
import { styles, toolBarStyles } from '../Invoice/styles'
import CompanyStatistics from '../Invoice/CompanyStatistics'
import { EInvoiceType } from '@linqpal/models'
import AutoPayIHCWarning from '../../../../ui/organisms/AutoPayIHCWarning/AutoPayIHCWarning'
import { AutoPayIHCFlow } from '../../MoreTab/Wallet/AutoPayIHCFlow'
import {
  useAutoPayIHCReducer,
  AutoPayIHCActionType,
} from '../../MoreTab/Wallet/AutoPayIHCFlow/useAutoPayIHCReducer'

export const InvoiceListing = observer(({ navigation, route }) => {
  const { t } = useTranslation('payables')
  const [modal, setModal] = useState(false)

  const [invoiceList, setInvoiceList] = useState([])
  const [filterStatus, setFilterStatus] = useState('')
  const [loading, setLoading] = useState(true)
  const [autoPayIHCState, dispatchAutoPayIHC] = useAutoPayIHCReducer()
  const [switcher, setSwitcher] = useState<ISwitchType>(SwitchType.INVOICES)
  const { open, acceptedFiles, getInputProps, fileRejections } =
    useDropzone(dropzoneOptions)
  const { hasUnsedActiveCards, fetchLoans } = CreditStore
  const { fetchInvoiceToPay } = useInvoicesToPay()
  const { setMessage } = useMessage()
  const canUploadInvoice = RootStore.userStore.settings.get('canUploadInvoice')
  const {
    routeParams: { action },
  } = RootStore

  const vendorsStore = Vendors
  const singleVendorStore = SingleVendor

  const isFactoring = !!(
    singleVendorStore.currentVendor?.supplierName &&
    singleVendorStore.isFactoring
  )

  useEffect(() => {
    if (action === 'reloadInvoices') RootStore.initRouteParams({})
  }, [action])

  useEffect(() => {
    fetchLoans()
  }, [fetchLoans])

  useEffect(() => {
    if (isFactoring) {
      singleVendorStore.fetchStatisticsData()
    }
  }, [isFactoring, singleVendorStore])

  useFocusEffect(
    useCallback(() => {
      const types = isFactoring
        ? [EInvoiceType.INVOICE]
        : [EInvoiceType.QUOTE, EInvoiceType.INVOICE, EInvoiceType.SALE_ORDER]
      fetchInvoiceToPay({ types })
        .then((data) => {
          setInvoiceList(data.items)
          setFilterStatus('All')
          setLoading(false)
        })
        .catch((e) => {
          setLoading(false)
          console.log(e)
        })
    }, [
      setInvoiceList,
      setFilterStatus,
      setLoading,
      fetchInvoiceToPay,
      isFactoring,
    ]),
  )

  useEffect(() => {
    if (acceptedFiles.length < 1) {
      return
    }
    builderInvoiceUpload(navigation, acceptedFiles)
  }, [acceptedFiles, navigation])

  useEffect(() => {
    if (fileRejections.length > 0) {
      invoiceUploadError(fileRejections, setMessage)
    }
  }, [fileRejections, setMessage])

  const list = useMemo(() => {
    let invoices
    if (filterStatus === 'All') {
      invoices = invoiceList
    } else {
      invoices = invoiceList.filter((invoice: IPayable) => {
        return (
          invoice.invoiceStatus ===
          filterStatus.split(' ').join('').toUpperCase()
        )
      })
    }

    if (invoices.length === 0 && !loading) {
      return <EmptyInvoicePageMessage />
    }

    if (switcher === SwitchType.VENDORS) {
      return <GroupedInvoices invoices={invoices} t={t} />
    }

    return (
      <InvoicesListDisplay
        invoices={invoices}
        navigation={navigation}
        route={route}
        isFactoring={isFactoring}
      />
    )
  }, [
    filterStatus,
    invoiceList,
    loading,
    navigation,
    route,
    switcher,
    t,
    isFactoring,
  ])

  if (loading || singleVendorStore.isStatisticsLoading) return <Loading />

  return (
    <>
      <Wrapper
        toolbar={
          <Toolbar
            onPress={() => setModal(true)}
            t={t}
            switcher={switcher}
            setSwitcher={setSwitcher}
            vendorsLength={vendorsStore.vendorsLength}
            isFactoring={isFactoring}
          />
        }
      >
        {hasUnsedActiveCards && (
          <ReadytoPayWithVirtualCard mobileView dontShowExpired />
        )}
        {isFactoring && (
          <>
            <CompanyHeader
              company={{
                name: singleVendorStore.currentVendor.supplierName,
                phone: singleVendorStore.currentVendor.supplierPhone,
              }}
              totalAmount={singleVendorStore.vendorsStatistics.totalDueSum}
              style={styles.invoicesListHeader}
              amountLabel={t('payables.mobile.totalBalanceDue')}
              amountLabelStyles={{ marginTop: 4, color: '#668598' }}
            />
            <CompanyStatistics
              statistics={singleVendorStore.vendorsStatistics}
            />
            {vendorsStore.vendorsLength === 1 && (
              <View style={toolBarStyles.factoringFilter}>
                <Text
                  style={toolBarStyles.filterText}
                  onPress={() => setModal(true)}
                >
                  <FilterIcon /> {t('payables.mobile.filters')}
                </Text>
              </View>
            )}
          </>
        )}
        {autoPayIHCState.showAutoPayWarning && (
          <View style={{ marginBottom: 15 }}>
            <AutoPayIHCWarning
              key={autoPayIHCState.refreshKey}
              onActionPress={() =>
                dispatchAutoPayIHC({
                  type: AutoPayIHCActionType.SHOW_IHC_SETUP_FLOW,
                })
              }
              onClose={() =>
                dispatchAutoPayIHC({
                  type: AutoPayIHCActionType.HIDE_WARNING,
                })
              }
              isAutoPayIhcFlowOpened={autoPayIHCState.showAutoPayFlow}
            />
          </View>
        )}
        {list}
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <input {...getInputProps()} />
        <Filter
          t={t}
          onPress={(value) => setFilterStatus(value)}
          filterStatus={filterStatus}
          onClose={() => setModal(false)}
          visible={modal}
        />
      </Wrapper>
      {!!canUploadInvoice && <FabButton onPress={open} />}
      {autoPayIHCState.showAutoPayFlow && (
        <AutoPayIHCFlow
          onClose={() =>
            dispatchAutoPayIHC({
              type: AutoPayIHCActionType.HIDE_IHC_SETUP_FLOW,
            })
          }
          navigation={navigation}
          onSuccess={() =>
            dispatchAutoPayIHC({
              type: AutoPayIHCActionType.AUTO_PAY_IHC_SETUP_SUCCESS,
            })
          }
        />
      )}
    </>
  )
})

const Toolbar = ({
  onPress,
  t,
  switcher,
  setSwitcher,
  vendorsLength,
  isFactoring,
}) => {
  if (isFactoring && vendorsLength === 1) {
    return (
      <View style={toolBarStyles.centeredContainer}>
        <Text style={toolBarStyles.centeredTitle}>
          {t('payables.allPayables')}
        </Text>
      </View>
    )
  }
  return (
    <View style={toolBarStyles.container}>
      <View style={toolBarStyles.header}>
        <Text style={toolBarStyles.title}>{t('payables.mobilePayables')}</Text>
        <Text style={toolBarStyles.filterText} onPress={onPress}>
          <FilterIcon /> {t('payables.mobile.filters')}
        </Text>
      </View>
      <Switcher active={switcher} onPress={setSwitcher} />
    </View>
  )
}

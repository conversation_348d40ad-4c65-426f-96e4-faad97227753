import React from 'react'
import { StyleSheet, View, Text } from 'react-native'
import { Spacer } from '@linqpal/components/src/ui'
import Button from '../../../../../../../IntegrationPayment/RegularIntegration/components/Button'
import { useTranslation } from 'react-i18next'
import BuilderBottomModal from '../../../../../../../../ui/molecules/BuilderBottomModal'
import { useResponsive } from '@linqpal/components/src/hooks'
import { IconInfoBlue } from '../../../../../../../../assets/icons'

interface SuccessStepProps {
  visible: boolean
  onClose: () => void
}

export const SuccessStep: React.FC<SuccessStepProps> = ({
  visible,
  onClose,
}) => {
  const { t } = useTranslation('global')
  const { sm } = useResponsive()

  return (
    <BuilderBottomModal
      visible={visible}
      onClose={onClose}
      height={sm ? 400 : '95%'}
      width={sm ? 600 : undefined}
      footer={
        <View style={sm ? styles.footer : styles.mobileFooter}>
          <Button
            buttonStyle={sm ? styles.desktopButton : styles.mobileButton}
            textStyle={styles.desktopButtonText}
            onPress={onClose}
            testID="done_disabled_auto_pay_btn"
            label={t('autoPayIHCFlow.success.doneButton')}
          />
        </View>
      }
      paddingHorizontal={sm ? 20 : 0}
    >
      <View style={styles.alertContainer}>
        <View
          style={
            sm
              ? styles.iconContainer
              : [styles.iconContainer, styles.mobileIconContainer]
          }
        >
          <IconInfoBlue width={72} height={72} />
        </View>
        <Spacer height={sm ? 25 : 35} />
        <Text style={sm ? styles.title : [styles.title, styles.mobileTitle]}>
          {t('autoPayIHCFlow.success.disabled.title')}
        </Text>
        <Spacer height={sm ? 15 : 8} />
        <Text style={sm ? styles.text : [styles.text, styles.mobileText]}>
          {t('autoPayIHCFlow.success.disabled.subtitle')}
        </Text>
        <Spacer height={sm ? 0 : 20} />
        <Text style={sm ? styles.text : [styles.text, styles.mobileText]}>
          {t('autoPayIHCFlow.success.disabled.additionalInfo')}
        </Text>
      </View>
    </BuilderBottomModal>
  )
}

const styles = StyleSheet.create({
  mobileIconContainer: {
    marginTop: -120,
  },
  iconContainer: {
    alignItems: 'center',
    marginTop: -30,
  },
  alertContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  mobileFooter: {
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopColor: '#DEE5EB',
    borderTopWidth: 1,
    paddingHorizontal: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    lineHeight: 32,
    fontFamily: 'Inter',
    textAlign: 'center',
    color: '#19262F',
  },
  mobileTitle: {
    fontSize: 18,
    lineHeight: 24,
    letterSpacing: -0.3,
  },
  text: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500',
    textAlign: 'center',
    fontFamily: 'Inter',
    color: '#19262F',
  },
  mobileText: {
    lineHeight: 20,
    fontWeight: '500',
  },
  footer: {
    height: 80,
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  mobileButton: {
    width: '100%',
    borderRadius: 8,
  },
  desktopButton: {
    height: 48,
    width: 89,
  },
  desktopButtonText: {
    fontSize: 14,
    lineHeight: 24,
    fontWeight: '700',
    fontFamily: 'Inter',
    letterSpacing: 0,
  },
})

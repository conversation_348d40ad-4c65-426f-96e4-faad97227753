import { PAYMENT_METHOD_TYPE } from '@linqpal/models/src/dictionaries'

interface PaymentMethod {
  id: string
  name: string
  accountNumber?: string
  accountType?: string
  paymentMethodType: typeof PAYMENT_METHOD_TYPE[keyof typeof PAYMENT_METHOD_TYPE]
  cardMetadata?: {
    type: string
    network: string
    isRegulated: boolean
    lastFour?: string
    nameFI?: string
  }
}

export const getPaymentMethodTitle = (
  paymentMethod: PaymentMethod,
  t: (key: string, options?: any) => string,
): string => {
  if (paymentMethod.paymentMethodType === PAYMENT_METHOD_TYPE.BANK) {
    const { accountNumber } = paymentMethod
    return t('autoPayWarning.enabled.bankAccountTitle', {
      number: `**** ${accountNumber?.slice(-4) ?? ''}`,
    })
  }

  if (paymentMethod.paymentMethodType === PAYMENT_METHOD_TYPE.CARD) {
    const { cardMetadata } = paymentMethod
    if (!cardMetadata) return '****'

    const accNum =
      paymentMethod.cardMetadata?.lastFour ||
      paymentMethod.accountNumber?.slice(-4) ||
      ''
    return t('autoPayWarning.enabled.cardAccountTitle', {
      bankName: cardMetadata.nameFI ?? '',
      cardNetwork: cardMetadata.network ?? '',
      cardType: cardMetadata.type ?? '',
      number: `**** ${accNum}`,
    })
  }

  return '****'
}

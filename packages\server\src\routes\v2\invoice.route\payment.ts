import { TCompoundRoute } from '@linqpal/models/src/routes2/types'
import { PaymentRequest } from '@linqpal/models/src/routes2/invoice/types'
import { isFeatureOn } from '../../../services/config.service'
import { authRequired } from '../../../services/auth.service'
import transactional from '../../../services/transactional.service'
import { exceptions } from '@linqpal/models'
import {
  cardPaymentService,
  handleInvoiceAfterPaymentActions,
  Invoice,
  invoicesService,
  payWithAch,
  checkFactoringInvoices,
} from '@linqpal/common-backend'
import {
  insertNotification,
  makeInvoiceNotificationRead,
} from '../../../controllers/notification.controller'
import mongoose, { Types } from 'mongoose'
import { getOperation } from '@linqpal/common-backend/src/services/cardPayment.service'
import { linkInvoiceToCustomer } from '@linqpal/common-backend/src/services/invoices.service/linkInvoiceToCustomer'
import { IPaymentInfo } from '@linqpal/common-backend/src/services/payment/types'

export const payment: TCompoundRoute<PaymentRequest, any> = {
  middlewares: {
    pre: [isFeatureOn('payments'), authRequired(), ...transactional.pre],
    post: transactional.post,
  },
  post: async (data, req) => {
    const { invoiceIds, paymentMethod = '', requestedAmount } = data
    if (!invoiceIds || !invoiceIds.length) {
      throw new exceptions.LogicalError('Invoices not found')
    }

    const objectIds = invoiceIds.map((id) => new Types.ObjectId(id))
    const invoices = await Invoice.find({
      _id: mongoose.trusted({
        $in: objectIds,
      }),
    }).session(req.session)

    if (!invoices || !invoices.length) {
      throw new exceptions.LogicalError('Invoices not found')
    }

    const builder = await invoicesService.findBuilderByAccountId(
      invoices[0].customer_account_id,
      req.session,
    )

    if (
      req.company &&
      builder &&
      req.company._id.toString() !== builder._id.toString()
    ) {
      throw new exceptions.LogicalError('Not enough permissions to pay')
    }
    const factoringValidation = await checkFactoringInvoices(
      invoices,
      paymentMethod,
      req.company._id.toString(),
    )

    if (!factoringValidation.isValid) {
      throw new exceptions.LogicalError(factoringValidation.errorMessage!)
    }

    const userId = req.user._id.toString()

    const checkInvoiceOperationPromises: Promise<any>[] = []
    const linkInvoicePromises: Promise<any>[] = []

    invoices.forEach((inv) => {
      checkInvoiceOperationPromises.push(
        getOperation(inv, inv.company_id, req.session),
      )
      linkInvoicePromises.push(
        linkInvoiceToCustomer(inv, userId, true, req.session),
      )
    })
    await Promise.all(checkInvoiceOperationPromises) // checks that invoices are not paid off
    await Promise.all(linkInvoicePromises) // links invoices to customer that makes a payment

    const invoiceCompanyId = invoices[0].company_id

    let paymentInfo: IPaymentInfo

    switch (paymentMethod) {
      case 'ach':
        paymentInfo = await payWithAch(
          invoices,
          data.accountId,
          req.company.id,
          invoiceCompanyId,
          req.session,
          userId,
          requestedAmount,
        )
        break
      case 'card':
        paymentInfo = await cardPaymentService.makeCardPayment({
          invoices,
          accountId: data.accountId,
          payerCompanyId: req.company.id,
          session: req.session,
          userId,
          userRequestedAmount: requestedAmount,
        })
        break
      default:
        throw new exceptions.LogicalError('Undefined payment method')
    }

    // Handle post-payment operations, e.g. notifications, integrations, etc
    // TODO: KK: Would be better to move insertNotification & makeInvoiceNotificationRead to common-backend
    await handleInvoiceAfterPaymentActions({
      invoiceIds,
      companyId: req.company.id,
      userId: req.user.id,
      invoiceCompanyId,
      paymentMethod,
      paymentInfo,
      insertNotification,
      makeInvoiceNotificationRead,
    })
  },
}

import React, { memo } from 'react'
import { commonColors } from '@linqpal/common-frontend/src/theme'
import { View, StyleSheet } from 'react-native'
import { LOAN_REPAYMENT_STATUS } from '@linqpal/models/src/dictionaries/loanStatuses'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'

interface VerticalProgressProps {
  isCompleted?: boolean
  shouldHideLine?: boolean
  status: string
}

export const VerticalPaymentProgress = memo(
  ({ isCompleted, shouldHideLine, status }: VerticalProgressProps) => {
    let customCircleStyle, customVerticalStyle

    if (isCompleted) {
      customCircleStyle = styles.completedCircle
      customVerticalStyle = styles.completedVertical
    } else if (
      [LOAN_REPAYMENT_STATUS.PASTDUE, LOAN_REPAYMENT_STATUS.LATE].includes(
        status,
      )
    ) {
      customCircleStyle = styles.pastDue
    } else if (status === LOAN_REPAYMENT_STATUS.PROCESSING) {
      customCircleStyle = styles.processing
    }

    return (
      <View style={{ alignItems: 'center' }}>
        <View style={composeStyle(styles.circleStyles, customCircleStyle)} />
        {!shouldHideLine && (
          <View
            style={composeStyle(styles.verticalLineStyles, customVerticalStyle)}
          />
        )}
      </View>
    )
  },
)

const styles = StyleSheet.create({
  circleStyles: {
    width: 10,
    height: 10,
    borderRadius: 5,
    borderWidth: 2,
    backgroundColor: commonColors.backgroundLight,
    borderColor: commonColors.borderLight,
  },
  verticalLineStyles: {
    flex: 1,
    borderLeftWidth: 2,
    borderLeftColor: commonColors.borderLight,
    borderLeftStyle: 'dashed',
  },
  completedCircle: {
    backgroundColor: commonColors.accentText,
    borderColor: commonColors.accentText,
  },
  completedVertical: {
    borderLeftColor: commonColors.accentText,
    borderLeftStyle: 'solid',
  },
  pastDue: {
    borderColor: '#DB081C',
  },
  processing: {
    borderColor: commonColors.accentText,
  },
})

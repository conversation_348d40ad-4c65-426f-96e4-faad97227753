import {
  Company,
  connectToDatabase,
  emailService,
  getEnvironmentVariables,
  initializeRepayment,
  LMS,
  LoanApplication,
  Logger,
  Slack,
} from '@linqpal/common-backend'
import type {
  IBankAccount,
  ICompany,
  IInvoice,
  ILoanApplication,
} from '@linqpal/common-backend/src/models/types'
import { SQSEvent } from 'aws-lambda'
import moment, { Moment } from 'moment'
import mongoose from 'mongoose'
import { Batch, BatchCompany, sendLateReminder } from './helper'
import { LoanApplicationService } from '@linqpal/common-backend/src/services/loanApplication.service'
import EmailNotifications from '@linqpal/common-backend/src/helpers/EmailNotifications'
import { getOwningUser } from '@linqpal/common-backend/src/services/company.service'
import SmsNotifications from '@linqpal/common-backend/src/helpers/SmsNotifications'
import smsService from '@linqpal/common-backend/src/services/sms.service'
import { customErrorToString } from '@linqpal/common-backend/src/helpers/SnsEventBuilder'

const logger = new Logger({ module: 'LMS', subModule: 'processDue' })

export async function processDue(event: SQSEvent) {
  await getEnvironmentVariables()
  await connectToDatabase()

  const log = logger.startTransaction()
  let result = { result: 'ok' }

  log.info('Received a batch of due loans from SQS')
  log.debug({ event: event.Records }, 'Due loan SQS event details')

  const overdueCompanies: BatchCompany[] = []
  const allLoans: Batch[] = []

  event.Records.forEach((record) => {
    const parsed = JSON.parse(record.body)
    if (parsed.overdueSum > 0) {
      overdueCompanies.push(parsed)
    }
    allLoans.push(...parsed.loans)
  })

  log.info({ event: event.Records }, 'Batch')
  log.info({ overdueCompanies }, 'List of overdue companies')

  try {
    const promises = allLoans.map((loan) => {
      return processRecord(loan, log)
    })

    const results = await Promise.allSettled(promises)
    log.debug({ results }, 'Loan event handling results')

    const reminders = overdueCompanies.map(async (comp) =>
      sendLateReminder(comp),
    )
    const reminderResults = await Promise.allSettled(reminders)

    results.map((res) => {
      if (res.status === 'rejected') result = { result: 'Error' }
    })

    log.info({ result: result.result }, 'Result of handling the loan event')
    log.info({ reminderResults }, 'Overdue reminder result')
    return result
  } catch (e) {
    log.error({ err: e }, 'An error occurred when trying to process due loans')
    throw e
  }
}

const processRecord = async (record: any, loggerTransaction: Logger) => {
  const session = await mongoose.startSession()

  const log = loggerTransaction.startSpan()
  const { nextPaymentDate, applicationId, dueStatus } = record

  const nextPaymentMoment = moment.tz(nextPaymentDate, 'UTC')
  const paymentAmount = parseFloat(record.nextPaymentAmount)
  const overDueAmount = parseFloat(record.overDueAmount)

  let company: ICompany | undefined | null

  try {
    session.startTransaction()
    const app = await LoanApplication.findById(applicationId).session(session)
    if (!app) return

    if (app.metadata?.payment_cancelled) {
      // prettier-ignore
      log.info({loanApplicationId: app.id}, 'payment is canceled, skipping app')
      return
    }

    company = await Company.findById(app.company_id).populate('bankAccounts')
    if (!company) return

    const customerId = company.finicity?.customerId || null

    log.info({ applicationId, customerId, dueStatus }, 'Handling loan')
    log.debug({ record }, 'Handled loan details')

    let bankAccount: Partial<IBankAccount> | undefined

    const invoices = await LoanApplicationService.getInvoices(app, session)
    if (!invoices?.length) log.warn({ app }, 'no invoices for application')

    const supplier = invoices?.[0]?.company_id
      ? await Company.findById(invoices[0].company_id)
      : null

    if (app.metadata?.repayment?.loanPaymentCollection === 'supplier') {
      if (supplier?.settings.repayment) {
        bankAccount = {
          routingNumber: supplier.settings.repayment.routingNumber,
          accountNumber: supplier.settings.repayment.accountNumber,
          accountholderName:
            supplier.settings.repayment.nameOnAccount ||
            supplier.legalName ||
            supplier.name,
        }
      }
    }

    const daysTillPayment = getDaysTillNextPayment(nextPaymentDate)

    if (daysTillPayment <= 0 && !app.metadata?.payment_cancelled) {
      log.info(
        { applicationId, paymentAmount, nextPaymentDate },
        'Will initialize repayment',
      )
      await initializeRepayment(
        {
          application: app,
          amountDue: paymentAmount,
          paymentDate: moment().utc().format('YYYY-MM-DD'),
          isAutoPay: true,
          logger: log,
          bankAccount,
        },
        session,
      )
    } else if (
      record.isOverdue &&
      overDueAmount > 0 &&
      !app.metadata?.payment_cancelled
    ) {
      log.info(
        { applicationId, paymentAmount, nextPaymentDate },
        'Will initialize repayment for overdue amount',
      )
      await initializeRepayment(
        {
          application: app,
          amountDue: overDueAmount,
          paymentDate: moment().utc().format('YYYY-MM-DD'),
          logger: log,
          bankAccount,
          isAutoPay: true,
        },
        session,
      )
    } else if (daysTillPayment === 0 || daysTillPayment === 3) {
      await sendUpcomingPaymentNotifications(
        app,
        supplier,
        invoices,
        paymentAmount,
        nextPaymentMoment,
        daysTillPayment,
      )
    } else if (daysTillPayment === 1 || daysTillPayment === 2) {
      await sendUpcomingPaymentNotifications(
        app,
        supplier,
        invoices,
        paymentAmount,
        nextPaymentMoment,
        daysTillPayment,
      )
      // await checkAvailableBalance(
      //   company,
      //   invoices,
      //   paymentAmount,
      //   daysTillPayment,
      // )
    }

    log.info({ applicationId }, 'Start syncLoanApplication')
    await LMS.syncLoanApplicationFields(app, session)
    await session.commitTransaction()
  } catch (err) {
    await session.abortTransaction()

    const errorInfo = JSON.stringify(
      { company: company?.name, ...record },
      null,
      '  ',
    )

    log.error(
      { applicationId, err, errorInfo },
      'An error occurred when trying to process a loan',
    )

    await Slack.notifyError(
      'ProcessDue',
      `Repayment failed: ${errorInfo}`,
      customErrorToString(err),
    )
  } finally {
    await session.endSession()
  }
}

async function sendUpcomingPaymentNotifications(
  app: ILoanApplication,
  supplier: ICompany | null,
  invoices: IInvoice[] | null,
  paymentAmount: number,
  paymentDate: Moment,
  dueDays: number,
) {
  const paymentType = app.metadata?.payment_cancelled ? 'manual' : 'auto'

  if (dueDays > 3) return
  if (dueDays < 3 && paymentType === 'auto') return

  if (!invoices?.length) {
    logger.warn({ app }, `no invoices for app ${app.id}`)
    return
  }

  logger.info(
    { app, paymentAmount, paymentDate, dueDays },
    'sending upcoming payment email',
  )

  const customerUser = await getOwningUser(app.company_id)
  const invoiceNumbers = invoices.map((i) => ({ number: i.invoice_number }))

  const emailMessage = EmailNotifications.drawPaymentUpcoming(paymentType, {
    supplierName: supplier?.legalName || supplier?.name || '',
    paymentAmount,
    paymentDate,
    dueDays,
    invoices: invoiceNumbers,
  })

  await emailService.sendToCompanyUsers(app.company_id, emailMessage)

  if (dueDays === 3) {
    // for 2 and 1 day we check available balance and send different SMS if not enough
    const smsMessage =
      invoices.length === 1
        ? SmsNotifications.drawPaymentIsIn3DaysForSingleInvoice({
            customerName: customerUser?.firstName || '',
            invoiceNumber: invoices[0].invoice_number,
          })
        : SmsNotifications.drawPaymentIsIn3DaysForGroupedInvoices({
            customerName: customerUser?.firstName || '',
          })

    await smsService.send(customerUser?.phone, smsMessage)
  }
}

// async function checkAvailableBalance(
//   company: ICompany,
//   invoices: IInvoice[],
//   paymentAmount: number,
//   daysTillNextPayment: number,
// ) {
//   // for now notifications available for single invoice apps only
//   if (invoices?.length !== 1) return Promise.resolve()
//
//   // prettier-ignore
//   logger.info({ company }, `checking available balance for company ${company.id}`)
//
//   const plaidAccount = company.bankAccounts?.find(
//     (account) =>
//       (account.isPrimaryForCredit ||
//         ((account.isPrimaryForCredit === null ||
//           account.isPrimaryForCredit === undefined) &&
//           account.isPrimary)) &&
//       account.plaid?.account_id &&
//       account.plaid?.access_token,
//   )
//
//   if (!plaidAccount) {
//     logger.warn({ company }, 'no plaid account found')
//     return Promise.resolve()
//   }
//
//   const balance = await PlaidService.getAvailableBalance(
//     company.id,
//     plaidAccount,
//   )
//
//   if (!isFinite(balance)) return Promise.resolve()
//   if (balance >= paymentAmount) return Promise.resolve()
//
//   // prettier-ignore
//   logger.info({ balance, paymentAmount}, `insufficient balance for company ${company.id}`)
//
//   const customerUser = await getOwningUser(company.id)
//   if (!customerUser)
//     throw new CriticalError(`no user found for company ${company.id}`)
//
//   const messageData = {
//     customerName: customerUser.firstName ?? '',
//     invoiceNumber: invoices[0].invoice_number,
//   }
//
//   // prettier-ignore
//   const smsMessage =
//     daysTillNextPayment === 1
//       ? SmsNotifications.drawPaymentIsIn1DayForSingleInvoiceAndInsufficientFunds(messageData)
//       : SmsNotifications.drawPaymentIsIn2DaysForSingleInvoiceAndInsufficientFunds(messageData)
//
//   return smsService.send(customerUser?.phone, smsMessage)
// }

function getDaysTillNextPayment(paymentDate: string) {
  const days = moment
    .tz(paymentDate, 'UTC')
    .startOf('day')
    .diff(moment().utc().startOf('day'), 'days')
  return days
}

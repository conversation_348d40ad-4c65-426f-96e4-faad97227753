import { Request, Response, NextFunction } from 'express'
import moment from 'moment'
import {
  LoanApplication,
  BankAccount,
} from '@linqpal/common-backend/src/models'
import { initializeRepayment } from '@linqpal/common-backend/src/services/lms/initializeRepayment'
import { Logger } from '@linqpal/common-backend/src/services/logger/logger.service'
import { exceptions } from '@linqpal/models'
import { PAYMENT_METHOD_TYPE } from '@linqpal/models/src/dictionaries'
import transactionalService from '../../../services/transactional.service'
import { LMS } from '@linqpal/common-backend'
import { GetRemainingAmount } from '@linqpal/common-backend/src/services/lms.service'
import numbro from 'numbro'
import { apiKeyRequired } from '../../../services/auth.service'
import { ControllerItem } from 'src/routes/controllerItem'

const logger = new Logger({ module: 'BffLoanPayment' })

export default {
  middlewares: {
    pre: [apiKeyRequired(), ...transactionalService.pre],
    post: [...transactionalService.post],
  },
  post: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { drawId, amount, bankAccountId, autoDebit = false } = req.body
      const logContext = { drawId, amount, bankAccountId, autoDebit }

      if (!drawId || !amount || !bankAccountId) {
        logger.error(logContext, 'Missing required parameters')
        return res.status(400).json({
          success: false,
          error: 'Missing required parameters',
        })
      }

      logger.info(logContext, 'Processing BFF loan payment request')

      const loanApplication = await LoanApplication.findOne({
        lms_id: drawId,
      }).session(req.session)

      if (!loanApplication) {
        logger.error({ drawId }, 'Loan application not found for drawId')
        return res.status(404).json({
          success: false,
          error: 'Loan application not found',
        })
      }

      const lmsInfo = await LMS.getLoanInfo(loanApplication.lms_id)
      if (!lmsInfo) {
        logger.error({ drawId }, 'Failed to retrieve loan info from LMS')
        return res.status(500).json({
          success: false,
          error: 'Failed to retrieve loan information',
        })
      }

      const remainingAmount = GetRemainingAmount(lmsInfo)
      const formattedRemaining = numbro(remainingAmount).format({
        mantissa: 2,
      })

      logger.info(
        {
          drawId,
          amount,
          remainingAmount,
          formattedRemaining,
        },
        'Checking payment amount against remaining balance',
      )

      if (amount > parseFloat(formattedRemaining)) {
        logger.warn(
          {
            drawId,
            amount,
            remainingAmount: formattedRemaining,
          },
          'Payment amount exceeds remaining balance',
        )
        return res.status(400).json({
          success: false,
          error: 'Payment amount exceeds remaining balance',
        })
      }

      const bankAccount = await BankAccount.findById(bankAccountId).session(
        req.session,
      )

      if (!bankAccount) {
        logger.error({ bankAccountId }, 'Bank account(card) not found')
        return res.status(404).json({
          success: false,
          error: 'Bank account(card) not found',
        })
      }

      if (bankAccount.paymentMethodType !== PAYMENT_METHOD_TYPE.CARD) {
        logger.error({ bankAccountId }, 'Bank account is not a card')
        return res.status(400).json({
          success: false,
          error: 'Bank account is not a card',
        })
      }

      await initializeRepayment(
        {
          application: loanApplication,
          amountDue: amount,
          paymentDate: moment().format('YYYY-MM-DD'),
          logger,
          bankAccount,
          isAutoDebit: autoDebit,
        },
        req.session,
      )

      logger.info(logContext, 'Successfully processed loan payment')

      return next()
    } catch (error: unknown) {
      logger.error(
        { error, body: req.body },
        'Error in BFF loan payment endpoint',
      )

      if (error instanceof exceptions.LogicalError) {
        return res.status(400).json({
          success: false,
          error: error.message,
        })
      }

      if (error instanceof Error) {
        if (
          error.message.includes('declined') ||
          error.message.includes('insufficient funds')
        ) {
          return res.status(402).json({
            success: false,
            error: error.message,
          })
        }
      }

      return res.status(500).json({
        success: false,
        error: 'An internal server error occurred',
      })
    }
  },
} as ControllerItem

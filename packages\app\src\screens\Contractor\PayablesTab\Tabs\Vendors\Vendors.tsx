import React, { useState } from 'react'
import { SortingOrder } from '@linqpal/models/src/dictionaries/global'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { currencyMask } from '../../../../../utils/helpers/masking'
import ApiTable, { tableStyles } from '../../../../../ui/molecules/ApiTable'
import { Filters } from './Filters'
import { InfoInCircle } from '../../../../../assets/icons'
import { View, Text } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { paths } from '../../../../links'
import {
  IPayablesVendors,
  PayablesVendorsSort,
} from '@linqpal/models/src/dictionaries'
import { ViewButton } from '../../Components/ViewButton'
import { CellContainer } from '../../../../../ui/atoms/CellContainer'
import { SingleVendor, Vendors } from '../../Store/VendorsStore'
import { EmptyResult } from '../../Components/EmptyResult'
import { SortingIcon } from '../../../../../ui/atoms/SortingIcon'
import { StickyHeader } from '../../commonStyles'

export const VendorsList = observer(() => {
  const store = Vendors
  const singleVendorStore = SingleVendor

  const navigation = useNavigation()

  const [[sortColumn, sortDirection], setSort] = useState([
    PayablesVendorsSort.CREATED_AT as string,
    SortingOrder.DESC as string,
  ])

  const { t } = useTranslation('payables')

  const columns = [
    {
      name: t('payables.tabVendors.table.VendorName'),
      cell: (item: IPayablesVendors) => (
        <CellContainer value={item.supplierName} />
      ),
      selector: PayablesVendorsSort.NAME,
    },
    {
      name: t('payables.tabVendors.table.TotalDue'),
      cell: (item: IPayablesVendors) => currencyMask(item.totalDueSum),
      selector: PayablesVendorsSort.TOTAL_DUE,
      sortable: true,
    },
    {
      name: t('payables.tabVendors.table.InvoicesDue'),
      cell: (item: IPayablesVendors) => item.totalDueInvoices,
    },
    {
      name: t('payables.tabVendors.table.TotalPastDue'),
      cell: (item: IPayablesVendors) => (
        <Text style={{ color: item.totalPastDueSum ? '#EC002A' : '#19262F' }}>
          {currencyMask(item.totalPastDueSum)}
        </Text>
      ),
      selector: PayablesVendorsSort.TOTAL_PAST_DUE,
      sortable: true,
    },
    {
      name: t('payables.tabVendors.table.InvoicesPastDue'),
      cell: (item: IPayablesVendors) => item.totalPastDueInvoices,
    },

    {
      name: '',
      right: true,
      marginLeft: 0,
      width: '80px',
      selector: (item: IPayablesVendors) => (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-end',
            flex: 2,
          }}
        >
          <ViewButton
            onPress={() => {
              singleVendorStore.setVendor(
                item.id,
                item.supplierName,
                item.supplierPhone,
                item.supplierIsInvited,
              )
              singleVendorStore.setIsFactoring(
                !!item.inHouseCreditIsEnabled,
                item.arAdvanceIsEnabled,
              )
              navigation.navigate(paths.Console.Payables.Vendor)
            }}
          />
        </View>
      ),
    },
  ]
  return (
    <>
      <StickyHeader>
        <Filters
          params={store.filter}
          searchPlaceholder={t('payables.tabVendors.searchPlaceholder')}
          onChange={(e) => store.setFilter(e)}
        />
      </StickyHeader>

      <ApiTable
        data={store.tableData.items}
        count={store.tableData.count}
        apiCaller={store.callApi}
        showFilters={false}
        searchable={false}
        columns={columns}
        keyField={'_id' as any}
        loading={store.isLoading}
        onChangePage={(page: number) => store.setPage(page)}
        style={tableStyles}
        sortCol={sortColumn}
        sortDir={sortDirection}
        sortIcon={(sortDirect: SortingOrder | null) => (
          <SortingIcon sortDirection={sortDirect} />
        )}
        onSort={(column, sortDirect) => {
          store.setSort(column, sortDirect)
        }}
        setSortColDir={setSort}
        disableTable={store.tableDisabled}
        emptyResult={
          <EmptyResult
            header={t('payables.emptyFilterResult.header')}
            text={t('payables.emptyFilterResult.text')}
            icon={<InfoInCircle />}
          />
        }
      />
    </>
  )
})

import React from 'react'
import { StyleSheet, TouchableOpacity, View } from 'react-native'
import { useTranslation } from 'react-i18next'
import BuilderBottomModal from '../../../../../../../../ui/molecules/BuilderBottomModal'
import { BtText, Spacer } from '@linqpal/components/src/ui'
import { ModalCloseIcon } from '../../../../../../../../assets/icons'
import { BButton } from '../../../../../../../../ui/atoms/builder-2.0/Button'
import useIsMobile from '../../../../../../PayablesTab/hooks/useIsMobile'

interface InitialStepProps {
  visible: boolean
  onClose: () => void
  onKeepAutoPayEnabled: () => void
  onTurnOffAutoPay: () => void
  isLoading: boolean
}

export const InitialStep: React.FC<InitialStepProps> = ({
  visible,
  onClose,
  onKeepAutoPayEnabled,
  onTurnOffAutoPay,
  isLoading,
}) => {
  const isMobile = useIsMobile()
  const { t } = useTranslation('global')

  return (
    <BuilderBottomModal
      visible={visible}
      width={isMobile ? undefined : 630}
      height={isMobile ? '100%' : 252}
      paddingHorizontal={0}
      title={
        <Header
          title={t('autoPayIHCFlow.turnOff.title')}
          subtitle={t('autoPayIHCFlow.turnOff.subtitle')}
          onClose={onClose}
        />
      }
      footer={
        <Footer
          onKeepAutoPayEnabled={onKeepAutoPayEnabled}
          onTurnOffAutoPay={onTurnOffAutoPay}
          isLoading={isLoading}
        />
      }
    />
  )
}

interface IFooterProps {
  onKeepAutoPayEnabled: () => void
  onTurnOffAutoPay: () => void
  isLoading: boolean
}

const Footer: React.FC<IFooterProps> = ({
  onKeepAutoPayEnabled,
  onTurnOffAutoPay,
  isLoading,
}) => {
  const isMobile = useIsMobile()
  const { t } = useTranslation('global')
  return (
    <View
      style={isMobile ? styles.mobileButtonContainer : styles.buttonContainer}
    >
      <BButton
        onPress={onTurnOffAutoPay}
        disabled={isLoading}
        loading={isLoading}
        labelStyle={[styles.buttonText, isMobile && styles.mobileButtonText]}
        buttonStyle={[
          styles.button,
          styles.turnOffButton,
          isMobile && styles.mobileButton,
        ]}
        style={isMobile ? styles.mobileButtonWrapper : undefined}
        contentStyle={isMobile ? { marginHorizontal: -10 } : undefined}
      >
        {t('autoPayIHCFlow.turnOff.turnOffButton')}
      </BButton>

      <BButton
        onPress={onKeepAutoPayEnabled}
        disabled={isLoading}
        labelStyle={[styles.buttonText, isMobile && styles.mobileButtonText]}
        buttonStyle={[styles.button, isMobile && styles.mobileButton]}
        style={isMobile ? styles.mobileButtonWrapper : undefined}
      >
        {isMobile
          ? t('autoPayIHCFlow.turnOff.keepAutoPayButton')
          : t('autoPayIHCFlow.turnOff.keepAutoPayEnabledButton')}
      </BButton>
    </View>
  )
}

interface IHeaderProps {
  title: string
  subtitle?: string
  onClose?: () => void
}

const Header: React.FC<IHeaderProps> = ({ title, subtitle, onClose }) => {
  const isMobile = useIsMobile()

  return (
    <View style={isMobile ? styles.mobileContainer : styles.container}>
      <View style={[styles.header, isMobile && styles.mobileHeader]}>
        <View
          style={[styles.textContainer, isMobile && styles.mobileTextContainer]}
        >
          <BtText style={[styles.title, isMobile && styles.mobileTitle]}>
            {title}
          </BtText>
          <Spacer height={isMobile ? 15 : 10} />
          {subtitle && (
            <BtText
              style={[styles.subtitle, isMobile && styles.mobileSubtitle]}
            >
              {subtitle}
            </BtText>
          )}
        </View>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <ModalCloseIcon width={40} height={40} />
        </TouchableOpacity>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 14,
  },
  container: {
    maxWidth: 570,
  },
  textContainer: {
    flex: 1,
    width: '100%',
  },
  closeButton: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  title: {
    fontFamily: 'Inter',
    fontWeight: '700',
    fontSize: 24,
    lineHeight: 34,
    letterSpacing: -0.5,
    color: '#001929',
    maxWidth: '75%',
  },
  subtitle: {
    fontFamily: 'Inter',
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 24,
    color: '#001929',
    letterSpacing: -0.5,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    paddingHorizontal: 30,
    justifyContent: 'flex-end',
    height: 75,
  },
  mobileButtonContainer: {
    flexDirection: 'row',
    width: '100%',
    gap: 12,
    alignItems: 'stretch',
    borderTopColor: '#DEE5EB',
    borderTopWidth: 1,
    padding: 16,
  },
  turnOffButton: {
    backgroundColor: '#EC002A',
  },
  button: {
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  buttonText: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '700',
    fontFamily: 'Inter',
  },
  mobileButtonText: {
    letterSpacing: 0,
  },
  mobileButton: {
    height: 48,
    paddingHorizontal: 0,
    width: '100%',
  },
  mobileButtonWrapper: {
    flex: 1,
    alignItems: 'stretch',
  },
  mobileContainer: {
    width: '100%',
    marginLeft: -30,
  },
  mobileTextContainer: {
    maxWidth: '100%',
  },
  mobileTitle: {
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: -0.3,
    maxWidth: '85%',
  },
  mobileSubtitle: {
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0,
  },
  mobileHeader: {
    paddingHorizontal: 20,
  },
})

import { observer } from 'mobx-react'
import { Instance } from 'mobx-state-tree'
import LoanStatusDetailsStore, { ILoan } from './LoanStatusDetailsStore'
import React, { useCallback, useState } from 'react'
import {
  LOAN_REPAYMENT_NAME,
  LOAN_REPAYMENT_SCHEDULE_STATUS,
  LOAN_REPAYMENT_STATUS,
  LOAN_REPAYMENT_TYPE,
} from '@linqpal/models/src/dictionaries/loanStatuses'
import EditableCell from './components/EditableCell'
import moment from 'moment-timezone'
import numbro from 'numbro'
import {
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CCardTitle,
  CCol,
  CDataTable,
  CLabel,
  CRow,
} from '@coreui/react'
import EditPaymentScheduleModal from './modals/EditPaymentScheduleModal'
import { AmountCell } from './components/AmountCell'
import NewPaymentPlanModal from './modals/NewPaymentPlanModal'
import AddFeeModal from './modals/AddFeeModal'
import EditFeeAmountModal from './modals/EditFeeAmountModal'
import { DisbursementPauseSwitch } from './components/DisbursementPauseSwitch'
import { AutoCollectionPauseSwitch } from './components/AutoCollectionPauseSwitch'

export const ReceivablesTable = observer(function PaymentPlan(props: {
  store: Instance<typeof LoanStatusDetailsStore>
  fetchDetails: () => void
}) {
  enum PopupTypes {
    None,
    ExpectedAmount,
    ExpectedDate,
    AddFee,
    SetNewPaymentPlan,
  }

  const { store, fetchDetails } = props
  const [selectedLoan, setSelectedLoan] = useState<null | ILoan>(null)
  const [popupType, setPopupType] = useState(PopupTypes.None)

  const isExpectedAmtEditable = (item) =>
    (![LOAN_REPAYMENT_STATUS.PAID, LOAN_REPAYMENT_STATUS.CANCELED].includes(
      item.status,
    ) &&
      [
        LOAN_REPAYMENT_TYPE.EXTENSION_FEE,
        LOAN_REPAYMENT_TYPE.PENALTY_INTEREST_FEE,
      ].includes(item.type)) ||
    (![LOAN_REPAYMENT_STATUS.CANCELED].includes(item.status) &&
      [
        LOAN_REPAYMENT_TYPE.LATE_FEE,
        LOAN_REPAYMENT_TYPE.MANUAL_LATE_FEE,
      ].includes(item.type))

  const paymentPlanFields = [
    'date',
    'expectedAmount',
    'amountPaid',
    'paymentDate',
    'adjustAmount',
    'adjustDate',
    'outstandingBalance',
    'repaymentStatus',
    'scheduleStatus',
    'type',
  ]

  const formatRepaymentStatus = (installment) => {
    const { status, expectedDate, paidDate } = installment
    const currentDate = moment().utc().startOf('day')
    const isLate =
      status === LOAN_REPAYMENT_STATUS.LATE ||
      (moment.tz(expectedDate, 'UTC').isBefore(currentDate) &&
        status === LOAN_REPAYMENT_STATUS.PENDING)

    if (isLate) {
      return LOAN_REPAYMENT_STATUS.LATE
    } else if (status === LOAN_REPAYMENT_STATUS.PAID) {
      const isPaidLate = moment(expectedDate) < moment(paidDate)
      return isPaidLate ? 'Paid Late' : LOAN_REPAYMENT_STATUS.PAID
    } else {
      return status
    }
  }

  const paymentPlanScopedSlots = {
    date: (item) => (
      <EditableCell
        value={moment(item.expectedDate).format('MM/DD/YYYY')}
        onPress={() => setPopupType(PopupTypes.ExpectedDate)}
        editable={
          item.scheduleStatus === LOAN_REPAYMENT_SCHEDULE_STATUS.CURRENT &&
          item.status === LOAN_REPAYMENT_STATUS.PENDING
        }
      />
    ),
    expectedAmount: (item) => (
      <EditableCell
        value={numbro(item.expectedAmount).formatCurrency({
          mantissa: 2,
          thousandSeparated: true,
        })}
        onPress={() => setPopupType(PopupTypes.ExpectedAmount)}
        editable={isExpectedAmtEditable(item)}
        className="text-monospace text-right"
      />
    ),
    amountPaid: (item) => <AmountCell value={item.paidAmount} />,
    paymentDate: (item) => (
      <td>{item.paidDate ? moment(item.paidDate).format('MM/DD/YYYY') : ''}</td>
    ),
    adjustAmount: (item) => <AmountCell value={item.adjustAmount} />,
    adjustDate: (item) => (
      <td>
        {item.adjustDate ? moment(item.adjustDate).format('MM/DD/YYYY') : ''}
      </td>
    ),
    outstandingBalance: (item, i) => (
      <AmountCell
        value={LoanStatusDetailsStore.principleBalance(item.paidAmount, i)}
      />
    ),
    repaymentStatus: (item) => <td>{formatRepaymentStatus(item)}</td>,
    scheduleStatus: (item) => <td>{item.scheduleStatus}</td>,
    type: (item) => <td>{LOAN_REPAYMENT_NAME[item.type]}</td>,
  }

  const onModalClose = useCallback(() => {
    setSelectedLoan(null)
    setPopupType(PopupTypes.None)
  }, [PopupTypes.None])

  return (
    <CCard>
      <CCardHeader>
        <CRow>
          <CCol>
            <CCardTitle className="mb-0">Installments</CCardTitle>
          </CCol>
        </CRow>
        <CRow className="mt-3">
          <CCol style={{ flex: '0 0 200px' }}>
            <CButton
              style={{ width: 150 }}
              color="primary"
              disabled={store.loanPrincipalBalance === 0}
              onClick={() => setPopupType(PopupTypes.SetNewPaymentPlan)}
            >
              New Payment Plan
            </CButton>
            <div className="mb-2" />
            <CButton
              color="primary"
              className="mr-2"
              style={{ width: 150 }}
              onClick={() => setPopupType(PopupTypes.AddFee)}
            >
              Add Fee
            </CButton>
          </CCol>
          <CCol style={{ flex: '0 0 210px' }} className="col-form-label">
            <CLabel className="font-weight-bold">Auto collection paused</CLabel>
            <div>
              Last modified by:{' '}
              {store.item?.lms?.autoCollectionPausedBy
                ? store.item?.lms?.autoCollectionPausedBy
                : 'n/a'}
            </div>
            <div>
              Date:{' '}
              {store.item?.lms?.autoCollectionPausedAt
                ? moment(store.item?.lms?.autoCollectionPausedAt).format(
                    'MM/DD/YYYY HH:mm',
                  )
                : 'n/a'}
            </div>
          </CCol>
          <CCol style={{ flex: '0 0 150px' }} className="my-auto">
            <AutoCollectionPauseSwitch
              store={store}
              fetchDetails={fetchDetails}
              onSuccess={(isPaused: boolean) => {
                console.log('Auto collection toggled:', isPaused)
              }}
            />
          </CCol>
          <DisbursementPauseSwitch store={store} />
        </CRow>
      </CCardHeader>
      <CCardBody>
        <CDataTable
          fields={paymentPlanFields}
          items={store.item?.lms?.loanReceivables.slice()}
          onRowClick={setSelectedLoan}
          scopedSlots={paymentPlanScopedSlots}
          border
          hover
          size="sm"
          striped
        />
      </CCardBody>
      {selectedLoan && popupType === PopupTypes.ExpectedDate && (
        <EditPaymentScheduleModal
          loanId={LoanStatusDetailsStore.item?.lms?.id || ''}
          payment={selectedLoan}
          onClose={onModalClose}
          onSuccess={fetchDetails}
        />
      )}
      {selectedLoan && popupType === PopupTypes.ExpectedAmount && (
        <EditFeeAmountModal
          onClose={onModalClose}
          payment={selectedLoan}
          fetchDetails={fetchDetails}
        />
      )}
      {popupType === PopupTypes.SetNewPaymentPlan && (
        <NewPaymentPlanModal
          store={store}
          onClose={onModalClose}
          onSuccess={fetchDetails}
        />
      )}
      {popupType === PopupTypes.AddFee && (
        <AddFeeModal
          loanId={LoanStatusDetailsStore.item?.lms?.id || ''}
          onClose={onModalClose}
          onSuccess={fetchDetails}
        />
      )}
    </CCard>
  )
})

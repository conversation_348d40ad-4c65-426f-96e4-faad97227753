import React from 'react'
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs'

interface IHCPaymentTab {
  name: string
  component: React.ComponentType<any>
  options: Record<string, unknown>
}

interface IHCPaymentTabNavigatorProps {
  initialTab: string
  tabs: IHCPaymentTab[]
}

const { Navigator, Screen } = createMaterialTopTabNavigator()

export const IHCPaymentTabNavigator = ({
  tabs,
  initialTab,
}: IHCPaymentTabNavigatorProps) => {
  const tabBarOptions = {
    indicatorStyle: {
      backgroundColor: '#00A0F3',
      opacity: 1,
    },
    tabStyle: {
      width: 'auto',
      paddingHorizontal: 20,
    },
    labelStyle: {
      textTransform: 'none' as const,
      fontWeight: '500' as const,
      fontSize: 14,
      lineHeight: 24,
      fontFamily: 'Inter, sans-serif',
    },
    activeTintColor: '#00A0F3',
    inactiveTintColor: '#394D5A',
    style: {
      marginBottom: 24,
    },
  }

  return (
    <Navigator
      initialRouteName={initialTab}
      tabBarOptions={tabBarOptions}
      sceneContainerStyle={{
        backgroundColor: '#FFF',
      }}
      swipeEnabled={false}
    >
      {tabs.map((item) => (
        <Screen
          key={item.name}
          name={item.name}
          component={item.component}
          options={item.options}
        />
      ))}
    </Navigator>
  )
}

import React, { useState } from 'react'
import { View, StyleSheet } from 'react-native'
import { BtText } from '@linqpal/components/src/ui'

interface IHCPaymentTab {
  name: string
  component: React.ComponentType<any>
  options: Record<string, unknown>
}

interface IHCPaymentTabNavigatorProps {
  initialTab: string
  tabs: IHCPaymentTab[]
}

interface SwitchProps {
  active: string
  onPress: (tabName: string) => void
}

const SwitchItem = ({
  active,
  onPress,
  tab,
}: SwitchProps & { tab: IHCPaymentTab }) => {
  return (
    <BtText
      onPress={() => onPress(tab.name)}
      style={[
        styles.switchItem,
        active === tab.name && styles.switchItemActive,
      ]}
    >
      {tab.options.title as string}
    </BtText>
  )
}

export const IHCPaymentTabNavigator = ({
  tabs,
  initialTab,
}: IHCPaymentTabNavigatorProps) => {
  const [activeTab, setActiveTab] = useState(initialTab)

  const handleTabPress = (tabName: string) => {
    setActiveTab(tabName)
  }

  const activeTabComponent = tabs.find(
    (tab) => tab.name === activeTab,
  )?.component

  return (
    <View style={styles.container}>
      <View style={styles.switchContainer}>
        {tabs.map((tab) => (
          <SwitchItem
            key={tab.name}
            active={activeTab}
            onPress={handleTabPress}
            tab={tab}
          />
        ))}
      </View>
      <View style={styles.contentContainer}>
        {activeTabComponent && React.createElement(activeTabComponent)}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  switchContainer: {
    height: 45,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 2,
    marginBottom: 24,
    paddingHorizontal: 4,
    paddingVertical: 4,
    backgroundColor: '#EFF5F9',
    borderRadius: 9,
    marginHorizontal: 2,
  },
  switchItem: {
    paddingVertical: 8,
    fontSize: 14,
    width: '49%',
    textAlign: 'center',
    borderRadius: 8,
    fontWeight: '500',
    color: '#335C75',
    fontFamily: 'Inter',
    lineHeight: 21,
  },
  switchItemActive: {
    color: '#00A0F3',
    fontWeight: '600',
    backgroundColor: '#FFFFFF',
  },
  contentContainer: {
    flex: 1,
    paddingRight: 5,
  },
})

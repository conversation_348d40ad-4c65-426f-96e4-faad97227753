import {
  Company,
  getPlan,
  initializeFinicity,
  LMS,
  LoanApplication,
  Operation,
  PlaidService,
  Transaction,
  User,
  UserRole,
} from '@linqpal/common-backend'
import { exceptions, RoleStatus } from '@linqpal/models'
import { LOAN_APPLICATION_STATUS } from '@linqpal/models/src/dictionaries'
import { LOAN_STATUS } from '@linqpal/models/src/dictionaries/loanStatuses'
import { Request, Response } from 'express'

const { LogicalError } = exceptions

export default async function getLoanStatus(req: Request, res: Response) {
  const { asc = 'true' } = req.query || {}
  const search = (req.query?.search as string) || ''
  const sort = (req.query?.sort as string) || 'issueDate'
  const page = parseInt(req.query?.page as string) || 1
  const limit = parseInt(req.query?.limit as string) || 10
  const isOverdue = (req.query?.isOverdue as string) || ''
  const lmsLoanStatus = (req.query?.loanStatus as string) || ''
  const drawApprovalId = (req.query?.drawApprovalId as string) || ''

  console.log(drawApprovalId)

  const aggregate = LoanApplication.aggregate([])
    .match({
      $or: [
        {
          status: {
            $in: [
              LOAN_APPLICATION_STATUS.APPROVED,
              LOAN_APPLICATION_STATUS.CLOSED,
            ],
          },
        },
        {
          // TODO: VK: Introduce new loan app's PENDING_DISBURSEMENT status for ATC version #2
          $and: [
            { 'metadata.repayment.autoTradeCreditEnabled': true },
            {
              status: {
                $in: [
                  LOAN_APPLICATION_STATUS.PROCESSING,
                  LOAN_APPLICATION_STATUS.CANCELED,
                ],
              },
            },
          ],
        },
      ],
      lms_id: { $exists: true },
      issueDate: { $ne: null },
      $expr: {
        $and: [
          { $ne: ['$lms_id', null] },
          { $ne: ['$lms_id', 0] },
          { $ne: ['$lms_id', '0'] },
        ],
      },
    })
    .addFields({ company_id: { $toObjectId: '$company_id' } })
    .lookup({
      from: Company.collection.name,
      localField: 'company_id',
      foreignField: '_id',
      as: 'company',
    })
    .unwind('$company')
    .addFields({ companyName: '$company.name' })

  if (search.trim().length > 0) {
    aggregate.match({
      $or: [
        { companyName: { $regex: search.trim(), $options: 'i' } },
        { lms_id: { $regex: search.trim(), $options: 'i' } },
      ],
    })
  }

  if (isOverdue.trim().length > 0) {
    aggregate.match({
      isOverdue: { $eq: isOverdue === 'true' },
    })
  }

  if (lmsLoanStatus.trim().length > 0) {
    aggregate.match({
      lmsLoanStatus: { $eq: lmsLoanStatus },
    })
  }

  if (drawApprovalId.trim().length > 0) {
    aggregate.match({
      drawApprovalId: { $eq: drawApprovalId },
    })
  }

  aggregate.project({
    _id: 1,
    issueDate: 1,
    companyName: 1,
    approvedAmount: { $ifNull: ['$approvedAmount', 0] },
    usedAmount: 1,
    nextPaymentAmount: { $ifNull: ['$nextPaymentAmount', '0'] },
    nextPaymentDate: { $ifNull: ['$nextPaymentDate', ''] },
    amountDue: { $ifNull: ['$amountDue', '0'] },
    lastPaymentDate: { $ifNull: ['$lastPaymentDate', ''] },
    'invoiceDetails.paymentPlan': 1,
    status: 1,
    lms_id: 1,
    metadata: 1,
  })

  const [result]: Array<any> = await LoanApplication.aggregate(
    aggregate.pipeline(),
  ).count('total')

  let items = await aggregate
    .sort({ [sort]: asc !== 'false' ? -1 : 1 })
    .skip((page - 1) * limit)
    .limit(+limit)

  const loanIds = items.map((e) => ({
    id: e.lms_id,
  }))
  const loans = await LMS.getLoansByIds({ ids: loanIds })

  items = await Promise.all(
    items.map(async (item) => {
      const lms = loans.find((loan) => loan.id === item.lms_id)
      item.lms_status = item.lms_status ?? lms?.status
      const daysLate = lms?.loanDetails?.businessDaysLate ?? ''
      const lateAmount = lms?.loanDetails?.lateAmount
      const previousPayment =
        lms &&
        lms?.loanReceivables
          .slice()
          .reverse()
          .find((p) => !!p.paidDate)
      const lastReceivable = lms?.loanReceivables.slice().reverse()[0]
      const nextPaymentAmount = lms?.loanDetails?.nextPaymentAmount
      item.nextPaymentAmount = nextPaymentAmount
      return {
        ...item,
        loanOption:
          item.metadata?.paymentPlan ||
          (await getPlan(item.invoiceDetails?.paymentPlan).catch((e) =>
            console.log(e),
          )),
        nextPaymentAmount,
        lastReceivable,
        daysLate,
        nextPaymentDate: lms?.nextPaymentDate,
        lastPaymentDate: previousPayment?.paidDate,
        lateAmount: lateAmount,
        repaymentStatus:
          lms?.status === LOAN_STATUS.CLOSED
            ? 'Paid off'
            : lms?.isAnyPaymentMissed
            ? 'Late'
            : 'Current',
      }
    }),
  )

  res.send({ items, total: result?.total })
}

export async function loanStatusDetails(req: Request, res: Response) {
  const app = await LoanApplication.findOne({ _id: req.query.id }).exec()

  if (!app) throw new exceptions.LogicalError('Loan application not found')

  const company = app?.company_id
    ? await Company.findById(app.company_id).populate('bankAccounts')
    : null
  const roles = await UserRole.find({
    role: 'Owner',
    company_id: company?._id,
    status: RoleStatus.Active,
  }).exec()
  const owners = await User.aggregate([])
    .match({ sub: { $in: roles.map((r) => r.sub) } })
    .project({ sub: 1, email: 1, phone: 1, firstName: 1, lastName: 1 })

  const item: any = {
    ...app?.toObject(),
    issueDate: app?.issueDate || app?.updatedAt,
    company: {
      ...company?.toObject(),
      owners,
    },
  }
  const loan_id = app?.lms_id
  if (loan_id) {
    const resp = await LMS.getLoanInfo(loan_id)

    let autoCollectionPausedByName: string | undefined | null = null
    if (resp?.autoCollectionPausedBy) {
      try {
        const user = await User.findById(resp.autoCollectionPausedBy)
        if (user) {
          autoCollectionPausedByName =
            `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.name
        }
      } catch (error) {
        console.warn(
          'Failed to fetch user for autoCollectionPausedBy:',
          resp.autoCollectionPausedBy,
          error,
        )
      }
    }

    item.lms = {
      ...resp,
      autoCollectionPausedBy: autoCollectionPausedByName,
    }

    const payments = await LMS.getPaymentsByLoanId(loan_id)
    item.payments = payments
    const notes = await LMS.getNotes(loan_id)
    const updated = await Promise.all(
      notes?.map(async (n) => {
        const [user] = await User.find({ sub: n.userId })
        return {
          ...n,
          userName: user ? `${user.firstName} ${user.lastName}` : n.userId,
        }
      }) || [],
    )
    item.notes = updated
  }
  const operations = await Operation.find({ owner_id: app.id })
  item.operations = await Promise.all(
    operations.map(async (operation) => ({
      ...operation.toObject(),
      transactions: await Transaction.find({ operation_id: operation.id }),
    })),
  )
  res.send({ item })
}

export async function getLoanAvailableBalance(req: Request, res: Response) {
  const company_id = req.query.company_id
    ? req.query.company_id
    : (await LoanApplication.findById(req.query.id))?.company_id

  const company = company_id
    ? await Company.findById(company_id).populate('bankAccounts')
    : null

  const plaid_account = company?.bankAccounts?.find(
    (account) =>
      (account.isPrimaryForCredit ||
        ((account.isPrimaryForCredit === null ||
          account.isPrimaryForCredit === undefined) &&
          account.isPrimary)) &&
      account.plaid?.account_id &&
      account.plaid?.access_token,
  )

  if (plaid_account) {
    const availableBalance =
      (await PlaidService.getAvailableBalance(company!.id, plaid_account)) || 0

    res.send({ availableBalance })
    return
  }

  const accountId = company?.bankAccounts?.find((account) => {
    return account.finicity?.accountId && account.isPrimary
  })?.finicity?.accountId
  const customerId = company?.finicity?.customerId

  if (!customerId || !accountId)
    throw new LogicalError("Customer ID or account doesn't exist")

  const finicity = await initializeFinicity()

  let tries = 2
  while (tries > 0) {
    try {
      const { availableBalance } = await finicity.accounts.availableBalanceLive(
        customerId,
        accountId,
      )
      res.send({ availableBalance })
      return
    } catch (e) {
      console.log(e)
      tries--
    }
  }
  if (tries <= 0) {
    const { availableBalance } = await finicity.accounts.availableBalance(
      customerId,
      accountId,
    )
    res.send({ availableBalance })
  }
}

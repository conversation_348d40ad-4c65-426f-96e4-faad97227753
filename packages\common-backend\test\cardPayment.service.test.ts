import * as cardPaymentService from '../src/services/cardPayment.service'
import chai from 'chai'
import mongoose from 'mongoose'
import {
  BankAccount,
  CardPricingPackage,
  Company,
  CustomerAccount,
  Invoice,
  Operation,
} from '..'
import {
  IBankAccount,
  ICompany,
  ICustomerAccount,
  IInvoice,
} from '../src/models/types'
import { AwsService } from '../index'
import { OPERATION_STATUS } from '@linqpal/models/src/dictionaries'
import nock from 'nock'
import { LogicalError } from '@linqpal/models/src/types/exceptions'
import smsService from '../src/services/sms.service'
import SgMail from '@sendgrid/mail'
import * as sinon from 'sinon'
import { dictionaries } from '@linqpal/models'
import chaiPromise from 'chai-as-promised'
import { Transaction } from '../src/models'
import { PublishCommand, SNSClient } from '@aws-sdk/client-sns'
import { mockClient } from 'aws-sdk-client-mock'
import {
  EventBridgeClient,
  PutEventsCommand,
} from '@aws-sdk/client-eventbridge'

chai.use(chaiPromise)
chai.should()

describe('Card Payment Service', function () {
  const tabapayBaseUrl = 'https://api.sandbox.tabapay.net:10443'
  process.env.LP_TABAPAY_CLIENT_ID = 'test-client-id'
  process.env.LP_TABAPAY_BEARER_TOKEN = 'test-token'
  process.env.LP_TABAPAY_SETTLEMENT_ACCOUNT_ID = 'test-settlement-account-id'
  process.env.LP_TABAPAY_MID_NO_CONVENIENCE_FEE = 'test-mid-0001'
  process.env.LP_TABAPAY_MID_WITH_CONVENIENCE_FEE = 'test-mid-0002'

  const cusomerPhone = '+***********'
  const customerEmail = '<EMAIL>'
  let supplierCompany: ICompany
  let customerAccount: ICustomerAccount
  let cardBankAccount: IBankAccount
  let otherBankAccount: IBankAccount
  let invoice: IInvoice

  beforeEach(async () => {
    supplierCompany = await Company.create({
      name: 'Supplier',
      address: {
        address: 'Some street',
        city: 'Kansas',
        zip: '11111',
        state: 'KS',
      },
      settings: {
        cardPricingPackageId: 'A',
        supplierCanPay: true,
      },
    })

    cardBankAccount = await BankAccount.create({
      isPrimary: true,
      name: 'Visa Card',
      routingNumber: '********',
      accountType: 'checking',
      paymentMethodType: 'card',
      cardMetadata: {
        accountId: 'tabapay-account-id',
      },
      accountNumber: {
        display: '****1234',
        cipher: '',
        hash: '',
      },
    })

    otherBankAccount = await BankAccount.create({
      isPrimary: true,
      name: 'Visa Card',
      routingNumber: '8945655',
      accountType: 'checking',
      paymentMethodType: 'card',
      cardMetadata: {
        accountId: 'tabapay-account-id2',
      },
    })

    customerAccount = await CustomerAccount.create({
      phone: cusomerPhone,
      email: customerEmail,
      company_id: supplierCompany.id,
      bankAccounts: [cardBankAccount._id],
    })

    invoice = await Invoice.create({
      status: 'PLACED',
      total_amount: 100.0,
      invoice_due_date: '2021-08-11T00:00:00.000Z',
      expiration_date: null,
      customer_account_id: customerAccount._id,
      company_id: supplierCompany._id,
      invoice_number: '555',
    })

    await CardPricingPackage.create({
      name: 'A',
      metadata: {
        ach: {
          merchant: { min: 1, max: 10, percentage: 1 },
          customer: { percentage: 1, amount: 1 },
        },
        creditCardVisa: {
          merchant: { percentage: 1, amount: 1 },
          customer: { percentage: 2, amount: 2 },
        },
        debitCardRegulated: {
          merchant: { percentage: 1, amount: 2 },
          customer: { percentage: 2, amount: 1 },
        },
        debitCardUnregulated: {
          merchant: { percentage: 2, amount: 3 },
          customer: { percentage: 3, amount: 2 },
        },
        creditCardMasterCard2: {
          merchant: { percentage: 3, amount: 4 },
          customer: { percentage: 4, amount: 3 },
        },
        creditCardVisa2: {
          merchant: { percentage: 8, amount: 9 },
          customer: { percentage: 9, amount: 8 },
        },
        creditCardTravel: {
          merchant: { percentage: 4, amount: 5 },
          customer: { percentage: 5, amount: 4 },
        },
        creditCardBusiness: {
          merchant: { percentage: 5, amount: 6 },
          customer: { percentage: 6, amount: 5 },
        },
        amex: {
          merchant: { percentage: 6, amount: 7 },
          customer: { percentage: 7, amount: 6 },
        },
      },
    })
  })

  afterEach(async () => {
    await Invoice.deleteMany()
    await Operation.deleteMany()
    await CustomerAccount.deleteOne({
      _id: new mongoose.Types.ObjectId(customerAccount.id),
    })
    await Company.deleteOne({
      _id: new mongoose.Types.ObjectId(supplierCompany.id),
    })
    await BankAccount.deleteOne({
      _id: new mongoose.Types.ObjectId(cardBankAccount.id),
    })

    await CardPricingPackage.deleteMany()
    nock.cleanAll()
  })

  function mockTabapayTransaction() {
    nock(tabapayBaseUrl)
      .post((uri) => uri.includes('transactions'))
      .reply(200, {
        transactionID: 'tabapay-transaction-id',
        status: 'COMPLETED',
      })
  }

  describe('collectCardPayment tests', function () {
    let smsSend: sinon.SinonStub
    let mailSend: sinon.SinonStub

    beforeEach(async () => {
      smsSend = sinon
        .stub(smsService, 'send')
        .callsFake(() => Promise.resolve())
      mailSend = sinon.stub(SgMail, 'send')
    })

    afterEach(async () => {
      smsSend.restore()
      mailSend.restore()
    })

    it('should collect payment when invoice is correct', async () => {
      mockTabapayTransaction()

      await cardPaymentService.collectCardPayment(
        invoice.id,
        cardBankAccount.id,
        supplierCompany,
        'https://test',
        await mongoose.startSession(),
      )

      const operation = await Operation.findOne({
        owner_id: invoice.id,
      })

      chai.expect(operation?.status).to.be.equal(OPERATION_STATUS.PROCESSING)
      chai.expect(operation?.metadata.payee_id).to.be.equal(supplierCompany.id)
      chai
        .expect(operation?.metadata.payment_method)
        .to.be.equal(dictionaries.PAYMENT_METHODS.CARD)
      chai.expect(operation?.metadata.paymentInitiator).to.be.equal('company')

      const expectedMessage =
        'A payment has been made by Supplier for invoice 555 for $100.00. ' +
        'Your card ending in 1234 has been charged. Click the link below to see the details' +
        `, https://test/receipt?id=${invoice.id}`
      smsSend
        .calledOnceWith(cusomerPhone, expectedMessage)
        .should.be.equal(true)

      mailSend
        .calledOnceWith({
          to: customerEmail,
          subject: 'Thank you for your invoice payment!',
          text: expectedMessage,
          from: { name: 'BlueTape Inc', email: '<EMAIL>' },
        })
        .should.be.equal(true)
    })

    it('should collect payment - verify fees and amount', async () => {
      mockTabapayTransaction()
      const phone = '+***********'
      const email = '<EMAIL>'

      cardBankAccount = await BankAccount.create({
        isPrimary: true,
        name: 'Amex Card',
        routingNumber: '********',
        accountType: 'checking',
        paymentMethodType: 'card',
        cardMetadata: {
          accountId: 'tabapay-account-id-2',
          network: 'Amex',
        },
        accountNumber: {
          display: '****1234',
          cipher: '',
          hash: '',
        },
      })

      supplierCompany = await Company.create({
        name: 'Supplier',
        address: {
          address: 'Some street',
          city: 'Kansas',
          zip: '11111',
          state: 'KS',
        },
        settings: {
          cardPricingPackageId: 'B',
          supplierCanPay: true,
        },
      })

      await CardPricingPackage.create({
        name: 'B',
        metadata: {
          amex: {
            merchant: { percentage: 3.5, amount: 0.3 },
            customer: { percentage: 0, amount: 0 },
          },
        },
      })

      customerAccount = await CustomerAccount.create({
        phone,
        email,
        company_id: supplierCompany.id,
        bankAccounts: [cardBankAccount._id],
      })

      invoice = await Invoice.create({
        status: 'PLACED',
        total_amount: 125.0,
        invoice_due_date: '2021-08-11T00:00:00.000Z',
        expiration_date: null,
        customer_account_id: customerAccount._id,
        company_id: supplierCompany._id,
        invoice_number: '555',
      })

      await cardPaymentService.collectCardPayment(
        invoice.id,
        cardBankAccount._id.toString(),
        supplierCompany,
        'https://test',
        await mongoose.startSession(),
      )

      const operation = await Operation.findOne({
        owner_id: invoice.id,
      })

      const transactions = await Transaction.find({
        operation_id: operation?._id.toString(),
      })

      chai.expect(transactions.length).to.be.equal(2)
      chai.expect(transactions[0]?.amount).to.be.equal(125)
      chai.expect(transactions[0]?.fee).to.be.equal(0)

      chai.expect(transactions[1]?.amount).to.be.equal(120.32)
      chai.expect(transactions[1]?.fee).to.be.equal(4.68) // (125 * 3.5)/100 + 0.3 = rounded to 4.68

      chai.expect(operation?.status).to.be.equal(OPERATION_STATUS.PROCESSING)
      chai.expect(operation?.metadata.payee_id).to.be.equal(supplierCompany.id)
      chai
        .expect(operation?.metadata.payment_method)
        .to.be.equal(dictionaries.PAYMENT_METHODS.CARD)
      chai.expect(operation?.metadata.paymentInitiator).to.be.equal('company')

      const expectedMessage =
        'A payment has been made by Supplier for invoice 555 for $125.00. ' +
        'Your card ending in 1234 has been charged. Click the link below to see the details' +
        `, https://test/receipt?id=${invoice.id}`
      smsSend.calledOnceWith(phone, expectedMessage).should.be.equal(true)

      mailSend
        .calledOnceWith({
          to: email,
          subject: 'Thank you for your invoice payment!',
          text: expectedMessage,
          from: { name: 'BlueTape Inc', email: '<EMAIL>' },
        })
        .should.be.equal(true)
    })

    it('should not collect payment when Tabapay returns error', async () => {
      nock(tabapayBaseUrl)
        .post((uri) => uri.includes('transactions'))
        .reply(500, {})

      const promise = cardPaymentService.collectCardPayment(
        invoice.id,
        cardBankAccount.id,
        supplierCompany,
        'https://test',
        await mongoose.startSession(),
      )

      chai.expect(promise).to.eventually.be.rejectedWith(LogicalError)
    })

    it('should not collect payment when incorrect account provided', async () => {
      mockTabapayTransaction()

      const promise = cardPaymentService.collectCardPayment(
        invoice.id,
        otherBankAccount.id,
        supplierCompany,
        'https://test',
        await mongoose.startSession(),
      )

      chai
        .expect(promise)
        .to.eventually.be.rejectedWith('Bank account not found')
    })

    it('should not collect payment when operation not allowed', async () => {
      mockTabapayTransaction()

      supplierCompany.settings.supplierCanPay = false

      const promise = cardPaymentService.collectCardPayment(
        invoice.id,
        cardBankAccount.id,
        supplierCompany,
        'https://test',
        await mongoose.startSession(),
      )

      chai
        .expect(promise)
        .to.eventually.be.rejectedWith('Operation is not supported')
    })
  })

  describe('makeCardPayment tests', function () {
    let customerCompany: ICompany
    let payedInvoice: IInvoice
    let snsMock: ReturnType<typeof mockClient>
    let mailSend: sinon.SinonStub
    let eventBridgeMock: ReturnType<typeof mockClient>

    beforeEach(async () => {
      snsMock = mockClient(SNSClient)
      mailSend = sinon.stub(SgMail, 'send')

      snsMock.on(PublishCommand).resolves({
        MessageId: 'mock-message-id',
      })
      eventBridgeMock = mockClient(EventBridgeClient)
      eventBridgeMock.on(PutEventsCommand).callsFake(() => {
        return null
      })
      customerCompany = await Company.create({
        name: 'Customer',
        address: {
          address: 'Some street2',
          city: 'Kansas',
          zip: '11111',
          state: 'KS',
        },
        settings: {
          cardPricingPackageId: 'A',
        },
        bankAccounts: [otherBankAccount.id],
      })

      payedInvoice = await Invoice.create({
        status: 'PLACED',
        total_amount: 100.0,
        invoice_due_date: '2021-08-11T00:00:00.000Z',
        expiration_date: null,
        customer_account_id: customerAccount._id,
        company_id: supplierCompany._id,
        invoice_number: '777',
      })

      await Operation.create({
        owner_id: payedInvoice.id,
        amount: payedInvoice.total_amount,
        date: '2021-08-11T00:00:00.000Z',
        type: dictionaries.OPERATION_TYPES.INVOICE.PAYMENT,
        status: dictionaries.OPERATION_STATUS.PROCESSING,
      })
    })

    afterEach(async () => {
      snsMock.reset()
      eventBridgeMock.reset()
      mailSend.restore()
      await Company.deleteOne({
        _id: new mongoose.Types.ObjectId(customerCompany.id),
      })
    })

    it('should make payment for correct invoice', async function (this: Mocha.Context) {
      this.timeout(60000)
      const session = await mongoose.startSession()
      session.startTransaction()

      try {
        mockTabapayTransaction()
        sinon.stub(AwsService, 'sendSQSMessage')

        await cardPaymentService.makeCardPayment({
          invoices: [invoice],
          accountId: otherBankAccount.id,
          payerCompanyId: customerCompany.id,
          session,
          userId: '',
          isAutoDebit: false,
        })

        const operation = await Operation.findOne({
          owner_id: invoice.id,
        })

        chai.expect(operation?.status).to.be.equal(OPERATION_STATUS.PROCESSING)
        chai
          .expect(operation?.metadata.payee_id)
          .to.be.equal(supplierCompany.id)
        chai
          .expect(operation?.metadata.payer_id)
          .to.be.equal(customerCompany.id)
        chai
          .expect(operation?.metadata.payment_method)
          .to.be.equal(dictionaries.PAYMENT_METHODS.CARD)
        chai
          .expect(operation?.metadata.paymentInitiator)
          .to.be.equal('customer')

        await session.commitTransaction()
      } catch (err) {
        await session.abortTransaction()
        throw err
      } finally {
        session.endSession()
        sinon.reset()
      }
    })

    it('should not make payment when invoice already payed', async () => {
      mockTabapayTransaction()

      const promise = cardPaymentService.makeCardPayment({
        invoices: [payedInvoice],
        accountId: otherBankAccount.id,
        payerCompanyId: customerCompany.id,
        session: await mongoose.startSession(),
        userId: '',
        isAutoDebit: false,
      })

      chai.expect(promise).to.eventually.be.rejectedWith('Invoice already paid')
    })
  })
})

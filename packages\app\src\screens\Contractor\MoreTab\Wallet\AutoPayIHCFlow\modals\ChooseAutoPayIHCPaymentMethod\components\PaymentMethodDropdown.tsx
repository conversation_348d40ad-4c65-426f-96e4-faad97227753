import React from 'react'
import { BtText, BtSelectDropdown } from '@linqpal/components/src/ui'
import { BankAccountPaymentMethods } from '@linqpal/models/src/dictionaries'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity } from 'react-native'

interface PaymentMethodDropdownProps {
  paymentMethods: any[]
  selectedPaymentMethod: any | null
  onSelect: (paymentMethod: any) => void
}

export const PaymentMethodDropdown = ({
  paymentMethods,
  selectedPaymentMethod,
  onSelect,
}: PaymentMethodDropdownProps) => {
  const { t } = useTranslation('global')

  const isCardPaymentMethod = (method: any) => {
    if (!method) return false
    return method.paymentMethodType !== BankAccountPaymentMethods.Bank
  }
  const getDisplayText = (method: any) => {
    if (!method) return ''
    console.log('here method', method)

    const isCard = isCardPaymentMethod(method)

    if (isCard) {
      return t('autoPayIHCFlow.addOrSelectPaymentMethod.cardAccountTitle', {
        cardType: method.cardMetadata?.type ?? 'Credit',
        number: method.cardMetadata?.lastFour ?? '***',
      })
    }
    return t('autoPayIHCFlow.addOrSelectPaymentMethod.bankAccountTitle', {
      number: method.accountNumber?.slice(-4) ?? '***',
    })
  }

  const renderPaymentMethodItem = ({ item, isSelected, onPress }) => {
    return (
      <TouchableOpacity
        style={{
          paddingVertical: 12,
          paddingHorizontal: 16,
          backgroundColor: isSelected ? '#F4F9FD' : '#FFFFFF',
        }}
        onPress={onPress}
      >
        <BtText
          style={{
            fontSize: 14,
            lineHeight: 20,
            color: '#001929',
            fontFamily: 'Inter',
            fontWeight: isSelected ? '700' : '500',
          }}
        >
          {getDisplayText(item)}
        </BtText>
      </TouchableOpacity>
    )
  }

  const renderSelectedItem = (item: any | null) => {
    return (
      <BtText
        style={{
          fontSize: 14,
          lineHeight: 16,
          color: '#001929',
          fontFamily: 'Inter',
          fontWeight: '700',
        }}
      >
        {getDisplayText(item)}
      </BtText>
    )
  }

  return (
    <BtSelectDropdown
      data={paymentMethods}
      selectedItem={selectedPaymentMethod}
      onSelect={onSelect}
      renderItem={renderPaymentMethodItem}
      renderSelectedItem={renderSelectedItem}
      label={t('autoPayIHCFlow.paymentMethodLabel')}
      keyExtractor={(item, index) =>
        item?.id ? String(item.id) : `dropdown-item-${index}`
      }
      isItemSelected={(item, selectedItem) => {
        return selectedItem && selectedItem.id === item.id
      }}
      maxDropdownHeight={200}
      scrollEnabled={paymentMethods.length > 4}
    />
  )
}

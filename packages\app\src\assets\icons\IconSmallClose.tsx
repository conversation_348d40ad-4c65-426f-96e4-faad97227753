/* eslint-disable i18next/no-literal-string */
import React from 'react'
import Svg, { <PERSON>, G, Defs, ClipPath, Rect } from 'react-native-svg'

interface IconCloseProps {
  width?: number
  height?: number
  color?: string
}

export const IconSmallClose: React.FC<IconCloseProps> = ({
  width = 16,
  height = 16,
  color = '#00A0F3',
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 16 16" fill="none">
      <G clipPath="url(#clip0_3707_552)">
        <Path
          d="M12.2 3.80665C11.94 3.54665 11.52 3.54665 11.26 3.80665L7.99998 7.05998L4.73998 3.79998C4.47998 3.53998 4.05998 3.53998 3.79998 3.79998C3.53998 4.05998 3.53998 4.47998 3.79998 4.73998L7.05998 7.99998L3.79998 11.26C3.53998 11.52 3.53998 11.94 3.79998 12.2C4.05998 12.46 4.47998 12.46 4.73998 12.2L7.99998 8.93998L11.26 12.2C11.52 12.46 11.94 12.46 12.2 12.2C12.46 11.94 12.46 11.52 12.2 11.26L8.93998 7.99998L12.2 4.73998C12.4533 4.48665 12.4533 4.05998 12.2 3.80665Z"
          fill={color}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_3707_552">
          <Rect width="16" height="16" fill="white" />
        </ClipPath>
      </Defs>
    </Svg>
  )
}

export default IconSmallClose

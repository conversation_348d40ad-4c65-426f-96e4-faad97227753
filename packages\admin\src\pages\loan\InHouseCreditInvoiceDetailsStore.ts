import {
  BankAccount as BankAccountModel,
  Operation,
  routes,
  Transaction,
} from '@linqpal/models'
import {
  LOAN_PARAMETERS_CHANGE_TYPE,
  LOAN_REPAYMENT_SCHEDULE_STATUS,
  LOAN_REPAYMENT_STATUS,
  LOAN_REPAYMENT_SUB_TYPE,
  LOAN_REPAYMENT_TYPE,
} from '@linqpal/models/src/dictionaries/loanStatuses'
import axios from 'axios'
import {
  cast,
  flow,
  getSnapshot,
  Instance,
  SnapshotOrInstance,
  types,
} from 'mobx-state-tree'
import moment from 'moment-timezone'
import numbro from 'numbro'
import { LoanCustomPaymentPlan } from './LoanCustomPaymentPlanStore'

function asCurrency(value: number | string): string {
  return numbro(value).formatCurrency({
    mantissa: 2,
    thousandSeparated: true,
  })
}

const Receivable = types.model({
  id: types.string,
  expectedDate: types.string,
  paidDate: types.maybeNull(types.string),
  expectedAmount: types.number,
  paidAmount: types.number,
  adjustAmount: types.number,
  adjustDate: types.maybeNull(types.string),
  type: types.enumeration(Object.values(LOAN_REPAYMENT_TYPE)),
  status: types.enumeration(Object.values(LOAN_REPAYMENT_STATUS)),
  loan: types.frozen(),
  loanId: types.string,
  scheduleStatus: types.enumeration(
    Object.values(LOAN_REPAYMENT_SCHEDULE_STATUS),
  ),
})

export interface ILoan extends Instance<typeof Receivable> {}

const Payment = types.model({
  amount: types.number,
  id: types.string,
  date: types.string,
  loanReceivables: types.array(Receivable),
  loan: types.frozen(),
  loanId: types.string,
  status: types.enumeration([
    'None',
    'Success',
    'Rejected',
    'Processing',
    'Canceled',
  ]),
  type: types.enumeration(['AutoDebit', 'Manual', 'Custom']),
  subType: types.enumeration(Object.values(LOAN_REPAYMENT_SUB_TYPE)),
})

export interface IPayment extends Instance<typeof Payment> {}

const LoanParameters = types.model({
  id: types.string,
  loanFeePercentage: types.number,
  lateFeePercentage: types.number,
  installmentsNumber: types.integer,
  paymentIntervalInDays: types.integer,
  isActive: types.boolean,
  minimumLateFeeAmount: types.number,
  gracePeriodInDays: types.integer,
  loanTemplateId: types.maybeNull(types.string),
  note: types.maybeNull(types.string),
  createdBy: types.maybeNull(types.string),
  changeType: types.enumeration(Object.values(LOAN_PARAMETERS_CHANGE_TYPE)),
})

const Note = types.model({
  entityName: types.string,
  propertyName: types.union(types.string, types.null),
  oldValue: types.union(types.string, types.null),
  newValue: types.string,
  changeAt: types.string,
  userId: types.string,
  userName: types.string,
  note: types.string,
})

const LoanDetails = types.model({
  businessDaysLate: types.integer,
  lateAmount: types.number,
  totalProcessingPaymentsAmount: types.number,
  principalBalance: types.number,
  loanOutstandingAmount: types.number,
  totalLoanAmount: types.number,
  totalDailyPenaltyInterest: types.number,
  nextPaymentAmount: types.number,
  nextPaymentDate: types.maybeNull(types.string),
})

const Company = types.model({
  name: types.string,
})

const InHouseInvoiceDetailsItem = types
  .model({
    id: types.string,
    companyId: types.string,
    startDate: types.string,
    amount: types.maybeNull(types.number),
    loanDetails: types.maybeNull(LoanDetails),
    status: types.enumeration([
      'None',
      'Created',
      'Started',
      'Pending',
      'Canceled',
      'Closed',
      'Defaulted',
      'Recovered',
      'Refinanced',
    ]),
    company: types.maybeNull(Company),
    refundAmount: types.number,
    companyOwners: types.array(
      types.model({
        email: types.maybeNull(types.string),
        phone: types.maybeNull(types.string),
        firstName: types.maybeNull(types.string),
        lastName: types.maybeNull(types.string),
      }),
    ),
    credit: types.maybeNull(
      types.model({
        creditDetails: types.model({
          outstandingCredit: types.number,
        }),
        creditLimit: types.maybeNull(types.number),
      }),
    ),
    loanParameters: types.array(LoanParameters),
    loanReceivables: types.array(Receivable),
    operations: types.array(
      types
        .compose(
          Operation,
          types.model({
            transactions: types.array(Transaction),
          }),
        )
        .named('operation'),
    ),
    payments: types.array(Payment),
    notes: types.array(Note),
    metadata: types.optional(types.frozen(), {}),
    companyName: types.maybe(types.string),
    isAutoCollectionPaused: types.optional(types.boolean, false),
    autoCollectionPausedAt: types.maybeNull(types.string),
    autoCollectionPausedBy: types.maybeNull(types.string),
  })
  .named('LoanStatusDetailsItem')

interface ILoanStatusDetailsItem
  extends Instance<typeof InHouseInvoiceDetailsItem> {}

export enum ManualPaymentMethod {
  BankAccount = 'bankAccount',
  ManualBankAccount = 'manualBankAccount',
  ManualCard = 'manualCard',
}

export enum AccountType {
  Savings = 'SAVINGS',
  Checking = 'CHECKING',
}

const PaymentCard = types
  .model({
    cardNumber: types.optional(types.string, ''),
    cardExpire: types.optional(types.string, ''),
    cardToken: types.optional(types.string, ''),
    firstName: types.optional(types.string, ''),
    lastName: types.optional(types.string, ''),
    phone: types.optional(types.string, ''),
  })
  .views((self) => {
    return {
      get isValid() {
        const { cardNumber = '', cardExpire = '', cardToken = '' } = self
        return (
          cardToken ||
          (/[\d\s]/g.test(cardNumber) &&
            cardNumber.replace(/\D/g, '').length === 16 &&
            moment(cardExpire, 'MM/YY').isValid() &&
            moment(cardExpire, 'MM/YY').isSameOrAfter(moment(), 'month'))
        )
      },
    }
  })
  .actions((self) => {
    return {
      setValue(name: string, value: string) {
        self[name] = value
      },
    }
  })
  .named('card')

const LoanManualPaymentItem = types
  .model({
    amount: types.optional(types.string, ''),
    method: types.maybeNull(
      types.enumeration<ManualPaymentMethod>(
        Object.values(ManualPaymentMethod),
      ),
    ),
    bankAccountId: types.maybeNull(types.string),
    manualBankAccount: types.optional(BankAccountModel, () =>
      BankAccountModel.create(),
    ),
    manualCard: types.optional(PaymentCard, () => PaymentCard.create()),
  })
  .views((self) => {
    return {
      get canSubmit() {
        const amount = parseFloat(self.amount)
        return (
          isFinite(amount) &&
          amount > 0 &&
          (self.method === ManualPaymentMethod.BankAccount
            ? !!self.bankAccountId
            : self.method === ManualPaymentMethod.ManualBankAccount
            ? !!(
                self.manualBankAccount?.accountNumber &&
                self.manualBankAccount?.routingNumber
              )
            : self.method === ManualPaymentMethod.ManualCard
            ? self.manualCard.isValid
            : false)
        )
      },
    }
  })
  .actions((self) => {
    return {
      setAmount(amount: string) {
        self.amount = amount
      },
      setMethod(method: ManualPaymentMethod) {
        self.method = method
      },
      setBankAccountId(id: string) {
        self.bankAccountId = id
      },
    }
  })
  .named('LoanManualPaymentItem')

export const InHouseInvoiceDetailsModel = types
  .model({
    loading: false,
    error: '',
    item: types.maybeNull(InHouseInvoiceDetailsItem),
    manualPayment: types.maybeNull(LoanManualPaymentItem),
    customPaymentPlan: types.optional(LoanCustomPaymentPlan, () =>
      LoanCustomPaymentPlan.create(),
    ),
    message: '',
    balance: 0,
    fee: '',
  })
  .views(function views(self) {
    return {
      get itemStartDate() {
        return self.item ? moment(self.item.startDate).format('MM/DD/YYYY') : ''
      },
      get itemCompanyName() {
        return self.item?.company?.name ? self.item.company.name : ''
      },
      get itemApprovedAmount() {
        return self.item ? asCurrency(self.item.amount ?? 0) : ''
      },
      get itemLateAmount() {
        const totalOverdueAmount = self.item?.loanDetails?.lateAmount ?? 0
        return asCurrency(totalOverdueAmount || 0)
      },
      get nextPaymentDisplayAmount() {
        const amt = self.item?.loanDetails?.nextPaymentAmount ?? 0
        return asCurrency(amt)
      },
      get itemNextPaymentDate() {
        const date = self.item?.loanDetails?.nextPaymentDate
        return date ? moment(date).format('MM/DD/YYYY') : ''
      },
      get itemNumberOfDaysLate() {
        return self.item?.loanDetails?.businessDaysLate || 0
      },
      get itemRefundAmount() {
        const amount = self.item?.refundAmount ?? 0
        return Number(numbro(amount).format({ mantissa: 2 }))
      },
      get itemLoanOutstandingAmount() {
        const amount = self.item?.loanDetails?.loanOutstandingAmount
        return Number(numbro(amount).format({ mantissa: 2 }))
      },
      get totalProcessingPayments() {
        const processing = self.item?.loanDetails?.totalProcessingPaymentsAmount
        return Number(numbro(processing).format({ mantissa: 2 }))
      },
    }
  })
  .actions(function (self) {
    return {
      formattedAmount(value) {
        return asCurrency(value || 0)
      },
      fetchData(id: string) {
        self.loading = true
        if (self.item?.id && self.item.id !== id) {
          self.item = null
        }
        const req = routes.admin.inHouseCreditInvoiceDetails(id)
        req.then(this.setData).catch(this.setMessage)
        return req
      },
      setData(data: {
        item: SnapshotOrInstance<typeof InHouseInvoiceDetailsItem>
      }) {
        if (axios.isCancel(data)) return
        self.loading = false
        self.message = ''
        self.item = cast<ILoanStatusDetailsItem>(data.item)
        self.customPaymentPlan = LoanCustomPaymentPlan.create()
      },
      setMetadata(metadata: any) {
        if (self.item) {
          self.item.metadata = {
            ...self.item.metadata,
            ...metadata,
          }
        }
      },
      setPaymentCancelled(isCancelled: boolean) {
        this.setMetadata({ payment_cancelled: isCancelled })
      },
      setSkipFinalPayment(isSkipped: boolean) {
        this.setMetadata({ skip_final_payment: isSkipped })
      },
      setMessage(e) {
        if (axios.isCancel(e)) return
        self.loading = false
        self.message =
          e.response?.data?.message ||
          e.data?.message ||
          e.message ||
          [e.status, e.statusText].filter(Boolean).join(' ') ||
          e.toString()
        self.item = null
      },
      setError(err) {
        self.error = err
      },
    }
  })
  .actions(function actions(self) {
    return {
      setBalance(value) {
        self.balance = value
      },
      principleBalance(amountPaid, index) {
        if (index === 0) {
          const loan = self.item?.loanDetails?.totalLoanAmount || 0
          const refund = self.item?.refundAmount || 0
          this.setBalance(loan + refund)
        }
        const newBal = numbro(self.balance - amountPaid).format({ mantissa: 2 })
        this.setBalance(Number(newBal))
        return newBal
      },
      toggleManualPayment() {
        if (self.manualPayment) {
          self.manualPayment = null
        } else {
          self.manualPayment = LoanManualPaymentItem.create()
        }
      },
      submitManualPayment: flow(function* submitManualPayment() {
        if (!(self.item && self.manualPayment && self.manualPayment.method))
          return
        const data = getSnapshot(self.manualPayment)
        console.log(data)
        yield routes.admin.loanPayment(self.item.id, {
          ...self.manualPayment,
          method: self.manualPayment.method,
        })
        yield self.fetchData(self.item.id)
        self.manualPayment = null
      }),
      calculateFee: flow(function* calculateFee() {
        if (!(self.item?.id && self.manualPayment?.amount)) return
        const res = yield routes.admin.calculateFee(
          self.item?.id,
          self.manualPayment?.amount,
          self.manualPayment?.manualCard.cardToken,
          self.manualPayment?.manualCard.firstName,
          self.manualPayment?.manualCard.lastName,
          self.manualPayment?.manualCard.phone,
        )
        self.fee = res.fee
      }),
    }
  })
  .named('LoanStatusDetailsPageStore')

export default InHouseInvoiceDetailsModel.create()

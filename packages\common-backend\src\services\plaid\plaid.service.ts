import axios, { Method } from 'axios'
import {
  CountryCode,
  InstitutionsSearchRequest,
  InstitutionsSearchResponse,
  LinkTokenCreateRequest,
  LinkTokenCreateResponse,
  Products,
  TransactionsGetResponse,
} from 'plaid'
import { Logger } from '../logger/logger.service'
import { ICompany } from '../../models/types'
import { crypt } from '../../../index'
import { accountBalance } from './api'

const log = new Logger({
  module: 'Plaid',
  subModule: 'plaid.service',
})

const products = [
  Products.Auth,
  Products.Assets,
  Products.Transactions,
  Products.Identity,
]

export const getLinkToken = async (
  companyId: string,
  userId: string,
  highlightRountingNumber?: string,
): Promise<LinkTokenCreateResponse> => {
  console.log('USER ID', companyId)
  const data: Omit<LinkTokenCreateRequest, 'institution_data'> & {
    institutionData?: { routingNumber: string }
  } = {
    user: {
      client_user_id: companyId,
    },
    client_name: 'BlueTape',
    language: 'en',
    products,
    country_codes: [CountryCode.Us],
  }
  if (highlightRountingNumber) {
    data.institutionData = {
      routingNumber: highlightRountingNumber,
    }
  }
  console.log('data', data)
  return requester.post('token/link-token', data, userId)
}

export const exchangePublicToken = async (
  userId: string,
  publicToken: string,
  companyId: string,
) => {
  return requester.post(
    'token/exchange-public',
    { public_token: publicToken },
    userId,
    companyId,
  )
}

export const searchInstitution = async (
  userId: string,
  query: string,
): Promise<InstitutionsSearchResponse> => {
  const data: InstitutionsSearchRequest = {
    query,
    country_codes: [CountryCode.Us],
    products,
  }
  return requester.post('institutions/search', data, userId)
}

export const fetchTransactions = async (
  userId: string,
  token: string,
  params: { startDate: string; endDate: string; count: number; offset: number },
): Promise<TransactionsGetResponse> => {
  const { startDate, endDate, count, offset } = params
  const data = {
    start_date: startDate,
    end_date: endDate,
    options: {
      count,
      offset,
      include_personal_finance_category: true,
      include_original_description: true,
    },
  }
  return requester.post(
    'transactions/get-paged',
    data,
    userId,
    undefined,
    token,
  )
}

export const getLinkTokenInUpdateMode = async (
  access_token: string,
  userId: string,
  companyId: string,
) => {
  const data = {
    user: {
      client_user_id: companyId,
    },
    client_name: 'BlueTape',
    language: 'en',
    country_codes: [CountryCode.Us],
    accessToken: access_token,
  }
  return requester.post('token/link-token/update', data, userId)
}

export const tokenHealthCheck = async (
  access_token: string,
  userId: string,
  plaid: any,
) => {
  const { account_id, item_id } = plaid
  return requester.post(
    'token/health-check',
    undefined,
    userId,
    undefined,
    access_token,
    { accountId: account_id, itemId: item_id },
  )
}

export const getAvailableBalance = async (
  companyId: string,
  plaid_account: NonNullable<ICompany['bankAccounts']>[0],
) => {
  const { account_id: accountId = '', access_token } = plaid_account.plaid || {}
  const accessToken = await crypt.decrypt(access_token.cipher)
  const getBalance = () =>
    accountBalance({
      companyId,
      accountId,
      accessToken,
    })
  let balance = await getBalance()
  let tries = 2
  while ('error' in balance && tries > 0) {
    await new Promise((resolve) => setTimeout(resolve, 1000))
    balance = await getBalance()
    tries--
  }

  if ('error' in balance) {
    log.warn(
      { companyId, accountId, error: balance.error },
      'unable to get available plaid balance',
    )
    return NaN
  }

  const balances = balance?.accounts?.map((a) =>
    Math.min(a.balances?.available || NaN, a.balances?.current || NaN),
  )

  if (!balances) {
    log.warn({ balance }, 'unexpected plaid response')
    return NaN
  }

  return Math.min(...balances)
}

async function request<T = any>(
  method: Method,
  url: string,
  data: any,
  params: any,
  userId?: string,
  companyId?: string,
  accessToken?: string,
  plaid?: { accountId: string; itemId: string },
) {
  const apiKey = process.env.NET_PLAID_APIKEY
  const { accountId, itemId } = plaid ?? {}
  const headers = {
    'Content-Type': 'application/json',
    'X-API-KEY': '',
    'X-User-Id': '',
    'X-Company-Id': '',
    token: '',
    accountId: '',
    itemId: '',
  }

  headers['X-API-KEY'] = apiKey ?? ''
  headers['X-User-Id'] = userId ?? ''
  headers['X-Company-Id'] = companyId ?? ''
  headers.token = accessToken ?? ''
  headers.accountId = accountId ?? ''
  headers.itemId = itemId ?? ''

  if (!data) {
    data = {}
  }
  const companyServiceUrl = process.env.NET_PLAID_API_URL

  console.log('HEADERS', headers)

  const base_url = companyServiceUrl
  if (!params) {
    params = {}
  }

  const fullUrl = `${base_url}/${url}`
  log.debug({ method, url: fullUrl, data, params }, 'Plaid request')
  return axios
    .request<T>({
      method,
      url: fullUrl,
      data: method !== 'get' ? data : null,
      params,
      headers,
    })
    .then((response) => {
      log.debug(
        { method, url: fullUrl, data, params, response },
        'Plaid response',
      )
      return response.data
    })
    .catch((error) => {
      log.error(
        { method, url: fullUrl, data, params, err: error },
        'Plaid request error',
      )
      throw new Error(
        `${error.response?.data?.error?.message} ${error.response?.data?.warnings}`,
      )
    })
}

axios.interceptors.response.use(
  async (response) => {
    return response
  },
  async (error) => {
    const { response, config } = error
    // Retry Later Error
    if (response?.status === 504 || response?.status === 502) {
      config.params.retry = config.params.retry ? config.params.retry + 1 : 1
      await new Promise((resolve) => setTimeout(resolve, 1000))
      if (config.params.retry < 5) {
        return axios.request(config)
      }
    }
    return Promise.reject(response?.data || error)
  },
)

const requester = {
  get: <T = any>(
    url: string,
    params?: any,
    userId?: string,
    companyId?: string,
  ) => {
    return request<T>('get', url, null, params, userId, companyId)
  },
  post: <T = any>(
    url: string,
    data?: any,
    userId?: string,
    companyId?: string,
    token?: string,
    plaid?: { accountId: string; itemId: string },
  ) => {
    return request<T>('post', url, data, null, userId, companyId, token, plaid)
  },
  put: <T = any>(
    url: string,
    data?: any,
    userId?: string,
    companyId?: string,
  ) => {
    return request<T>('put', url, data, null, userId, companyId)
  },
  patch: <T = any>(
    url: string,
    data?: any,
    userId?: string,
    companyId?: string,
  ) => {
    return request<T>('patch', url, data, null, userId, companyId)
  },
  delete: <T = any>(
    url: string,
    params?: any,
    userId?: string,
    companyId?: string,
  ) => {
    return request<T>('delete', url, null, params, userId, companyId)
  },
}

import React, { useEffect, useMemo, useState } from 'react'
import { useNavigation } from '@react-navigation/native'
import Wrapper from '../../Wrapper'
import Loading from '../../../Loading'
import { observer } from 'mobx-react'
//import { InvoiceListItem } from '../../../../ui/atoms/InvoiceListItem'
//import { getDisplayAmount } from '../../../../utils/helpers/getDisplayAmount'
import { BtText } from '@linqpal/components/src/ui'
import Toolbar from './Toolbar'
import CompanyHeader from './CompanyHeader'
import { useTranslation } from 'react-i18next'
import { styles, toolBarStyles } from './styles'
import { EInvoiceType, routes } from '@linqpal/models'
import { SingleVendor } from '../../PayablesTab/Store/VendorsStore'
import CompanyStatistics from './CompanyStatistics'
import { View, Text } from 'react-native'
import { FilterIcon } from '../../../../assets/icons'
import { EmptyInvoicePageMessage, Filter } from '../InvoiceListing/components'
import { InvoicesListDisplay } from '../InvoicesListDisplay'
import AutoPayIHCWarning from '../../../../ui/organisms/AutoPayIHCWarning/AutoPayIHCWarning'
import { AutoPayIHCFlow } from '../../MoreTab/Wallet/AutoPayIHCFlow'
import {
  useAutoPayIHCReducer,
  AutoPayIHCActionType,
} from '../../MoreTab/Wallet/AutoPayIHCFlow/useAutoPayIHCReducer'

export default observer(function CompanyInactiveInvoices({
  companyInvoices,
  route,
}) {
  const navigation: any = useNavigation()
  const [invoices, setInvoices] = useState<any[]>([])
  const [filterStatus, setFilterStatus] = useState('All')
  const [modal, setModal] = useState(false)
  const [company, setCompany] = useState<any | null>(null)
  const [isFactoring, setIsFactoring] = useState<boolean | null>(null)
  const [isInvited, setIsInvited] = useState<boolean | null>(null)
  const [autoPayState, dispatchAutoPay] = useAutoPayIHCReducer()
  const { t } = useTranslation('payables')
  const singleVendorStore = SingleVendor

  useEffect(() => {
    setInvoices(companyInvoices)
    setCompany(companyInvoices[0]?.company)
  }, [companyInvoices])

  useEffect(() => {
    const fetchData = async () => {
      if (!company?._id) return

      singleVendorStore.setVendor(
        company._id,
        company.name,
        company.phone,
        company.isInvited || false,
      )
      try {
        if (company?.isInvited) {
          setIsFactoring(false)
          singleVendorStore.setIsFactoring(false, false)
          setIsInvited(true)
        } else {
          const response = await routes.company.isFactoringEnabled({
            supplierId: company._id,
          })
          singleVendorStore.setIsFactoring(
            response.inHouseCreditIsEnabled,
            response.arAdvanceIsEnabled,
          )
          setIsFactoring(
            response.arAdvanceIsEnabled && response.inHouseCreditIsEnabled,
          )
          setIsInvited(false)
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      }
    }

    fetchData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [company])

  useEffect(() => {
    if (isFactoring) {
      singleVendorStore.fetchStatisticsData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFactoring])

  const list = useMemo(() => {
    if (company && typeof isFactoring === 'boolean' && invoices?.length) {
      let invoiceList
      if (filterStatus === 'All') {
        if (isFactoring) {
          invoiceList = invoices.filter(
            (invoice: any) => invoice.type === EInvoiceType.INVOICE,
          )
        } else invoiceList = invoices
      } else {
        invoiceList = invoices.filter((invoice: any) => {
          const statusMatches =
            invoice.status === filterStatus.split(' ').join('').toUpperCase()

          if (isFactoring) {
            return statusMatches && invoice.type === EInvoiceType.INVOICE
          }
          return statusMatches
        })
      }

      if (invoiceList.length === 0) {
        return <EmptyInvoicePageMessage />
      }

      return (
        <InvoicesListDisplay
          invoices={invoiceList}
          navigation={navigation}
          route={route}
          isFactoring={isFactoring || false}
          disabled={!(isFactoring || isInvited)}
          isFromInvitedSupplier={!!isInvited}
        />
      )
    } else return null
  }, [
    filterStatus,
    invoices,
    navigation,
    route,
    isFactoring,
    company,
    isInvited,
  ])

  if (
    !company ||
    typeof isFactoring !== 'boolean' ||
    singleVendorStore.isStatisticsLoading
  )
    return <Loading />

  return (
    <>
      <Wrapper toolbar={<Toolbar company={company} navigation={navigation} />}>
        <CompanyHeader
          company={company}
          totalAmount={
            isFactoring ? singleVendorStore.vendorsStatistics.totalDueSum : 0.0
          }
          style={styles.invoicesListHeader}
          amountLabel={
            isFactoring
              ? t('payables.mobile.totalBalanceDue')
              : t('payables.mobile.amountDue')
          }
          amountLabelStyles={
            isFactoring
              ? { marginTop: 4, color: '#668598' }
              : { color: '#99ADBA', marginTop: 4 }
          }
        />
        {isFactoring &&
          (singleVendorStore.isStatisticsLoading ? (
            <Loading />
          ) : (
            <>
              <CompanyStatistics
                statistics={singleVendorStore.vendorsStatistics}
              />

              <View style={toolBarStyles.factoringFilter}>
                <Text
                  style={toolBarStyles.filterText}
                  onPress={() => setModal(true)}
                >
                  <FilterIcon /> {t('payables.mobile.filters')}
                </Text>
              </View>
            </>
          ))}
        {autoPayState.showAutoPayWarning && isFactoring && (
          <View style={{ marginBottom: 15 }}>
            <AutoPayIHCWarning
              onActionPress={() =>
                dispatchAutoPay({
                  type: AutoPayIHCActionType.SHOW_IHC_SETUP_FLOW,
                })
              }
              onClose={() =>
                dispatchAutoPay({ type: AutoPayIHCActionType.HIDE_WARNING })
              }
              key={autoPayState.refreshKey}
              isAutoPayIhcFlowOpened={autoPayState.showAutoPayFlow}
            />
          </View>
        )}
        {!isFactoring && (
          <BtText
            size={18}
            weight={'600'}
            style={{
              width: '100%',
              textAlign: 'center',
              marginVertical: 14,
              lineHeight: 24,
            }}
          >
            {t('payables.mobile.allPayables')}
          </BtText>
        )}
        {list}
        <Filter
          t={t}
          onPress={(value) => setFilterStatus(value)}
          filterStatus={filterStatus}
          onClose={() => setModal(false)}
          visible={modal}
        />
      </Wrapper>
      {autoPayState.showAutoPayFlow && (
        <AutoPayIHCFlow
          onClose={() =>
            dispatchAutoPay({ type: AutoPayIHCActionType.HIDE_IHC_SETUP_FLOW })
          }
          navigation={navigation}
          onSuccess={() =>
            dispatchAutoPay({
              type: AutoPayIHCActionType.AUTO_PAY_IHC_SETUP_SUCCESS,
            })
          }
        />
      )}
    </>
  )
})

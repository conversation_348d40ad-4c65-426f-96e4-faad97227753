import React, { useMemo, useState, useEffect } from 'react'
import { Text, View, StyleSheet } from 'react-native'
import { IconWarning } from '../../../../../assets/icons'
import Wrapper from '../../../Wrapper'
import { useResponsive } from '../../../../../utils/hooks'
import { Tabs, ViewAddedMethods } from '../BankCardListing/ViewAddedMethods'
import { AddMethodFAB } from '../BankCardListing/AddMethodFAB'
import { Header } from '../BankCardListing/Header'
import { useTranslation } from 'react-i18next'
import { PaymentMethodScreenWrapper } from './PaymentMethodScreenWrapper'
import { commonColors } from '@linqpal/common-frontend/src/theme'
import AutoPayIHCWarning from '../../../../../ui/organisms/AutoPayIHCWarning/AutoPayIHCWarning'
import { AutoPayIHCFlow } from '../AutoPayIHCFlow'
import { useNavigation, useRoute } from '@react-navigation/native'
import { AutoPayIHCEnabledWarningOrigin } from '../../../../../ui/organisms/AutoPayIHCWarning/AutoPayIHCEnabledWarning'
import { useStore } from '../../../../../store'

export const PaymentMethodListing: React.FC = () => {
  const [tab, setTab] = useState(Tabs.BANKS)
  const [showAutoPayFlow, setShowAutoPayFlow] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)
  const { md } = useResponsive()
  const { t } = useTranslation('global')
  const navigation = useNavigation()
  const route = useRoute()
  const { screensStore } = useStore()
  const { paymentMethodsStore } = screensStore

  useEffect(() => {
    const params = route.params as any
    if (params?.showAutoPayFlow === 'true') {
      setShowAutoPayFlow(true)
      navigation.setParams({ showAutoPayFlow: undefined })
    }
  }, [route.params, navigation, paymentMethodsStore])

  const footerTitle = useMemo(
    () =>
      tab === Tabs.BANKS
        ? t('Wallet.bank-delete-footer')
        : t('Wallet.card-delete-footer'),
    [tab, t],
  )
  if (md) {
    return (
      <PaymentMethodScreenWrapper>
        <AutoPayIHCWarning
          key={refreshKey}
          onActionPress={() => {
            setShowAutoPayFlow(true)
          }}
          origin={AutoPayIHCEnabledWarningOrigin.Account}
          marginBottom={20}
          isAutoPayIhcFlowOpened={showAutoPayFlow}
        />
        <View style={styles.desktopContainer}>
          <ViewAddedMethods setTab={setTab} />
          <View style={styles.desktopFooter}>
            <IconWarning />
            <Text style={styles.footerText}>{footerTitle}</Text>
            <AddMethodFAB tab={tab} />
          </View>
        </View>
        {showAutoPayFlow && (
          <AutoPayIHCFlow
            onClose={() => {
              setShowAutoPayFlow(false)
            }}
            navigation={navigation}
            onSuccess={() => {
              setShowAutoPayFlow(false)
              setRefreshKey((prev) => prev + 1)
            }}
          />
        )}
      </PaymentMethodScreenWrapper>
    )
  }
  return (
    <>
      <Wrapper
        contentContainerStyle={{ paddingHorizontal: 0 }}
        toolbar={<Header />}
      >
        <AutoPayIHCWarning
          key={refreshKey}
          onActionPress={() => {
            setShowAutoPayFlow(true)
          }}
          origin={AutoPayIHCEnabledWarningOrigin.Account}
          marginBottom={5}
          marginTop={20}
          isAutoPayIhcFlowOpened={showAutoPayFlow}
        />
        <ViewAddedMethods setTab={setTab} />
      </Wrapper>
      <View style={styles.mobileFooter}>
        <IconWarning />
        <Text style={styles.footerText}>{footerTitle}</Text>
        <AddMethodFAB tab={tab} />
      </View>
      {showAutoPayFlow && (
        <AutoPayIHCFlow
          onClose={() => {
            setShowAutoPayFlow(false)
          }}
          navigation={navigation}
          onSuccess={() => {
            setShowAutoPayFlow(false)
            setRefreshKey((prev) => prev + 1)
          }}
        />
      )}
    </>
  )
}

const styles = StyleSheet.create({
  desktopContainer: {
    flex: 1,
    justifyContent: 'space-between',
    width: 420,
  },
  desktopFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 'auto',
    backgroundColor: 'white',
  },
  mobileFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    height: 60,
    paddingHorizontal: 16,
  },
  footerText: {
    fontWeight: '500',
    fontSize: 12,
    lineHeight: 20,
    color: commonColors.textLight,
    marginLeft: 8,
  },
})

import { useCallback } from 'react'
import { FLOWS } from '../utils/flows'
import {
  IntegrationErrorCodesType,
  unexpectedErrorCode,
} from '@linqpal/models/src/dictionaries'
import { PaymentMethod } from '../../../Contractor/InvoicesTab/Invoice/InvoiceDetails'

export const usePaymentFlow = (
  setFlow: (flow: FLOWS) => void,
  setPaymentMethod: (method: PaymentMethod | null) => void,
  setError: (error: IntegrationErrorCodesType) => void,
  refetch: () => void,
) => {
  const handleAddNewBankAccount = useCallback(() => {
    setFlow(FLOWS.ADD_NEW_BANK_ACOUNT)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleAddNewCreditCard = useCallback(() => {
    setFlow(FLOWS.ADD_NEW_CREDIT_CARD)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleAddNewPaymentMethod = useCallback(() => {
    setFlow(FLOWS.ADD_NEW_PAYMENT_METHOD)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handlePayWithBankOrCard = useCallback(
    (selectedPaymentMethod: PaymentMethod) => {
      setPaymentMethod(selectedPaymentMethod)
      switch (selectedPaymentMethod.paymentMethodType) {
        case 'card':
          setFlow(FLOWS.PAY_WITH_CARD_FLOW)
          break
        case 'bank':
          setFlow(FLOWS.PAY_WITH_BANK_ACCOUNT_FLOW)
          break
        default:
          setError(unexpectedErrorCode)
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  )

  const handleResetFlow = useCallback(() => {
    setFlow(FLOWS.NONE)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleNewPaymentMethodAdded = useCallback(async () => {
    setFlow(FLOWS.NONE)
    refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetch])

  const handleFinishPaymentMethodAdded = useCallback(() => {
    setFlow(FLOWS.NONE)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handlePrimaryForCreditChanged = useCallback(() => {
    setFlow(FLOWS.NONE)
    refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetch])

  const handleLinkBankManually = useCallback(() => {
    window.open(`/link-bank`, '_blank')
  }, [])

  return {
    handleAddNewBankAccount,
    handleAddNewCreditCard,
    handleAddNewPaymentMethod,
    handlePayWithBankOrCard,
    handleResetFlow,
    handleNewPaymentMethodAdded,
    handlePrimaryForCreditChanged,
    handleLinkBankManually,
    handleFinishPaymentMethodAdded,
  }
}

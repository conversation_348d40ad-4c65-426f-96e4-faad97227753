import React, { memo, useState, useCallback } from 'react'
import { routes } from '@linqpal/models'
import { PlaidLinkOptions } from 'react-plaid-link'
import { AddNewBankAccountFlow_IDLE } from '../components/AddNewBankAccountFlow_IDLE'
import { AddNewBankAccountFlow_ERROR } from '../components/AddNewBankAccountFlow_ERROR'
import { AddNewBankAccountFlow_SUCCESS } from '../components/AddNewBankAccountFlow_SUCCESS'
import { PlaidLink } from '../components/PlaidLink'

enum STEPS {
  IDLE,
  SUCCESS,
  ERROR,
}

interface AddNewBankAccountFlowProps {
  onClose: () => void
  onSuccess: () => void
  onFinish: () => void
  onLinkBankAccountManually: () => void
}

export const AddNewBankAccountFlow: React.FC<AddNewBankAccountFlowProps> = memo(
  ({ onClose, onSuccess, onLinkBankAccountManually, onFinish }) => {
    const [step, setStep] = useState(STEPS.IDLE)
    const [loading, setLoading] = useState(false)
    const [plaidLinkOptions, setPlaidLinkOptions] =
      useState<PlaidLinkOptions | null>(null)

    const onSuccessAction = useCallback((publicToken, metadata) => {
      console.log(
        `ONSUCCESS: Metadata ${JSON.stringify(metadata)} ${publicToken}`,
      )

      setLoading(true)

      routes.plaid
        .exchangePublicToken(publicToken)
        .then(() => {
          setLoading(false)
          onSuccess && onSuccess()
          setStep(STEPS.SUCCESS)
        })
        .catch((err) => {
          setLoading(false)
          setStep(STEPS.ERROR)
          console.log(err)
        })

      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    const handleNext = useCallback(async () => {
      if (step === STEPS.IDLE) {
        setLoading(true)
        try {
          const res = await routes.plaid.createLinkToken()
          const options: PlaidLinkOptions = {
            token: res.link_token,
            onSuccess: onSuccessAction,
            onExit: (err, metadata) => {
              console.log(
                `Exited early. Error: ${JSON.stringify(
                  err,
                )} Metadata: ${JSON.stringify(metadata)}`,
              )
            },
            onEvent: (eventName, metadata) => {
              console.log(
                `Event ${eventName}, Metadata: ${JSON.stringify(metadata)}`,
              )
            },
          }
          setPlaidLinkOptions(options)
        } catch (err) {
          setStep(STEPS.ERROR)
          console.error(err)
        }
        setLoading(false)
      }
    }, [step, setLoading, onSuccessAction])

    return (
      <>
        {!!plaidLinkOptions && <PlaidLink config={plaidLinkOptions} />}
        {step === STEPS.IDLE && (
          <AddNewBankAccountFlow_IDLE
            loading={loading}
            onCreateToken={handleNext}
            onClose={onClose}
            onLinkBankManually={onLinkBankAccountManually}
          />
        )}
        {step === STEPS.ERROR && (
          <AddNewBankAccountFlow_ERROR onClose={onClose} />
        )}
        {step === STEPS.SUCCESS && (
          <AddNewBankAccountFlow_SUCCESS onClose={onFinish} />
        )}
      </>
    )
  },
)

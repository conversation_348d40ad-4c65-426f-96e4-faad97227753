import { CSwitch } from '@coreui/react'
import { routes } from '@linqpal/models'
import { observer } from 'mobx-react'
import React, { useEffect, useState } from 'react'
import { Instance } from 'mobx-state-tree'
import InHouseCreditInvoiceDetailsStore from '../InHouseCreditInvoiceDetailsStore'

interface Props {
  store: Instance<typeof InHouseCreditInvoiceDetailsStore>
  onSuccess?: (isPaused: boolean) => void
  fetchDetails?: () => void
}

export const InHouseAutoCollectionPauseSwitch = observer(
  function InHouseAutoCollectionPauseSwitch({
    store,
    onSuccess = () => {},
    fetchDetails,
  }: Props) {
    const loanId = store.item?.id
    const isAutoCollectionPaused = store.item?.isAutoCollectionPaused || false

    const [{ isPaused, isLoading }, setState] = useState<{
      isPaused: boolean
      isLoading: boolean
    }>({
      isPaused: isAutoCollectionPaused,
      isLoading: false,
    })

    useEffect(() => {
      setState((prev) => ({
        ...prev,
        isPaused: isAutoCollectionPaused,
      }))
    }, [isAutoCollectionPaused])

    const toggleAutoCollection = (checked: boolean) => {
      if (!loanId || isLoading) return

      setState((prev) => ({ ...prev, isLoading: true }))

      const req = routes.admin.toggleLoanAutoPaymentCollection(loanId, checked)
      req
        .then(() => {
          setState({
            isPaused: checked,
            isLoading: false,
          })

          if (fetchDetails) {
            fetchDetails()
          }

          onSuccess(checked)
        })
        .catch((error) => {
          console.error('Failed to toggle auto collection:', error)
          setState({
            isPaused: !checked,
            isLoading: false,
          })
        })
    }

    return (
      <CSwitch
        labelOn="Yes"
        labelOff="No"
        color="primary"
        checked={isPaused}
        onChange={(e) =>
          toggleAutoCollection((e.target as HTMLInputElement).checked)
        }
        disabled={isLoading || !loanId}
        shape="pill"
      />
    )
  },
)

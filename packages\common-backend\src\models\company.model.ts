import {
  Address as AddressModel,
  Company as CompanyModel,
} from '@linqpal/models'
import mongoose, { Schema } from 'mongoose'
import createSchema from '../helpers/createSchema'
import { wirePhoneValidator } from '../helpers/phoneValidator'
import { wireCryptValidator } from '../helpers/cryptValidator'
import { ICompany, ICompanyConnectorReference } from './types'
import { PURCHASE_TYPE } from '@linqpal/models/src/dictionaries'
import {
  ArAdvanceEarlyPaymentDiscountDays,
  DisclosureType,
} from '@linqpal/models/src/dictionaries/factoring'

const AddressSchema = new Schema(createSchema(AddressModel), {
  timestamps: false,
})

const RepaymentSchema = new Schema(
  {
    repays: {
      type: String,
      enum: ['borrower', 'supplier'],
      default: 'borrower',
    },
    nameOnAccount: String,
    routingNumber: String,
    accountNumber: String,
    autoApprove: Boolean,
    paymentPlan: String,
    maxInvoiceAmount: Number,
  },
  { timestamps: false },
)

const EmailSchema = new Schema({
  senderEmail: String,
  sendInvitationTemplate: String,
  sendInHouseCreditInvitationTemplate: String,
  sendInvoiceTemplate: String,
  logoUrl: String,
})

const ACHdiscountSchema = new Schema(
  {
    percentage: Number,
    validityInDays: Number,
  },
  { timestamps: false },
)

const InvoiceLoanPlanSchema = new Schema(
  {
    minAmount: Number,
    maxAmount: Number,
    plans: [String],
  },
  { timestamps: false, _id: false },
)

const ArAdvanceSchema = new Schema(
  {
    isEnabled: Boolean,
    lastChangedBy: String,
    lastChangedAt: Date,
    disclosureType: {
      type: String,
      enum: DisclosureType,
      default: DisclosureType.DAF,
    },
    factoringTerms: [String],
    defaultFactoringTerm: String,
    supplierPackages: [String],
    defaultSupplierPackage: String,
    earlyPaymentDiscount: new Schema(
      {
        isEnabled: Boolean,
        earlyPaymentInDays: {
          type: String,
          enum: ArAdvanceEarlyPaymentDiscountDays,
          default: ArAdvanceEarlyPaymentDiscountDays.None,
        },
        discountPct: Number,
      },
      { timestamps: false, _id: false },
    ),
    merchantLimit: Number,
    defaultCustomerLimit: Number,
    gracePeriodInDays: Number,
    lateFeeAmount: Number,
    isLateInterestChargedToMerchant: Boolean,
    defaultDebtInvestor: String,
  },
  { timestamps: false, _id: false },
)

const CompanyConnectorReference = new Schema<ICompanyConnectorReference>(
  {
    integrationId: { type: String, required: true },
    customerId: { type: String, required: true },
  },
  { timestamps: false, _id: false },
)

export const AutomatedDrawApprovalSchema = new Schema(
  {
    isEnabled: Boolean,
    drawLimit: Number,
    creditLimitPercentage: Number,
    dailyAmountLimit: Number,
    weeklyAmountLimit: Number,
    lastUpdatedBy: String,
    lastUpdatedAt: Date,
  },
  { timestamps: false, _id: false },
)

export const InHouseCreditSchema = new Schema(
  {
    isAutoPayRequired: { type: Boolean, required: false },
    isAutoPayEnabledByCompanyUser: { type: Boolean, required: false },
  },
  { timestamps: false, _id: false },
)

export const DepositDetailsSchema = new Schema(
  {
    isSecured: Boolean,
    depositAmount: Number,
    isDepositPaid: Boolean,
  },
  { timestamps: false, _id: false },
)

export const DirectTermsSchema = new Schema(
  {
    loanPlans: [String],
  },
  { timestamps: false, _id: false },
)

export const DownPaymentDetailsSchema = new Schema(
  {
    isRequired: Boolean,
    downPaymentPercentage: Number,
    expireDays: Number,
  },
  { timestamps: false, _id: false },
)

const CustomerSettingsSchema = new Schema(
  {
    source: String,
    sourceCampaign: String,
    startLOCAfterSignUp: Boolean,
  },
  { timestamps: false, _id: false },
)

const CompanySchema = createSchema(CompanyModel)

CompanySchema.address = { type: AddressSchema }
CompanySchema.bankAccounts = [
  { type: Schema.Types.ObjectId, ref: 'BankAccount' },
]

CompanySchema.tabapay = {
  bankAccountId: { type: String },
}
CompanySchema.settings = {
  approveRead: { type: Boolean },
  acceptAchPayment: { type: Boolean },
  achDelayDisabled: { type: Boolean },
  accountAdded: { type: Boolean },
  canUploadInvoice: { type: Boolean },
  canPostTransactions: { type: Boolean },
  canEditAuthorization: { type: Boolean },
  sendFinalPaymentWhenLoanIsPaid: { type: Boolean },
  tutorialViewed: { type: Boolean },
  welcomeViewed: { type: Boolean },
  cardPricingPackageId: { type: String },
  loanPricingPackageId: { type: String },
  lastPurchaseDate: { type: Date },
  onboardingType: { type: [String] },
  businessType: { type: String },
  OCRModelId: { type: String },
  dueDay: { type: Number },
  loanPlans: { type: [String] },
  achDiscount: { type: ACHdiscountSchema },
  invitedBy: { type: String },
  supplierCanPay: { type: Boolean },
  defaultDebtInvestorTradeCredit: { type: String },
  repayment: { type: RepaymentSchema },
  arAdvance: { type: ArAdvanceSchema },
  automatedDrawApproval: { type: AutomatedDrawApprovalSchema },
  inHouseCredit: { type: InHouseCreditSchema },
  depositDetails: { type: DepositDetailsSchema },
  directTerms: { type: DirectTermsSchema },
  downPaymentDetails: { type: DownPaymentDetailsSchema },
  customerSettings: { type: CustomerSettingsSchema },
  email: { type: EmailSchema },
  test: { type: Boolean },
  invoiceLoanPlans: {
    type: [InvoiceLoanPlanSchema],
  },
}
CompanySchema.finicity = {
  customerId: { type: String },
  lastImport: { type: Number },
  transactionImportCompleted: { type: Boolean, default: false },
  transactionsImportedCount: { type: Number, default: -1 },
}
CompanySchema.plaid = {
  assetsReportStatus: {
    type: new Schema(
      {
        reportRequestId: String,
        daysAvailableFrom: String,
        daysAvailableTo: String,
      },
      { timestamps: false },
    ),
  },
}

CompanySchema.hubspotId = { type: String }
CompanySchema.hubspotLastSyncDate = { type: Date }
CompanySchema.ein.type = Schema.Types.Mixed
CompanySchema.cbw = { type: Schema.Types.Mixed }
CompanySchema.onBoarding = { type: Schema.Types.Mixed }
CompanySchema.connector = { type: Schema.Types.Mixed }
CompanySchema.isBusiness = { type: Boolean }

CompanySchema.credit = {
  limit: { type: Number },
  balance: { type: Number },
  purchaseType: { type: String, enum: Object.values(PURCHASE_TYPE) },
  LoCnumber: { type: Schema.Types.ObjectId },
  LoCnumCreatedAt: { type: Date },
  pastDueAmount: { type: Number },
  processingAmount: { type: Number },
}

CompanySchema.connectorReferences = [
  {
    type: CompanyConnectorReference,
  },
]
CompanySchema.newDECreditApplicationExecutionArn = { type: String }

const companySchema = new Schema<ICompany>(CompanySchema)
wirePhoneValidator(companySchema, 'phone')
wireCryptValidator(companySchema, 'ein')

export const Company = mongoose.model<ICompany>('Company', companySchema)

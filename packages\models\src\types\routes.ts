import {
  LOAN_REPAYMENT_SCHEDULE_STATUS,
  LOAN_REPAYMENT_TYPE,
} from '../dictionaries/loanStatuses'
import {
  ArAdvanceEarlyPaymentDiscountDays,
  DisclosureType,
} from '../dictionaries/factoring'
import { DownPaymentMethod } from '../dictionaries/downPayment'

export interface IInHouseCreditSettings {
  isEnabled: boolean
  isRecourse: boolean
  limit: number
  factoringTerm: string | null
  supplierPackage: string | null
  recoursePercentage: number | null
  debtInvestor: string | null
}

export interface IInHouseCreditCompanySettings {
  isAutoPayRequired: boolean
  isAutoPayEnabledByCompanyUser: boolean
}

export interface IArAdvanceLimit {
  arAdvanceBalance: number
  arAdvanceAvailableCredit: number
}

export interface IArAdvanceSettings {
  isEnabled: boolean
  disclosureType: DisclosureType
  factoringTerms: string[]
  defaultFactoringTerm: string | null
  supplierPackages: string[]
  defaultSupplierPackage: string | null
  earlyPaymentDiscount: {
    isEnabled: boolean
    earlyPaymentInDays: ArAdvanceEarlyPaymentDiscountDays | null
    discountPct: number
  }
  merchantLimit: number
  defaultCustomerLimit: number
  gracePeriodInDays: number | null
  lateFeeAmount: number | null
  isLateInterestChargedToMerchant: boolean
  lastChangedBy: string | null
  lastChangedAt: Date | null
  defaultDebtInvestor: string | null
}

export interface IAutomatedDrawApprovalSettings {
  isEnabled: boolean
  drawLimit: number
  creditLimitPercentage: number
  dailyAmountLimit: number
  weeklyAmountLimit: number
  lastUpdatedBy: string | null
  lastUpdatedAt: Date | null
}

export interface ICustomerSettings {
  loanPlans: string[]
  loanPaymentCollection: 'borrower' | 'supplier'
  acceptCheckPayment: boolean
  acceptAchPayment: boolean
  autoDebit: boolean
  autoTradeCreditEnabled: boolean
  autoTradeCreditPaymentPlan: string
  autoTradeCreditMaxInvoiceAmount: number
  tradeCreditPaymentFrequency?: string | null
  sendFinalPaymentWhenLoanIsPaid: boolean
  acceptCreditPayment?: boolean
  inHouseCredit?: IInHouseCreditSettings
  automatedDrawApproval?: IAutomatedDrawApprovalSettings
  debtInvestorTradeCredit: string | null
}

export interface ISupplierAutoTradeCreditSettings {
  autoApprove: boolean
  paymentPlan: string
  maxInvoiceAmount: number
}

export interface IDepositDetailsSettings {
  isSecured: boolean
  depositAmount: number
  isDepositPaid: boolean
}

export interface IAchDiscount {
  percentage: number
  validityInDays: number
}

export interface IDirectTermsSettings {
  loanPlans: string[]
}

export interface IDownPaymentSettings {
  isRequired: boolean
  downPaymentPercentage: number
  expireDays: number
}

export interface ISupplier {
  _id: string
  settings: ICompanySettings
}

export interface ICompanySettings {
  acceptAchPayment: boolean
  achDelayDisabled: boolean
  canPostTransactions: boolean
  canEditAuthorization: boolean
  canUploadInvoice?: boolean
  loanPlans: string[]
  achDiscount: IAchDiscount
  sendFinalPaymentWhenLoanIsPaid: boolean
  invoiceLoanPlans?: {
    minAmount: number
    maxAmount?: number
    plans: string[]
  }[]
  repayment?: ISupplierAutoTradeCreditSettings
  arAdvance?: IArAdvanceSettings
  supplierCanPay?: boolean
  automatedDrawApproval?: IAutomatedDrawApprovalSettings
  depositDetails?: IDepositDetailsSettings
  directTerms?: IDirectTermsSettings
  downPaymentDetails?: IDownPaymentSettings
  defaultDebtInvestorTradeCredit?: string
  inHouseCredit?: IInHouseCreditCompanySettings
}

export interface ICustomerCompanySettings
  extends Pick<
    ICompanySettings,
    | 'canUploadInvoice'
    | 'directTerms'
    | 'depositDetails'
    | 'downPaymentDetails'
    | 'inHouseCredit'
  > {}

export type RepaymentType =
  typeof LOAN_REPAYMENT_TYPE[keyof typeof LOAN_REPAYMENT_TYPE]

type RepaymentScheduleStatus =
  typeof LOAN_REPAYMENT_SCHEDULE_STATUS[keyof typeof LOAN_REPAYMENT_SCHEDULE_STATUS]

export interface IReceivablesRescheduleItem {
  id?: string
  receivableType?: RepaymentType
  newExpectedAmount: number
  newExpectedDate: string
  newScheduleStatus: RepaymentScheduleStatus
}

export interface ICreateCredit {
  companyId: string
  creditApplicationId?: string
  creditLimit?: number
}

export interface IDownPaymentDetails {
  percentage: number
  amount: number
  paymentMethod?: DownPaymentMethod
  accountId?: string
}

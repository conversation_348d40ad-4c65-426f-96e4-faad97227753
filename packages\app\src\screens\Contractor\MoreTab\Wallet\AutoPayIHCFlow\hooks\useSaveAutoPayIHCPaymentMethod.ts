import { useState, useCallback } from 'react'
import { routes } from '@linqpal/models'

export const useSaveAutoPayIHCPaymentMethod = () => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [success, setSuccess] = useState(false)

  const saveAutoPayIHCPaymentMethod = useCallback(
    async (paymentMethodId: string) => {
      if (!paymentMethodId) {
        setError(new Error('No payment method ID provided'))
        return false
      }

      setLoading(true)
      setError(null)
      setSuccess(false)

      try {
        await routes.contractor.ihcAutoPayEnable(paymentMethodId)

        setSuccess(true)
        setLoading(false)
        return true
      } catch (err) {
        setError(err as Error)
        setLoading(false)
        return false
      }
    },
    [],
  )

  return {
    saveAutoPayIHCPaymentMethod,
    loading,
    error,
    success,
  }
}

import { NextFunction, Request, Response } from 'express'
import {
  UserRole,
  User,
  cardPaymentService,
  payWithAch,
  invoicesService,
  handleInvoiceAfterPaymentActions,
  checkFactoringInvoices,
} from '@linqpal/common-backend'
import { exceptions } from '@linqpal/models'
import { Logger } from '@linqpal/common-backend/src/services/logger/logger.service'
import transactionalService from '../../../services/transactional.service'
import { ControllerItem } from 'src/routes/controllerItem'
import { apiKeyRequired } from '../../../services/auth.service'
import { Invoice } from '@linqpal/common-backend'
import { getOperation } from '@linqpal/common-backend/src/services/cardPayment.service'
import { IPaymentInfo } from '@linqpal/common-backend/src/services/payment/types'
import {
  insertNotification,
  makeInvoiceNotificationRead,
} from '../../../controllers/notification.controller'
import mongoose, { Types } from 'mongoose'
import { linkInvoiceToCustomer } from '@linqpal/common-backend/src/services/invoices.service/linkInvoiceToCustomer'

const logger = new Logger({ module: 'BffInvoicePayment' })

export default {
  middlewares: {
    pre: [apiKeyRequired(), ...transactionalService.pre],
    post: [...transactionalService.post],
  },
  post: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const {
        invoiceIds,
        paymentMethod,
        accountId,
        companyId,
        autoDebit = false,
        requestedAmount,
      } = req.body

      const logContext = {
        invoiceIds,
        paymentMethod,
        accountId,
        companyId,
        autoDebit,
        requestedAmount,
      }

      if (!invoiceIds || !invoiceIds.length) {
        logger.error(logContext, 'Missing invoiceIds')
        return res.status(400).json({
          success: false,
          error: 'Missing invoiceIds',
        })
      }

      if (!companyId) {
        logger.error(logContext, 'Missing companyId')

        return res.status(400).json({
          success: false,
          error: 'Missing companyId',
        })
      }

      logger.info(logContext, 'Processing BFF invoice payment request')

      const userId = await findOwnerUserIdByCompanyId(companyId, req.session)
      if (!userId) {
        logger.error({ companyId }, 'No owner found for company')
        return res.status(404).json({
          success: false,
          error: 'No owner found for company',
        })
      }

      const objectIds = invoiceIds.map((id: string) => new Types.ObjectId(id))
      const invoices = await Invoice.find({
        _id: mongoose.trusted({
          $in: objectIds,
        }),
      }).session(req.session)

      if (!invoices || invoices.length === 0) {
        logger.error({ invoiceIds }, 'No invoices found')
        return res.status(404).json({
          success: false,
          error: 'No invoices found',
        })
      }

      const builder = await invoicesService.findBuilderByAccountId(
        invoices[0].customer_account_id,
        req.session,
      )

      if (builder && companyId !== builder._id.toString()) {
        logger.error(
          { companyId, builderId: builder._id },
          'Not enough permissions to pay',
        )
        return res.status(403).json({
          success: false,
          error: 'Not enough permissions to pay',
        })
      }
      // Check for factoring invoices
      const factoringValidation = await checkFactoringInvoices(
        invoices,
        paymentMethod,
        companyId,
      )

      if (!factoringValidation.isValid) {
        logger.error(
          { factoringInvoiceId: factoringValidation.factoringInvoice?.id },
          factoringValidation.errorMessage,
        )
        return res.status(400).json({
          success: false,
          error: factoringValidation.errorMessage,
        })
      }

      const checkInvoiceOperationPromises: Promise<any>[] = []
      const linkInvoicePromises: Promise<any>[] = []
      invoices.forEach((inv) => {
        checkInvoiceOperationPromises.push(
          getOperation(inv, inv.company_id, req.session),
        )
        linkInvoicePromises.push(
          linkInvoiceToCustomer(inv, userId, true, req.session),
        )
      })
      await Promise.all(checkInvoiceOperationPromises)
      await Promise.all(linkInvoicePromises)

      const invoiceCompanyId = invoices[0].company_id
      let paymentInfo: IPaymentInfo

      switch (paymentMethod) {
        case 'ach':
          paymentInfo = await payWithAch(
            invoices,
            accountId,
            companyId,
            invoiceCompanyId,
            req.session,
            userId,
            requestedAmount,
          )
          break
        case 'card':
          paymentInfo = await cardPaymentService.makeCardPayment({
            invoices,
            accountId,
            payerCompanyId: companyId,
            session: req.session,
            userId,
            userRequestedAmount: requestedAmount,
            isAutoDebit: autoDebit,
          })
          break
        default:
          throw new exceptions.LogicalError('Undefined payment method')
      }

      logger.info(logContext, 'Successfully processed BFF invoice payment')

      // Handle post-payment operations, e.g. notifications, integrations actions, etc
      await handleInvoiceAfterPaymentActions({
        invoiceIds,
        companyId,
        userId,
        invoiceCompanyId,
        paymentMethod,
        paymentInfo,
        insertNotification,
        makeInvoiceNotificationRead,
      })

      res.locals.result = {
        success: true,
        paymentInfo: true,
      }

      return next()
    } catch (error: unknown) {
      logger.error(
        { error, body: req.body },
        'Error in BFF invoice payment endpoint',
      )

      if (error instanceof exceptions.LogicalError) {
        return res.status(400).json({
          success: false,
          error: error.message,
        })
      }

      if (error instanceof Error) {
        if (
          error.message.includes('declined') ||
          error.message.includes('insufficient funds')
        ) {
          return res.status(402).json({
            success: false,
            error: error.message,
          })
        }
      }

      return res.status(500).json({
        success: false,
        error: 'An internal server error occurred',
      })
    }
  },
} as ControllerItem

async function findOwnerUserIdByCompanyId(
  companyId: string,
  session: any,
): Promise<string | null> {
  const userRole = await UserRole.findOne({
    company_id: companyId,
    role: 'Owner',
    status: 'Active',
  }).session(session)

  if (!userRole) {
    return null
  }

  const user = await User.findOne({ sub: userRole.sub }).session(session)
  return user?._id?.toString() || null
}

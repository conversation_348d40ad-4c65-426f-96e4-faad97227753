import React, { useState, useCallback, useRef, useEffect } from 'react'
import { observer } from 'mobx-react'
import { AddNewPaymentMethodModal } from '../../../../IntegrationPayment/InvoiceDetails/components/AddNewPaymentMethodModal'
import { AddNewBankAccountFlow } from '../../../../IntegrationPayment/InvoiceDetails/flows/AddNewBankAccountFlow'
import { AddNewCreditCardFlow } from '../../../../IntegrationPayment/InvoiceDetails/flows/AddNewCreditCardFlow'
import { PayWithBankAccountFlow } from '../../../../IntegrationPayment/InvoiceDetails/flows/PayWithBankAccountFlow'
import { PayWithCardFlow } from '../../../../IntegrationPayment/InvoiceDetails/flows/PayWithCreditCardFlow'

import { FLOWS, IPaymentMethod, usePaymentFlow } from '../hooks/usePaymentFlow'
import { ChoosePaymentMethodBottomModal } from './ChoosePaymentModal'
import { useGetPaymentMethods } from '../hooks/useGetPaymentMethods'
import BuilderInvoiceSignup from '../../BuilderInvoiceSignup'
import { useStore } from '../../../../../store'
import { isWeb } from '../../../../../utils/helpers/commonUtils'
import cookies from '../../../../../utils/service/cooker'
import { PayFactoringInvoiceFlow } from '../../../../IntegrationPayment/InvoiceDetails/flows/PayFactoringInvoiceFlow'

interface IPaymentFlowProps {
  invoice: any
  onClose: () => void
  navigation: any
  cardPaymentAvailable: boolean
  acceptAchPayment: boolean
  onSuccess: () => void
  route?: any
}

interface IPaymentFlowHandlers {
  handleAddNewBankAccount: () => void
  handleAddNewCreditCard: () => void
  handleAddNewPaymentMethod: () => void
  handlePayWithBankOrCard: (selectedPaymentMethod: IPaymentMethod) => void
  handleResetFlow: () => void
  handleNewPaymentMethodAdded: () => Promise<void>
  handleFinishPaymentMethodAdded: () => void
  handleLinkBankManually: () => void
  handleChoosePaymentMethod: () => void
  handleCloseFlow: () => void
  handlePaymentSuccess: () => void
}

const PaymentFlowContent = observer(
  ({
    flow,
    invoice,
    paymentMethod,
    cardPaymentAvailable,
    acceptAchPayment,
    paymentMethods,
    arePaymentMethodsLoading,
    handlers,
  }: {
    flow: FLOWS
    invoice: any
    paymentMethod: IPaymentMethod | null
    cardPaymentAvailable: boolean
    acceptAchPayment: boolean
    paymentMethods: any[]
    arePaymentMethodsLoading: boolean
    handlers: IPaymentFlowHandlers
  }) => {
    switch (flow) {
      case FLOWS.CHOOSE_PAYMENT_METHOD:
        return (
          <ChoosePaymentMethodBottomModal
            cardPaymentAvailable={cardPaymentAvailable}
            acceptAchPayment={acceptAchPayment}
            onAddNewPaymentMethod={handlers.handleAddNewPaymentMethod}
            onPayWithBankOrCard={handlers.handlePayWithBankOrCard}
            paymentMethods={paymentMethods}
            arePaymentMethodsLoading={arePaymentMethodsLoading}
            onClose={handlers.handleCloseFlow}
          />
        )

      case FLOWS.ADD_NEW_PAYMENT_METHOD:
        return (
          <AddNewPaymentMethodModal
            cardPaymentAvailable={true}
            acceptAchPayment={true}
            onLinkCard={handlers.handleAddNewCreditCard}
            onConnectBank={handlers.handleAddNewBankAccount}
            onClose={handlers.handleResetFlow}
          />
        )

      case FLOWS.ADD_NEW_BANK_ACCOUNT:
        return (
          <AddNewBankAccountFlow
            onClose={handlers.handleResetFlow}
            onSuccess={handlers.handleNewPaymentMethodAdded}
            onLinkBankAccountManually={handlers.handleLinkBankManually}
            onFinish={handlers.handleFinishPaymentMethodAdded}
          />
        )

      case FLOWS.ADD_NEW_CREDIT_CARD:
        return (
          <AddNewCreditCardFlow
            onClose={handlers.handleResetFlow}
            onSuccess={handlers.handleNewPaymentMethodAdded}
            invoiceId={invoice._id}
          />
        )

      case FLOWS.PAY_WITH_BANK_ACCOUNT_FLOW:
        return paymentMethod ? (
          <PayWithBankAccountFlow
            invoiceId={invoice._id}
            invoiceCompanyName={invoice.company.name}
            paymentMethod={paymentMethod}
            onClose={handlers.handleCloseFlow}
            onSuccess={handlers.handlePaymentSuccess}
            onFail={handlers.handleCloseFlow}
            handleChangePaymentMethod={handlers.handleChoosePaymentMethod}
          />
        ) : null

      case FLOWS.PAY_WITH_CARD_FLOW:
        return paymentMethod ? (
          <PayWithCardFlow
            invoiceId={invoice._id}
            invoiceCompanyName={invoice.company.name}
            paymentMethod={paymentMethod}
            onClose={handlers.handleCloseFlow}
            onSuccess={handlers.handlePaymentSuccess}
            onFail={handlers.handleCloseFlow}
            handleChangePaymentMethod={handlers.handleChoosePaymentMethod}
          />
        ) : null

      case FLOWS.PAY_IHC_INVOICE:
        return paymentMethod ? (
          <PayFactoringInvoiceFlow
            invoiceId={invoice._id}
            invoice={invoice}
            invoiceCompanyName={invoice.company.name}
            paymentMethod={paymentMethod}
            onClose={handlers.handleCloseFlow}
            onSuccess={handlers.handlePaymentSuccess}
            onFail={handlers.handleCloseFlow}
            handleChangePaymentMethod={handlers.handleChoosePaymentMethod}
            handleAddNewPaymentMethod={handlers.handleAddNewPaymentMethod}
          />
        ) : null

      default:
        return null
    }
  },
)

export const PaymentFlow = observer(
  ({
    invoice,
    onClose,
    navigation,
    cardPaymentAvailable,
    acceptAchPayment,
    onSuccess,
    route,
  }: IPaymentFlowProps) => {
    const store = useStore()
    const session = cookies.get('session')
    const isUserAuthenticated = store.userStore.isAuthenticated
    const authenticated = (isWeb && session) || isUserAuthenticated
    const [flow, setFlow] = useState<FLOWS>(FLOWS.CHOOSE_PAYMENT_METHOD)
    const [paymentMethod, setPaymentMethod] = useState<IPaymentMethod | null>(
      null,
    )
    const previousAuthRef = useRef(authenticated)

    const {
      paymentMethods,
      refetch,
      loading: arePaymentMethodsLoading,
    } = useGetPaymentMethods()

    useEffect(() => {
      if (authenticated && !previousAuthRef.current) {
        refetch()
      }
      previousAuthRef.current = authenticated
    }, [authenticated, refetch])

    const handleCloseFlow = useCallback(() => {
      setPaymentMethod(null)
      onClose()
    }, [onClose])

    const handlePaymentSuccess = useCallback(() => {
      onSuccess()
    }, [onSuccess])

    const handlers = usePaymentFlow(
      setFlow,
      setPaymentMethod,
      refetch,
      navigation,
      invoice,
      onClose,
    )

    const allHandlers = {
      ...handlers,
      handleCloseFlow,
      handlePaymentSuccess,
    }

    const isModalVisible = flow !== null

    return (
      <BuilderInvoiceSignup
        invoice={invoice}
        visible={isModalVisible}
        onClose={handleCloseFlow}
        navigation={navigation}
        route={route}
      >
        <PaymentFlowContent
          flow={flow}
          invoice={invoice}
          paymentMethod={paymentMethod}
          cardPaymentAvailable={cardPaymentAvailable}
          acceptAchPayment={acceptAchPayment}
          paymentMethods={paymentMethods}
          arePaymentMethodsLoading={arePaymentMethodsLoading}
          handlers={allHandlers}
        />
      </BuilderInvoiceSignup>
    )
  },
)

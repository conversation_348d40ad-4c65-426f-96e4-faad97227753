import { BankAccountPaymentMethodTypes } from '@linqpal/models/src/dictionaries'
import {
  IBankAccount,
  ICompany,
  ICustomerAccount,
  IUser,
} from '../models/types'
import mongoose, { ClientSession } from 'mongoose'
import { BankAccount, Company, CustomerAccount } from '../models'
import { exceptions, IEncrypted } from '@linqpal/models'
import { institutionsService } from './institutions.service'
import { AwsService, crypt } from '../..'
import md5 from 'crypto-js/md5'
import { maskBankAccountNumber } from '../helpers/maskBankAccountNumber'
import { formatPersonName } from '@linqpal/models/src/helpers/personNameFormatter'

export interface BankAccountData {
  isPrimary: boolean
  isPrimaryForCredit: boolean
  name: string
  accountNumber: string
  routingNumber: string
  paymentMethodType: BankAccountPaymentMethodTypes
}

async function throwExceptionIfBlocked(
  institutionName: string,
  routingNumber: string,
): Promise<void> {
  const isBlocked = await institutionsService.checkIfIsBlocked(
    institutionName,
    routingNumber,
  )
  if (isBlocked) {
    throw new exceptions.DeniedError()
  }
}

async function sendCbwEarlyWarningMessage(
  company: ICompany | null,
): Promise<void> {
  if (company && process.env.NODE_ENV !== 'development') {
    await AwsService.sendSQSMessage(
      'cbwew',
      JSON.stringify({ id: company.id }),
      'CBW_EARLY_WARNING',
    )
  }
}

async function encryptAccountNumber(
  accountNumber: string,
): Promise<IEncrypted> {
  if (!/^\d+$/.test(accountNumber)) {
    throw new exceptions.LogicalError('"Account number" does not match format')
  }
  const cipher = await crypt.encrypt(accountNumber)
  return {
    cipher,
    hash: md5(accountNumber).toString(),
    display: maskBankAccountNumber(accountNumber),
  }
}

async function updateOtherAccountsPrimaryField(
  company: ICompany | null,
  customerAccount: ICustomerAccount | null,
  id: string | undefined,
  session: ClientSession,
  fieldName: string,
): Promise<void> {
  const bankAccountIdsToUpdate = (
    company?.bankAccounts ||
    customerAccount?.bankAccounts ||
    []
  )
    .filter((bankaccount) => bankaccount._id.toString() !== id)
    .map((ba) => ba._id)

  if (bankAccountIdsToUpdate.length === 0) {
    return
  }

  await BankAccount.updateMany(
    {
      _id: mongoose.trusted({
        $in: bankAccountIdsToUpdate,
      }),
    },
    {
      $set: {
        [fieldName]: false,
      },
    },
    { multi: true, session: session },
  )
}

export async function addOrUpdateBankAccount(
  company: ICompany | null,
  customerAccount: ICustomerAccount | null,
  _id: string | undefined,
  data: BankAccountData,
  user: IUser,
  session: ClientSession,
): Promise<string | undefined> {
  throwExceptionIfBlocked(data.name, data.routingNumber)
  // ensure only one account is set to primary
  if (data.isPrimary) {
    await updateOtherAccountsPrimaryField(
      company,
      customerAccount,
      _id,
      session,
      'isPrimary',
    )
  }

  if (data.isPrimaryForCredit) {
    await updateOtherAccountsPrimaryField(
      company,
      customerAccount,
      _id,
      session,
      'isPrimaryForCredit',
    )
  }
  let resultId: string | undefined
  if (_id) {
    const existingBank = await BankAccount.findById(_id).session(session)
    resultId = existingBank?.id

    let accountNumber: string | IEncrypted | undefined
    if (data.accountNumber.indexOf('*') === -1) {
      accountNumber = await encryptAccountNumber(data.accountNumber)
    } else {
      accountNumber = existingBank?.accountNumber
    }

    // TODO: VK: Not sure how it's intended to work
    // previous version didn't update isPrimaryForCredit when IsPrimary was true,
    // but this entire logic should be reviewed (if IsPrimary and isPrimaryForCredit always exists will else be ever executed?)
    if (data.isPrimary || data.isPrimaryForCredit) {
      await BankAccount.findOneAndUpdate(
        { _id },
        {
          isPrimary: data.isPrimary,
          isPrimaryForCredit: data.isPrimaryForCredit,
        },
      ).session(session)
    } else {
      await BankAccount.findOneAndUpdate(
        { _id },
        { ...data, accountNumber, ew: null },
      ).session(session)
    }
  } else {
    const accountNumber = await encryptAccountNumber(data.accountNumber)

    const [newBankAccount] = await BankAccount.create(
      [
        {
          ...data,
          paymentMethodType: 'bank',
          accountNumber,
          status: 'notverified',
          createdBy: user
            ? formatPersonName(user.firstName, user.lastName)
            : null,
        },
      ],
      {
        session: session,
      },
    )

    resultId = newBankAccount.id
    if (company && company.bankAccounts) {
      company.bankAccounts.push(newBankAccount)
      await company.save()
    }

    if (customerAccount && customerAccount.bankAccounts) {
      customerAccount.bankAccounts.push(newBankAccount)
      await customerAccount.save()
    }
  }

  await sendCbwEarlyWarningMessage(company)
  return resultId
}

export async function getBankAccount(
  id: string,
  companyId: string,
  customerAccountId?: string,
) {
  let bankAccounts: IBankAccount[] | undefined | null
  if (!customerAccountId) {
    const company = await Company.findOne({ _id: companyId }).populate(
      'bankAccounts',
    )

    bankAccounts = company?.bankAccounts
  } else {
    const customerAccount = await CustomerAccount.findOne({
      _id: customerAccountId,
      company_id: companyId,
    }).populate('bankAccounts')

    bankAccounts = customerAccount?.bankAccounts
  }

  const bankAccountObj: Partial<IBankAccount> =
    bankAccounts?.find((bankAccountItem) => {
      return (
        bankAccountItem.id === id &&
        (bankAccountItem.paymentMethodType === 'card' ||
          (bankAccountItem.paymentMethodType === 'bank' &&
            (bankAccountItem.accountType === 'savings' ||
              bankAccountItem.accountType === 'checking')))
      )
    }) || {}

  bankAccountObj.accountNumber = (
    bankAccountObj?.accountNumber as IEncrypted
  )?.display

  return bankAccountObj
}

export async function deleteBankAccount(
  id: string,
  companyId: string,
  customerAccountId?: string,
) {
  let found = false
  if (!customerAccountId) {
    const company = await Company.findOne({ _id: companyId })
    found = (company?.bankAccounts || []).some((a) => a.toString() === id)
  } else {
    const customerAccount = await CustomerAccount.findOne({
      _id: customerAccountId,
      company_id: companyId,
    })
    found = (customerAccount?.bankAccounts || []).some(
      (a) => a.toString() === id,
    )
  }

  if (!found) {
    throw new exceptions.LogicalError('Account not found')
  }

  await BankAccount.findByIdAndUpdate(id, {
    isDeactivated: true,
    $unset: { 'cardMetadata.token': true },
  })

  // await BankAccount.findByIdAndDelete(id)
  // company.bankAccounts = company.bankAccounts.filter(a => a.toString() !== id)
  // await company.save()
}

export async function updatePrimaryForIHCAutoPay(
  companyId: string,
  paymentMethodId: string,
): Promise<void> {
  const company = await Company.findById(companyId).populate('bankAccounts')

  if (!company) {
    throw new exceptions.LogicalError('Company not found')
  }

  const targetPaymentMethod = company.bankAccounts?.find(
    (account) => account._id.toString() === paymentMethodId,
  )

  if (!targetPaymentMethod) {
    throw new exceptions.LogicalError('Payment method not found')
  }

  const bankAccountIds =
    company.bankAccounts?.map((account) => account._id) || []

  if (bankAccountIds.length > 0) {
    await BankAccount.updateMany(
      {
        _id: mongoose.trusted({
          $in: bankAccountIds,
        }),
      },
      {
        $set: {
          isPrimaryForIHCAutoPay: false,
        },
      },
      { multi: true },
    )
  }
  await BankAccount.findByIdAndUpdate(paymentMethodId, {
    $set: {
      isPrimaryForIHCAutoPay: true,
    },
  })
}

export async function turnOffIHCAutoPay(companyId: string): Promise<void> {
  const company = await Company.findById(companyId).populate('bankAccounts')

  if (!company) {
    throw new exceptions.LogicalError('Company not found')
  }

  const bankAccountIds =
    company.bankAccounts?.map((account) => account._id) || []

  if (bankAccountIds.length > 0) {
    await BankAccount.updateMany(
      {
        _id: mongoose.trusted({
          $in: bankAccountIds,
        }),
      },
      {
        $set: {
          isPrimaryForIHCAutoPay: false,
        },
      },
      { multi: true },
    )
  }
}

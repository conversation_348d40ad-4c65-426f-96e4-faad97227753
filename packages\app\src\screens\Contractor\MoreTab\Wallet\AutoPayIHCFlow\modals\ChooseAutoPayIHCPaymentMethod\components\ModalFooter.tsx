import React from 'react'
import { StyleSheet, View } from 'react-native'
import { useTranslation } from 'react-i18next'
import { AutoPayIHCMode } from './ModalContent'
import useIsMobile from '../../../../../../PayablesTab/hooks/useIsMobile'
import { BButton } from '../../../../../../../../ui/atoms/builder-2.0/Button'

interface ModalFooterProps {
  onSavePaymentMethod: () => void
  onTurnOffAutoPay?: () => void
  hasSelectedPaymentMethod: boolean
  mode?: AutoPayIHCMode
  isSubmitting?: boolean
  submitDisabled?: boolean
}

export const ModalFooter = ({
  onSavePaymentMethod,
  onTurnOffAutoPay,
  hasSelectedPaymentMethod,
  mode = AutoPayIHCMode.Enable,
  isSubmitting = false,
  submitDisabled,
}: ModalFooterProps) => {
  const { t } = useTranslation('global')
  const isMobile = useIsMobile()

  const getPrimaryButtonStyle = () => {
    if (isMobile) {
      const hasTwoButtons =
        mode === AutoPayIHCMode.UpdateAutoPayNotRequired && onTurnOffAutoPay
      return hasTwoButtons
        ? styles.mobilePrimaryButton
        : styles.mobileSinglePrimaryButton
    }

    if (mode === AutoPayIHCMode.UpdateAutoPayNotRequired && onTurnOffAutoPay) {
      return { paddingHorizontal: 30 }
    }

    return { paddingHorizontal: 5 }
  }

  const getMobileContainerStyle = () => {
    if (!isMobile) return styles.buttonContainer

    const hasTwoButtons =
      mode === AutoPayIHCMode.UpdateAutoPayNotRequired && onTurnOffAutoPay
    return hasTwoButtons
      ? styles.mobileButtonContainer
      : styles.mobileSingleButtonContainer
  }
  return (
    <View style={getMobileContainerStyle()}>
      {mode === AutoPayIHCMode.UpdateAutoPayNotRequired && onTurnOffAutoPay && (
        <BButton
          onPress={onTurnOffAutoPay}
          disabled={isSubmitting}
          buttonStyle={[
            styles.button,
            isMobile
              ? styles.mobileSecondaryButton
              : styles.secondaryActionButton,
          ]}
          labelStyle={[styles.buttonText, isMobile && styles.mobileButtonText]}
          contentStyle={isMobile ? { marginHorizontal: -10 } : undefined}
          style={isMobile ? styles.mobileButtonWrapper : undefined}
        >
          {t('autoPayIHCFlow.updatePaymentMethod.turnOff')}
        </BButton>
      )}
      <BButton
        onPress={onSavePaymentMethod}
        disabled={!hasSelectedPaymentMethod || isSubmitting || submitDisabled}
        loading={isSubmitting}
        labelStyle={[styles.buttonText, isMobile && styles.mobileButtonText]}
        buttonStyle={[styles.button, getPrimaryButtonStyle()]}
        style={isMobile ? styles.mobileButtonWrapper : undefined}
      >
        {mode === AutoPayIHCMode.Enable
          ? t('autoPayIHCFlow.addOrSelectPaymentMethod.buttonText')
          : t('autoPayIHCFlow.updatePaymentMethod.save')}
      </BButton>
    </View>
  )
}

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    marginTop: 5,
    gap: 10,
    paddingHorizontal: 30,
    justifyContent: 'flex-end',
    height: 80,
  },
  mobileButtonContainer: {
    flexDirection: 'row',
    width: '100%',
    gap: 12,
    alignItems: 'stretch',
    borderTopColor: '#DEE5EB',
    borderTopWidth: 1,
    padding: 16,
  },
  mobileSingleButtonContainer: {
    padding: 16,
    alignItems: 'stretch',
    borderTopColor: '#DEE5EB',
    borderTopWidth: 1,
    width: '100%',
  },
  secondaryActionButton: {
    backgroundColor: '#EC002A',
    paddingHorizontal: 10,
  },
  mobileSecondaryButton: {
    backgroundColor: '#EC002A',
    height: 48,
    width: '100%',
  },
  mobilePrimaryButton: {
    height: 48,
    width: '100%',
  },
  mobileButtonWrapper: {
    flex: 1,
    alignItems: 'stretch',
  },
  mobileSinglePrimaryButton: {
    height: 48,
    width: '100%',
  },
  button: {
    borderRadius: 8,
  },
  buttonText: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '700',
    fontFamily: 'Inter',
    textAlign: 'center',
  },
  mobileButtonText: {
    letterSpacing: 0,
  },
})

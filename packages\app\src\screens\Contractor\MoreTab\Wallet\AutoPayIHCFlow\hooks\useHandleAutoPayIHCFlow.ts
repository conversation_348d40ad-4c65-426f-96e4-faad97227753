import { useCallback } from 'react'
import { paths } from '../../../../../links'
import { AutoPayIHCMode } from '../modals/ChooseAutoPayIHCPaymentMethod/components'
import { useSaveAutoPayIHCPaymentMethod } from './useSaveAutoPayIHCPaymentMethod'

export enum FLOWS {
  NONE,
  CHOOSE_PAYMENT_METHOD,
  ADD_FIRST_PAYMENT_METHOD,
  ADD_ADDITIONAL_PAYMENT_METHOD,
  ADD_NEW_BANK_ACCOUNT,
  CONNECT_NEW_BANK_ACCOUNT,
  ADD_NEW_CREDIT_CARD,
  TURN_OFF_AUTO_PAY,
  AUTO_PAY_CONFIGURATION_SUCCESS,
}

export interface IPaymentMethod {
  id: string
  paymentMethodType: string
  name: string
  accountNumber: string
  accountType: string
  cardMetadata?: any
  isPrimaryForIHC?: boolean
  isDeactivated?: boolean
}

export const useHandleAutoPayIHCFlow = (
  setFlow: (flow: FLOWS) => void,
  setPaymentMethod: (method: IPaymentMethod | null) => void,
  refetch: () => void,
  navigation: any,
  onClose: () => void,
  mode: AutoPayIHCMode,
  paymentMethods?: IPaymentMethod[],
) => {
  const {
    saveAutoPayIHCPaymentMethod,
    loading: isSubmitting,
    error: submitError,
  } = useSaveAutoPayIHCPaymentMethod()

  const handleAddNewBankAccount = useCallback(() => {
    setFlow(FLOWS.ADD_NEW_BANK_ACCOUNT)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleAddNewCreditCard = useCallback(() => {
    setFlow(FLOWS.ADD_NEW_CREDIT_CARD)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleAddNewPaymentMethod = useCallback(() => {
    setFlow(FLOWS.ADD_ADDITIONAL_PAYMENT_METHOD)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleTurnOffAutoPay = useCallback(() => {
    if (mode === AutoPayIHCMode.UpdateAutoPayNotRequired) {
      setFlow(FLOWS.TURN_OFF_AUTO_PAY)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleChoosePaymentMethod = useCallback(() => {
    setFlow(FLOWS.CHOOSE_PAYMENT_METHOD)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const handleResetFlow = useCallback(() => {
    setFlow(FLOWS.CHOOSE_PAYMENT_METHOD)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleNewPaymentMethodAdded = useCallback(() => {
    refetch()
    setFlow(FLOWS.CHOOSE_PAYMENT_METHOD)

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const handleFinishPaymentMethodAdded = useCallback(() => {
    setFlow(FLOWS.CHOOSE_PAYMENT_METHOD)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleLinkBankManually = useCallback(() => {
    onClose()
    navigation.navigate(paths.LinkBank, {
      ihcAutoPayFlow: 'true',
    })
  }, [navigation, onClose])

  const handleSaveAutoPaymentMethod = useCallback(
    async (paymentMethodId: string) => {
      const success = await saveAutoPayIHCPaymentMethod(paymentMethodId)
      if (success) {
        if (paymentMethods) {
          const selectedMethod = paymentMethods.find(
            (method) => method.id === paymentMethodId,
          )
          if (selectedMethod) {
            setPaymentMethod(selectedMethod)
          }
        }
        setFlow(FLOWS.AUTO_PAY_CONFIGURATION_SUCCESS)
        return true
      }
      return false
    },
    [saveAutoPayIHCPaymentMethod, paymentMethods, setPaymentMethod, setFlow],
  )

  return {
    handleAddNewBankAccount,
    handleAddNewCreditCard,
    handleAddNewPaymentMethod,
    handleResetFlow,
    handleNewPaymentMethodAdded,
    handleLinkBankManually,
    handleChoosePaymentMethod,
    handleTurnOffAutoPay,
    handleSaveAutoPaymentMethod,
    handleFinishPaymentMethodAdded,
    isSubmitting,
    submitError,
  }
}

import axios, { Method } from 'axios'
import { round } from 'lodash'
import moment, { Moment } from 'moment'
import {
  InstallmentType,
  LOAN_REPAYMENT_SCHEDULE_STATUS,
  LOAN_REPAYMENT_STATUS,
  LOAN_REPAYMENT_TYPE,
  TimelineItemType,
  UI_LOAN_STATUS,
} from '@linqpal/models/src/dictionaries/loanStatuses'
import { ILoanApplication } from '../models/types'
import { Logger } from './logger/logger.service'
import { LOAN_APPLICATION_TYPE } from '@linqpal/models/src/dictionaries/loanApplicationTypes'
import { PricingProduct } from '@linqpal/models/src/dictionaries/pricingProduct'
import {
  LmsCreditStatus,
  PurchaseTypes,
} from '@linqpal/models/src/dictionaries'
import { RequestContext } from '../helpers/RequestContext'
import { ApiHeaders } from '../helpers/ApiRequester'
import { CreditApplicationType } from '@linqpal/models/src/dictionaries/creditApplicationType'
import { CriticalError } from '@linqpal/models/src/types/exceptions'
import { INoSupplierDetails } from './onBoarding/types'
import { ClientSession } from 'mongoose'

const {
  PAST_DUE_LATE,
  PAST_DUE,
  DUE_NEXT,
  PAST_DUE_PENALTY,
  PAST_DUE_LATE_AND_INTEREST,
} = UI_LOAN_STATUS
const { LATE_FEE, MANUAL_LATE_FEE, PENALTY_INTEREST_FEE } = LOAN_REPAYMENT_TYPE
const { PENDING, LATE } = LOAN_REPAYMENT_STATUS
const { CURRENT } = LOAN_REPAYMENT_SCHEDULE_STATUS

const log = new Logger({ module: 'LMS', subModule: 'lms.service' })

// ===================================== AutoPay =====================================

export async function getUpcomingLoan(
  paymentDate: string | moment.Moment,
  days = 3,
) {
  const date = moment(paymentDate).format('YYYY-MM-DD')
  const result = await requester.get<{ id: string }[]>(
    `AutopayLoans?UpcomingDate=${date}&CountDaysForUpcomming=${days}`,
  )
  log.debug({ result }, 'Upcoming and overdue Loans')
  return result
}

// ===================================== LOANS =====================================

//TBD filter by customer id
export async function loanList() {
  return requester.get('Loans')
}

export const enum PaymentStatus {
  None = 'None',
  Success = 'Success',
  Rejected = 'Rejected',
  Processing = 'Processing',
  Canceled = 'Canceled',
}

export const enum ReceivableType {
  Installment = 'Installment',
  LoanFee = 'LoanFee',
  LatePaymentFee = 'LatePaymentFee',
  ManualLatePaymentFee = 'ManualLatePaymentFee',
  ExtensionFee = 'ExtensionFee',
  PenaltyInterestFee = 'PenaltyInterestFee',
}

export const enum ReceivableStatus {
  None = 'None',
  Paid = 'Paid',
  Late = 'Late',
  Pending = 'Pending',
  Canceled = 'Canceled',
}

export const enum LoanPaymentType {
  AutoDebit = 'AutoDebit',
  Manual = 'Manual',
  Custom = 'Custom',
}

export const enum ScheduleStatus {
  Current = 'Current',
  Rescheduled = 'Rescheduled',
  Postponed = 'Postponed',
  Replanned = 'Replanned',
}

export const enum LoanStatus {
  Started = 'Started',
  Closed = 'Closed',
  Refinanced = 'Refinanced',
  Canceled = 'Canceled',
}

export const enum LoanProduct {
  LineOfCredit = 'LineOfCredit',
  ArAdvance = 'ARAdvance',
  InHouseCredit = 'InHouseCredit',
}

export const enum LoanParametersChangeType {
  Initial = 'Initial',
  Reschedule = 'Reschedule',
  Replan = 'Replan',
}

// TODO: VK: use pascal case for all for .NET compatibility?
export const enum LoanOrigin {
  Normal = 'normal',
  Express = 'express',
  Quote = 'quote',
  Factoring = 'factoring',
  NoSupplier = 'noSupplier',
}

export const enum DownPaymentStatus {
  NotRequired = 'NotRequired',
  Due = 'Due',
  Paid = 'Paid',
}

interface ILoanParameter {
  id: string
  isActive: boolean
  loanId: string
  loanTemplateId?: string
  loanFeePercentage: number
  installmentsNumber: number
  paymentIntervalInDays: number
  lateFeePercentage: number
  gracePeriod: number
  totalDurationInDays: number
  code: string
  note?: string
  createdBy?: string
  changeType: LoanParametersChangeType
  downPaymentPercentage: number
  downPaymentAmount: number
}

export interface IReceivable {
  id: string
  loanId?: string
  expectedDate: string
  paidDate?: string
  adjustDate?: string
  expectedAmount: number
  paidAmount: number
  processingAmount: number
  adjustAmount: number
  type: ReceivableType
  status: ReceivableStatus
  scheduleStatus: ScheduleStatus
}

export interface ICreateLoanPayable {
  payableId?: string
  amount: number
  invoiceNumber?: string | null
}

export interface ILoanPayable {
  id: string
  loanId: string
  payableId: string
  amount: number
  invoiceNumber?: string
}

interface ILoanDetails {
  loanId: string
  businessDaysLate: number
  daysLate: number
  nextPaymentAmount: number
  nextPaymentDate?: string
  lateAmount: number
  totalProcessingPaymentsAmount: number
  totalBalance: number
  principalBalance: number
  principalBalanceWithoutFee: number
  loanOutstandingAmount: number
  totalPaid: number
  totalOutstandingFeesAmount: number
  totalLoanAmount: number
  totalFeesAmount: number
  isAnyReceivableLate: boolean
  isFullyPaid: boolean
  oldestDueOrPastDueDate?: string
  refinancedPrincipalBalance: number
  refinancedOutstandingFeesAmount: number
  remainingNumberOfPayments: number
  numberOfLatePayments: number
  numberOfFailedPayments: number
  numberOfMissedPayments: number
}

interface IActiveLoanTemplate {
  code: string
  legacyId: string | null
  product: LoanProduct
}

export interface ILoanPayment {
  id: string
  date: string
  amount: number
  type: LoanPaymentType
  status: string
  transactionNumber: string
  createdAt: string
  updatedAt: string
}

export interface ILoan {
  id: string
  companyId: string
  amount: number
  refundAmount: number
  fee: number
  loanTemplateId?: string
  loanParameters: ILoanParameter[]
  loanReceivables: IReceivable[]
  activeReceivables: IReceivable[]
  payments: ILoanPayment[]
  loanDetails?: ILoanDetails
  status: LoanStatus
  isDeleted: boolean
  isOverdue: boolean
  startDate: string
  closeDate?: string | null
  lastPaymentDate?: string
  nextPaymentDate?: string
  nextPaymentAmount: number
  isAnyPaymentMissed: boolean
  currentStatus?: string
  currentAmount?: number
  projectId?: string
  drawApprovalId?: string
  merchantId?: string
  merchantName?: string
  loanPayables?: ILoanPayable[]
  creditId: string
  activeLoanTemplate: IActiveLoanTemplate
  term: string
  loanOrigin: LoanOrigin | null
  downPaymentStatus: DownPaymentStatus
  downPaymentStatusAt: string
  isAutoCollectionPaused?: boolean
  autoCollectionPausedAt?: string
  autoCollectionPausedBy?: string
}

interface ILoanPayablesDetails {
  id: string
  loanAmount: number
  loanTotalAmount: number
  totalPaidAmount: number
  totalUnpaidAmount: number
  totalPastDueAmount: number
  totalDueAmount: number
  oldestDueOrPastDueDate: string
  nextUpcomingInstallmentAmount: number
  nextUpcomingInstallmentDate: string
  oldestPastDueLateBusinessDays: number
  totalDailyPenaltyInterest: number
  amounts: any[]
}

export interface IReceivablesRescheduleItem {
  id?: string
  receivableType?: ReceivableType
  newExpectedAmount: number
  newExpectedDate: string
  newScheduleStatus: ScheduleStatus
}

export interface ILoanReplan {
  newLoanTemlateId: string
  replanDate?: string
}

export interface ITimelineItem {
  amount: number
  date: string
  dueStatus: InstallmentType
  itemType: TimelineItemType
  loanReceivableId: string | null
  paymentId: string | null
}

export interface IChangePaymentReceivableItem {
  id: string
  amount: number
  receivableType: ReceivableType
}

export interface IChangePaymentInfo {
  status: PaymentStatus
  transactionNumber: string
  paymentId: string
  amount: number
  receivables: IChangePaymentReceivableItem[]
}

export interface ILoanIssueInfo {
  loanId: string
  customerId: string
  amount: number
  fee: number
}

export interface IResultWithPagination<T> {
  pageNumber: number
  pagesCount: number
  totalCount: number
  result: T[]
}

interface IQueryWithPagination {
  pageSize?: number
  pageNumber?: number
}

export interface ILoanReceivablesQuery extends IQueryWithPagination {
  companyId: string
  dateFrom: string
  dateTo: string
  isOverdue: boolean
  loanIds: string[]
}

export interface ILoanQuery {
  fromDate?: Date
  toDate?: Date // toDate is required when fromDate is specified
  einHash?: string
  payableId?: string
  projectId?: string
  product?: PricingProduct
  loanStatus?: LoanStatus
  showLateOnly?: boolean
  detailed?: boolean
}

interface IGetLoansByIdsQuery extends IQueryWithPagination {
  searchIdType: 'DrawApprovalId' | 'LoanReceivableId' | 'PayableId'
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  detailed?: boolean
}

export interface ICredit {
  id: string
  createdAt: string
  updatedAt: string
  companyId: string | null
  merchantId: string | null
  creditApplicationId: string | null
  projectId: string | null
  startDate: string
  closeDate: string | null
  creditLimit: number
  currency: string | null
  status: string
  purchaseType: string
  product: string
  pastDueDays: number
  revenueFallPercentage: number
  creditDetails: {
    numberOfActiveDraws: number
    numberOfDraws: number
    availableCredit: number
    outstandingCredit: number
    usedCreditPercentage: number
    creditHoldAmount: number
    totalCreditHoldAmount: number
    businessDaysLate: number
    lateAmount: number
    principalBalance: number
    drawOutstandingAmount: number
    processingAmount: number
    totalPaid: number
    totalDrawAmount: number
    isAnyReceivableLate: true
    isFullyPaid: true
    oldestDueOrPastDueDate: string | null
    totalDailyPenaltyInterest: number
    hadPenalty: true
  }
}

export async function getLoanReceivablesByCompanyId(
  query: ILoanReceivablesQuery,
): Promise<IResultWithPagination<IReceivable>> {
  const resp = await requester.post(`/LoanReceivables/Company`, query)
  return resp
}

const isPendingLateFee = (l: any): boolean => {
  const LateFeeTypes = [LATE_FEE, MANUAL_LATE_FEE]
  return LateFeeTypes.includes(l.type) && [LATE, PENDING].includes(l.status)
}
const isPendingPenalty = (l: any): boolean =>
  l.type === PENALTY_INTEREST_FEE && [LATE, PENDING].includes(l.status)

const getTypeOfPastDueStatus = (receivables: any[]) => {
  const isLateFeeExists = receivables.find((lr: any) => isPendingLateFee(lr))
  const isPenaltyExists = receivables.find((lr: any) => isPendingPenalty(lr))
  //console.log(receivables, isLateFeeExists, isPenaltyExists)
  if (isLateFeeExists && isPenaltyExists) return PAST_DUE_LATE_AND_INTEREST
  else if (isPenaltyExists) return PAST_DUE_PENALTY
  else if (isLateFeeExists) return PAST_DUE_LATE
  else return PAST_DUE
}

export const getCurrentLoanStatusAndAmount = (
  loanLms: any,
  loanDetails: any = {},
) => {
  const {
    lateAmount,
    totalProcessingPaymentsAmount: processingAmount,
    nextPaymentAmount,
  } = loanDetails
  const result = { status: DUE_NEXT, amount: 0 }
  const filteredReceivables = loanLms.loanReceivables.filter(
    (lr: any) => lr.scheduleStatus === CURRENT,
  )

  if ((!lateAmount || processingAmount >= lateAmount) && nextPaymentAmount) {
    result.amount = nextPaymentAmount
  } else if (processingAmount < lateAmount) {
    const today = moment().utc().startOf('day')
    const total = filteredReceivables.reduce((sum: number, lr: any) => {
      const expected = moment.tz(lr.expectedDate, 'UTC')
      if (
        lr.status === LATE ||
        (lr.status === PENDING && expected.isBefore(today, 'day')) ||
        isPendingLateFee(lr) ||
        isPendingPenalty(lr)
      ) {
        sum = round(sum + (lr.expectedAmount - lr.paidAmount), 2)
      }
      return sum
    }, 0)

    result.status = getTypeOfPastDueStatus(filteredReceivables)
    result.amount = total - processingAmount
  }
  return result
}

const getFeeEndpoint = (type: ReceivableType) => {
  switch (type) {
    case ReceivableType.ManualLatePaymentFee:
    case ReceivableType.LatePaymentFee:
      return 'Admin/LatePaymentFee'
    case ReceivableType.ExtensionFee:
      return 'Admin/ExtensionFee'
    case ReceivableType.PenaltyInterestFee:
      return 'Admin/PenaltyInterest'
    default:
      throw new Error(`Unsupported fee type ${type}`)
  }
}

export async function findLoans(filter: ILoanQuery): Promise<ILoan[]> {
  // prettier-ignore
  return requester.get<ILoan[]>(
    'Loans', {
      FromDate: filter.fromDate ? moment(filter.fromDate).format('YYYY-MM-DD') : '',
      ToDate: filter.toDate ? moment(filter.toDate).format('YYYY-MM-DD') : '',
      EinHash: filter.einHash,
      PayableId: filter.payableId,
      ProjectId: filter.projectId,
      Product: filter.product,
      LoanStatus: filter.loanStatus,
      ShowLateOnly: filter.showLateOnly,
      Detailed: filter.detailed ?? false,
  })
}

export async function findLoansByIds(
  ids: string[],
  query: IGetLoansByIdsQuery,
): Promise<IResultWithPagination<ILoan>> {
  const response = await requester.post<IResultWithPagination<ILoan>>(
    `Loans/getByIds?detailed=${query.detailed}&searchIdType=${
      query.searchIdType
    }&sortBy=${query.sortBy ?? ''}&sortOrder=${
      query.sortOrder ?? ''
    }&pageNumber=${query.pageNumber}&pageSize=${query.pageSize}`,
    ids,
  )

  return response
}

export async function getLoanInfo(
  loanId: string,
  detailed = true,
): Promise<ILoan | null> {
  const resp = await requester.get(`Loans/${loanId}?Detailed=${detailed}`)
  if (!resp) {
    return null
  }
  const nextPaymentDate = resp.loanDetails?.nextPaymentDate
  const nextPaymentAmount = resp.loanDetails?.nextPaymentAmount
  const isAnyPaymentMissed = resp.loanDetails?.isAnyReceivableLate
  const updated = getActiveReceivables(resp)
  const result = getCurrentLoanStatusAndAmount(resp, resp.loanDetails)

  return {
    ...updated,
    nextPaymentDate,
    nextPaymentAmount,
    isAnyPaymentMissed,
    currentStatus: result.status,
    currentAmount: result.amount,
  }
}

export async function getInstallments(
  loanId: string,
): Promise<ITimelineItem[]> {
  const res = requester.get(`loanreceivables/payments?loanId=${loanId}`)
  return res
}

export async function getCreditInfoByCreditApplicationIdAndCompanyId(
  creditApplicationId: string,
  companyId: string,
): Promise<ICredit | null> {
  const res = await requester.get<ICredit[]>(`Credits`, {
    CompanyId: companyId,
    CreditApplicationId: creditApplicationId,
    Detailed: 'true',
  })

  // take latest in case of multiple
  return res.at(-1) ?? null
}

export async function getCreditCompanyInfo(
  companyId: string,
): Promise<ICredit[]> {
  const res = await requester.get<ICredit[]>(
    `Credits/Company/${companyId}?detailed=true`,
  )

  return res
}

export async function getCreditsByCompanyIds(
  companyIds: string[],
): Promise<ICredit[]> {
  const res = await requester.post<ICredit[]>(`Credits/Company/`, {
    ids: companyIds,
  })

  return res
}

export async function updateCreditDetails(
  creditId: string,
  userId: string,
  updateCreditDto: {
    creditLimit?: number
    purchaseType?: PurchaseTypes
    revenueFallPercentage?: number
    notes?: string
  },
): Promise<ICredit> {
  const url = `Admin/Credits/${creditId}/Details`
  return requester.patch(url, updateCreditDto, undefined, userId)
}

export async function findCredits(filter: {
  projectId?: string
  companyId?: string
  merchantId?: string
  creditApplicationId?: string
  status?: LmsCreditStatus
  productType?: PricingProduct
  detailed?: boolean
}): Promise<ICredit[]> {
  return requester.get<ICredit[]>('Credits', {
    creditApplicationId: filter.creditApplicationId,
    projectId: filter.projectId,
    merchantId: filter.merchantId,
    companyId: filter.companyId,
    product: filter.productType,
    status: filter.status,
    detailed: filter.detailed ?? false,
  })
}

export async function createCredit(credit: {
  creditApplicationId: string
  merchantId: string
  companyId: string
  startDate: Date | string
  productType: PricingProduct
  creditLimit: number
  currency: string
  closeDate?: Date | string
  pastDueDays?: number
  revenueFallPercentage?: number
}) {
  const data = {
    CompanyId: credit.companyId,
    MerchantId: credit.merchantId,
    CreditApplicationId: credit.creditApplicationId,
    StartDate: credit.startDate,
    CreditLimit: credit.creditLimit,
    Currency: credit.currency,
    Product: credit.productType,
    CloseDate: credit.closeDate,
    PastDueDays: credit.pastDueDays,
    RevenueFallPercentage: credit.revenueFallPercentage,
  }
  return requester.post(`Credits`, data)
}

export async function getLoansToSync(detailed = true): Promise<ILoan[] | null> {
  return requester.patch(`Admin/Loans?detailed=${detailed}`)
}

export async function createLoan(
  customerId: string,
  companyName: string,
  loanTemplateId: string,
  loanAmount: number,
  einHash: string,
  loanOrigin: LoanOrigin,
  fundingSource: string,
  projectId?: string,
  merchantId?: string,
  merchantName?: string,
  drawApprovalId?: string,
  loanPayables?: ICreateLoanPayable[],
  downPaymentPercentage?: number | null,
  downPaymentAmount?: number | null,
  downPaymentExpireAt?: string | null,
) {
  const data = {
    companyId: customerId,
    companyName: companyName,
    amount: loanAmount,
    einHash: einHash,
    loanTemplateId: loanTemplateId,
    loanOrigin: loanOrigin,
    fundingSource: fundingSource,
    projectId: projectId,
    merchantId: merchantId,
    merchantName: merchantName,
    drawApprovalId: drawApprovalId,
    loanPayables: loanPayables,
    downPaymentPercentage: downPaymentPercentage,
    downPaymentAmount: downPaymentAmount,
    downPaymentExpireAt: downPaymentExpireAt,
  }

  log.info(data, '`LMS.createLoan` request data')

  const loan = await requester.post('Loans', data)

  return {
    id: loan.id,
    paymentDate: loan.loanReceivables[0].expectedDate,
    amount: loan.amount,
    fee: loan.fee,
  }
}

async function changeLoanStatus(loanId: string, status: string) {
  const data = {
    status: status,
  }
  const url = `/Loans/${loanId}`
  return requester.patch(url, data)
}

export async function getLoanPayablesDetails(
  loanId: string,
  date: string | Moment,
): Promise<ILoanPayablesDetails> {
  const searchDate = moment(date).format('YYYY-MM-DD')
  return requester.get(`Loans/${loanId}/Payables/Details?date=${searchDate}`)
}

export async function replanLoan(
  loanId: string,
  loanReplanDetails: ILoanReplan,
) {
  return requester.put(`/Loans/${loanId}`, loanReplanDetails)
}

export async function activateLoan(loanId: string) {
  return changeLoanStatus(loanId, LMSLoanStatus.Started)
}

export async function cancelLoan(loanId: string) {
  return changeLoanStatus(loanId, LMSLoanStatus.Canceled)
}

export async function closeLoan(loanId: string) {
  return changeLoanStatus(loanId, LMSLoanStatus.Closed)
}

export async function searchForLoan() {
  // TODO: need to find loan with displayId to avoid duplicates
}

export async function isLoanClosed(loanId: string) {
  const loan = await requester.get(`Loans/${loanId}?Detailed=false`)
  return loan.status === LMSLoanStatus.Closed
}

// ===================================== LoanTemplate =====================================

export async function getLoanTemplateList() {
  return requester.get('LoanTemplates')
}

export async function getLoanTemplateById(id: string) {
  return requester.get(`LoanTemplates/${id}`)
}

// ===================================== LoanParameters =====================================

export async function getLoanParametersList() {
  return requester.get('LoanParameters')
}

export async function getLoanParametersListByLoanId(loanId: string) {
  return requester.get(`LoanParameters?LoanId=${loanId}`)
}

export async function getLoanParametersById(id: string) {
  return requester.get(`LoanParameters/${id}`)
}

// ===================================== Calculator =====================================

export async function getPaymentPlan(amount: number) {
  return requester.get(`Calculator/${amount}`)
}

// ===================================== Paymnets =====================================

export async function getPaymentsByLoanId(loanId: string) {
  return requester.get(`Payments?LoanId=${loanId}`)
}

async function ChangePaymentStatus(
  paymentId: string,
  status: changePaymentStatus,
): Promise<IChangePaymentInfo> {
  const data = {
    status: status,
  }
  const url = `Payments/${paymentId}`
  return requester.patch(url, data)
}

export async function changePaymentTransactionNumber(
  paymentId: string,
  transactionNumber: string | undefined,
): Promise<IChangePaymentInfo> {
  const data = {
    transactionNumber: transactionNumber,
  }
  const url = `Payments/${paymentId}`
  const res = await requester.patch(url, data)

  log.debug({ res }, 'Change transaction number Result')

  return res
}

export async function rejectPayment(
  paymentId: string,
): Promise<IChangePaymentInfo> {
  const res = await ChangePaymentStatus(paymentId, changePaymentStatus.Rejected)

  log.debug({ res }, 'Reject Payment Result')

  return res
}

export async function approvePayment(
  paymentId: string,
): Promise<IChangePaymentInfo> {
  const res = await ChangePaymentStatus(paymentId, changePaymentStatus.Success)

  log.debug({ res }, 'Approve Payment Result')

  return res
}

export async function performPayment(
  loanId: string,
  amount: number,
  isAutoPay = false,
) {
  const data = {
    loanId: loanId,
    amount: amount,
    type: CreatePaymentType.Manual,
  }

  if (isAutoPay) {
    data.type = CreatePaymentType.AutoDebit
  }
  const url = `Payments`
  return requester.post(url, data)
}

// ===================================== Installmnets =====================================

//TBD rename to GetPayments?
export async function getPayments(loanId: string): Promise<
  {
    id: string
    date: string
    amount: number
    status: 'None' | 'Success' | 'Rejected' | 'Processing' | 'Canceled'
    type: 'AutoDebit' | 'Manual' | 'Custom'
    subType: PaymentSubType
    loanId: string
  }[]
> {
  return requester.get(`Payments?LoanId=${loanId}`)
}

export async function checkIsFullyPaid(loanId: string) {
  const loan = await requester.get(`Loans/${loanId}?Detailed=true`)
  return loan.loanDetails.isFullyPaid
}

export async function OverDueDetector() {
  return requester.patch(`OverDueDetector`)
}

// ===================================== Admin =====================================

export async function getLoanReceivablesByLoanId(
  loanId: string,
): Promise<IReceivable[]> {
  const receivables = await requester.get(
    `Admin/Loans/${loanId}/LoanReceivables`,
  )
  return receivables
}

export async function getNotes(loanId: string) {
  const resp = await requester.get<{ userId: string }[]>(
    `ChangeLogs?LoanId=${loanId}`,
  )
  if (!resp) {
    return null
  }
  return resp
}

export function getActiveReceivables(loan: ILoan) {
  const excludeRescheduled = loan?.loanReceivables.filter(
    (l) =>
      ![
        LOAN_REPAYMENT_SCHEDULE_STATUS.RESCHEDULED,
        LOAN_REPAYMENT_SCHEDULE_STATUS.POSTPONED,
      ].includes(l.scheduleStatus) &&
      l.status !== LOAN_REPAYMENT_STATUS.CANCELED,
  )
  return { ...loan, activeReceivables: excludeRescheduled }
}

interface Id {
  id: string
}

interface Ids {
  ids: Array<Id>
}

export async function getLoansByIds(
  ids: Ids,
  detailed = true,
): Promise<ILoan[]> {
  const loans = await requester.post(`Admin/Loans?detailed=${detailed}`, ids)

  if (!detailed) return loans

  const loansWithPaymentsData = loans.map((loan: ILoan) => {
    const updated = getActiveReceivables(loan)
    const nextPaymentDate = loan.loanDetails?.nextPaymentDate
    const nextPaymentAmount = loan.loanDetails?.nextPaymentAmount
    const isAnyPaymentMissed = loan.loanDetails?.isAnyReceivableLate
    const result = getCurrentLoanStatusAndAmount(loan, loan.loanDetails)
    return {
      ...updated,
      nextPaymentDate,
      nextPaymentAmount,
      isAnyPaymentMissed,
      currentStatus: result.status,
      currentAmount: result.amount,
    }
  })

  return loansWithPaymentsData
}

/*export async function getAgingList(companyId: string) {
  return requester.get(`/Admin/Loans/AgingList/Company/${companyId}`)
}*/

export async function getAgingList(
  customerCompanyId: string,
  product: CreditApplicationType = CreditApplicationType.LineOfCredit,
  supplierCompanyId?: string,
) {
  return requester.get(`Admin/Loans/AgingList/Company/${customerCompanyId}`, {
    merchantId: supplierCompanyId,
    product: product,
  })
}

export async function rescheduleLoanReceivables(
  loanId: string,
  loanReceivables: IReceivablesRescheduleItem[],
  userId: string,
  note: string,
): Promise<ILoan> {
  const data = {
    loanReceivables: loanReceivables,
    note: note,
  }
  const rescheduledLoan: ILoan = await requester.post(
    `Admin/Loans/${loanId}/LoanReceivables/Reschedule`,
    data,
    undefined,
    userId,
  )
  log.debug({ rescheduledLoan }, 'Rescheduled Loan')

  return rescheduledLoan
}

export async function UpdateLoanReceivablesByLoanId(
  loanId: string,
  receivables: IReceivable[],
  userId: string,
  note: string,
) {
  const data = {
    loanReceivables: receivables,
    note: note,
  }
  const updatedLoan = await requester.put(
    `Admin/Loans/${loanId}/LoanReceivables`,
    data,
    undefined,
    userId,
  )
  log.debug({ updatedLoan }, 'Updated Loan')

  return updatedLoan
}

export async function updateReceivableExpectedDate(
  receivableId: string,
  date: string,
  note: string,
  userId: string,
) {
  const data = {
    date: moment(date).format('YYYY-MM-DD'),
    note: note,
  }

  const updatedReceivable = await requester.patch(
    `Admin/LoanReceivables/${receivableId}`,
    data,
    undefined,
    userId,
  )
  log.debug({ updatedReceivable }, 'Updated receivable expected date')

  return updatedReceivable
}

export async function changeLoanStatusAdmin(
  loanId: string,
  status: string,
  note: string,
  userId: string,
) {
  const data = {
    status: status,
    note: note,
  }

  const changeLoanStatusResponse = await requester.patch(
    `Admin/Loans/${loanId}`,
    data,
    undefined,
    userId,
  )

  return changeLoanStatusResponse
}

export async function createManualPayment(
  loanId: string,
  amount: number,
  date: string,
  note: string,
  userId: string,
): Promise<IChangePaymentInfo> {
  const data = {
    loanId: loanId,
    amount: amount,
    date: moment(date).format('YYYY-MM-DD'),
    note: note,
  }

  const res = await requester.post('Admin/Payments', data, undefined, userId)
  log.debug({ res }, 'Admin manual payment Result')

  return res
}

export async function createFee(
  loanId: string,
  type: ReceivableType,
  amount: number,
  note: string,
  userId: string,
  date?: string,
) {
  const data = {
    loanId: loanId,
    expectedAmount: amount,
    expectedDate: moment(date).format('YYYY-MM-DD'),
    note: note,
  }

  const endpoint = getFeeEndpoint(type)
  await requester.post(endpoint, data, undefined, userId)
}

export async function cancelFee(
  loanReceivableId: string,
  type: ReceivableType,
  note: string,
  userId: string,
) {
  const data = {
    status: 'Canceled',
    note,
  }

  const endpoint = getFeeEndpoint(type)
  const res = await requester.patch(
    `${endpoint}/${loanReceivableId}`,
    data,
    undefined,
    userId,
  )

  return res
}

export async function changeFeeExpectedAmount(
  loanReceivableId: string,
  type: ReceivableType,
  expectedAmount: number,
  note: string,
  userId: string,
) {
  const data = {
    expectedAmount,
    note,
  }

  const endpoint = getFeeEndpoint(type)
  const res = await requester.patch(
    `${endpoint}/${loanReceivableId}`,
    data,
    undefined,
    userId,
  )

  return res
}

export async function CreateRefundPayment(
  loanId: string,
  amount: number,
  note: string,
  userId: string,
  date?: string,
) {
  const data = {
    loanId: loanId,
    amount: amount,
    date: moment(date).format('YYYY-MM-DD'),
    note: note,
  }

  await requester.post('Admin/Refunds', data, undefined, userId)
}

export async function cancelPayment(
  paymentId: string,
  note: string,
  userId: string,
): Promise<IChangePaymentInfo> {
  const data = {
    note: note,
    status: AdminPaymentStatus.Canceled,
  }
  const res = await requester.patch(
    `Admin/Payments/${paymentId}`,
    data,
    undefined,
    userId,
  )

  log.debug({ res }, 'Cancel Payment Result')

  return res
}

export function getLoanOrigin(
  app: ILoanApplication,
  noSupplierDetails?: INoSupplierDetails | null,
): LoanOrigin {
  if (app.type === LOAN_APPLICATION_TYPE.QUOTE) {
    return LoanOrigin.Quote
  }

  if (app.metadata?.repayment?.autoTradeCreditEnabled) {
    return LoanOrigin.Express
  }

  if (noSupplierDetails) {
    return LoanOrigin.NoSupplier
  }
  return LoanOrigin.Normal
}

// ===================================== UTILLS =====================================

export const syncLoanApplicationFields = async (
  app: ILoanApplication,
  session: ClientSession | null = null,
): Promise<void> => {
  const loan = await getLoanInfo(app.lms_id)

  const nextPaymentDate = loan?.loanDetails?.nextPaymentDate
  const nextPaymentAmount = loan?.loanDetails?.nextPaymentAmount
  const remainingAmount = loan?.loanDetails?.loanOutstandingAmount

  app.lastPaymentDate = moment(loan?.lastPaymentDate).format('YYYY-MM-DD')
  app.nextPaymentDate = moment(nextPaymentDate).format('YYYY-MM-DD')
  app.nextPaymentAmount = nextPaymentAmount
  app.remainingAmount = remainingAmount ?? 0
  app.processingAmount = loan?.loanDetails?.totalProcessingPaymentsAmount ?? 0
  app.pastDueAmount = loan?.loanDetails?.lateAmount ?? 0

  await app.save({ session })
}

export const isAnyPaymentMissed = (loan: any) => {
  const late = loan?.loanDetails?.isAnyReceivableLate
  return !!late
}

export const GetRemainingAmount = (loanLms: ILoan): number => {
  const remainingAmount = loanLms.loanDetails?.loanOutstandingAmount ?? 0
  return round(remainingAmount, 2)
}

export async function toggleLoanAutoPaymentCollection(
  loanId: string,
  isAutoCollectionPaused: boolean,
  userId: string,
): Promise<ILoan> {
  const data = {
    isAutoCollectionPaused,
  }

  const url = `Admin/Loans/${loanId}/autocollection`
  const result = await requester.patch(url, data, undefined, userId)

  log.debug(
    { loanId, isAutoCollectionPaused, userId, result },
    'Toggle auto collection',
  )

  return result
}

async function request<T = any>(
  method: Method,
  url: string,
  data: any,
  params: any,
  baseUrl?: string,
  userId?: string,
) {
  const apiKey = process.env.LS_API_KEY

  const headers = {
    'Content-Type': 'application/json',
    ApiKey: '',
    userId: '',
    [ApiHeaders.CorrelationId]: RequestContext.current?.requestId ?? '',
  }

  if (apiKey) {
    headers.ApiKey = apiKey
  }

  if (userId) {
    headers.userId = userId
  }

  if (!data) {
    data = {}
  }
  let lsUrl =
    'https://ca-loan-service-dev.bravesea-34bfcf76.westus.azurecontainerapps.io'

  switch (process.env.LP_MODE) {
    case 'dev':
      lsUrl =
        'https://ca-loan-service-dev.bravesea-34bfcf76.westus.azurecontainerapps.io'
      break
    case 'beta':
      lsUrl = 'https://api-beta.bluetape.com/loanService'
      break
    case 'qa':
      lsUrl = 'https://api-qa.bluetape.com/loanService'
      break
    case 'prod':
      lsUrl = 'https://api-prod.bluetape.com/loanService'
      break
  }

  const base_url = baseUrl || lsUrl
  if (!params) {
    params = {}
  }
  const fullUrl = `${base_url}/${url}`
  log.debug({ method, url: fullUrl, data, params }, 'LMS request')

  const axiosWithInterceptor = axios.create({
    baseURL: base_url,
    headers,
    params,
  })

  axiosWithInterceptor.interceptors.response.use(
    async (response) => {
      return response
    },
    async (error) => {
      const { response, config } = error
      // Retry Later Error
      if (
        response.status === 504 ||
        response.status === 502 ||
        (response.status === 500 && method.toLowerCase() === 'get')
      ) {
        config.params.retry = config.params.retry ? config.params.retry + 1 : 1
        await new Promise((resolve) => setTimeout(resolve, 1000))
        if (config.params.retry < 5) {
          return axiosWithInterceptor.request(config)
        }
      }
      return Promise.reject(response.data || error)
    },
  )

  try {
    const response = await axiosWithInterceptor.request<T>({
      method,
      url: fullUrl,
      data: method !== 'get' ? data : null,
    })
    log.debug({ method, url: fullUrl, data, params, response }, 'LMS response')
    return response.data
  } catch (error: any) {
    log.error(
      { method, url: fullUrl, data, params, err: error },
      'LMS request error',
    )

    throw new CriticalError(error.message, { urL: fullUrl, error })
  }
}

const requester = {
  get: <T = any>(
    url: string,
    params?: any,
    baseUrl?: string,
    userId?: string,
  ) => {
    return request<T>('get', url, null, params, baseUrl, userId)
  },
  post: <T = any>(
    url: string,
    data?: any,
    baseUrl?: string,
    userId?: string,
  ) => {
    return request<T>('post', url, data, null, baseUrl, userId)
  },
  put: <T = any>(
    url: string,
    data?: any,
    baseUrl?: string,
    userId?: string,
  ) => {
    return request<T>('put', url, data, null, baseUrl, userId)
  },
  patch: <T = any>(
    url: string,
    data?: any,
    baseUrl?: string,
    userId?: string,
  ) => {
    return request<T>('patch', url, data, null, baseUrl, userId)
  },
  delete: <T = any>(
    url: string,
    params?: any,
    baseUrl?: string,
    userId?: string,
  ) => {
    return request<T>('delete', url, null, params, baseUrl, userId)
  },
}

enum AdminPaymentStatus {
  Canceled = 'Canceled',
}

export enum LMSLoanStatus {
  Canceled = 'Canceled',
  Pending = 'Pending',
  Created = 'Created',
  Closed = 'Closed',
  Started = 'Started',
  Defaulted = 'Defaulted',
  Recovered = 'Recovered',
  Refinanced = 'Refinanced',
}

export enum PaymentSubType {
  Repayment = 'Repayment',
  Refund = 'Refund',
}

enum changePaymentStatus {
  Success = 'Success',
  Rejected = 'Rejected',
}

export enum CreatePaymentType {
  AutoDebit = 'AutoDebit',
  Manual = 'Manual',
}

export const LoanTemplateIdByPaymentPlan = {
  30: {
    lmsTemplateId: '3476b83d-f7c7-492d-a986-a6e3b3b87b43',
  },
  60: {
    lmsTemplateId: '3ee4cbd6-935a-436e-b2d1-041ac9a5dc11',
  },
  90: {
    lmsTemplateId: '7ce0034d-800e-4ce0-9585-e9dd17338878',
  },
  '60vc': {
    lmsTemplateId: '3ee4cbd6-935a-436e-b2d1-041ac9a5dc11',
  },
  '90vc': {
    lmsTemplateId: '7ce0034d-800e-4ce0-9585-e9dd17338878',
  },
  '120vc': {
    lmsTemplateId: '7ce0034d-900e-4ce0-9585-e9dd17338878',
  },
}

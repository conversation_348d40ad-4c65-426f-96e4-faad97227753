import moment from 'moment'
import * as math from 'mathjs'
import { Logger } from '../logger/logger.service'
import { loanCardPayment, queryCard } from '../tabapay'
import mongoose from 'mongoose'
import { Company, Operation } from '../../models'
import { drawRepayment } from '../payment/achPayment.service'
import {
  getSourceBankAccount,
  isPaymentMethodCard,
} from '../bankAccounts/helper'
import { LoanServicingAccounts } from '../loan'
import { dictionaries, exceptions, IEncrypted } from '@linqpal/models'
import { States, statesHashMap } from '@linqpal/models/src/dictionaries'
import { IBankAccount, ICompany, ILoanApplication } from '../../models/types'
import { pull } from './ach.pull.controller'
import CryptService from '../crypt.service'
import { getEnvironmentVariables } from '../secrets-manager.service'
import { IAccountInfoResponse } from '../tabapay/types'
import { getCardFee } from '@linqpal/models/src/helpers/getCardFee'

const logger = new Logger({ module: 'LoanPro', subModule: 'makeLoanPayments' })

export const makeLoanPayment = async (
  {
    loanApplication,
    amount,
    paymentDate,
    bankAccount,
    lms_paymentId,
    cardToken,
    firstName,
    lastName,
    phoneNumber,
    isAutoDebit,
  }: {
    loanApplication: ILoanApplication
    amount: number
    paymentDate: string | Date
    bankAccount?: Partial<IBankAccount>
    lms_paymentId?: string
    cardToken?: string
    firstName?: string
    lastName?: string
    phoneNumber?: string
    isAutoDebit?: boolean
  },
  session: mongoose.ClientSession | null = null,
  cbwTimeout?: number,
) => {
  const log = logger.startTransaction()
  log.info(
    {
      loan_id: loanApplication._id,
      amount,
      paymentDate,
      bankAccount,
      lms_paymentId,
    },
    'STARTING ACH pulls for loan repayment',
  )

  try {
    if (amount < 0 || !amount)
      throw new exceptions.LogicalError(`Not valid amount: ${amount}`)

    const builderCompany = await Company.findById(
      loanApplication.company_id,
    ).populate('bankAccounts')

    if (!builderCompany)
      throw new exceptions.LogicalError('Company does not exist')

    if (
      !builderCompany.bankAccounts ||
      builderCompany.bankAccounts.length === 0
    )
      throw new exceptions.LogicalError(
        'Builder does not have a bank account to pull funds from',
      )
    let op = await Operation.findOne({
      owner_id: loanApplication._id,
      type: dictionaries.OPERATION_TYPES.LOAN.REPAYMENT,
      'metadata.paymentDate': moment(paymentDate).format('YYYY-MM-DD'),
      'metadata.lms_paymentId': lms_paymentId,
    }).session(session)

    if (op) {
      log.info({ op }, 'Operation found cancelling')
      return null
    }

    const sourceBankAccount = getSourceBankAccount(bankAccount, builderCompany)

    let fee = 0
    const reason = `Loan repayment, customerID: ${builderCompany.id}, loanID: ${loanApplication.lms_id}`
    if (isPaymentMethodCard(sourceBankAccount, cardToken)) {
      const [loanRepaymentOperation] = await Operation.create(
        [
          {
            status: dictionaries.OPERATION_STATUS.PROCESSING,
            owner_id: loanApplication._id,
            amount,
            type: dictionaries.OPERATION_TYPES.LOAN.REPAYMENT,
            metadata: {
              paymentDate: moment(paymentDate).format('YYYY-MM-DD'),
              payer_id: builderCompany.id,
              lms_paymentId,
              isAutoDebit,
            },
          },
        ],
        { session },
      )
      op = loanRepaymentOperation

      let card: IAccountInfoResponse
      if (cardToken) {
        card = await queryCard(
          {
            token: cardToken,
            firstName,
            lastName,
            phoneNumber,
          },
          false,
        )
      } else {
        const { network: cardNetwork, type: cardType } =
          sourceBankAccount?.cardMetadata
        card = { ...sourceBankAccount?.cardMetadata, cardNetwork, cardType }
      }
      fee = math.round((amount * getCardFee(card)) / 100, 2)

      await loanCardPayment(
        op,
        {
          transactionType: dictionaries.TABAPAY_TRANSACTION_TYPES.PULL,
          cardToken,
          firstName,
          lastName,
          sourceAccountId: sourceBankAccount?.cardMetadata?.accountId,
          destinationAccountId:
            process.env.LP_TABAPAY_SETTLEMENT_ACCOUNT_ID || '',
          amount,
          fee,
          reason,
        },
        session,
      )
    } else {
      const isDrawRepayment = await isEligibleForDrawRepayments(
        loanApplication.company_id,
      )

      if (isDrawRepayment) {
        await drawRepayment(
          loanApplication,
          sourceBankAccount,
          lms_paymentId,
          amount,
        )
      } else {
        const achSender = await getExternalACHPullPayload(
          loanApplication,
          builderCompany,
          sourceBankAccount,
        )
        console.log(achSender)
        const accounts =
          LoanServicingAccounts[loanApplication.fundingSource || 'cbw']
        const senderId =
          process.env.LP_MODE === 'prod' ? accounts.prod : accounts.nonProd

        const [cbwOperation] = await Operation.create(
          [
            {
              status: dictionaries.OPERATION_STATUS.PROCESSING,
              owner_id: loanApplication._id,
              amount,
              type: dictionaries.OPERATION_TYPES.LOAN.REPAYMENT,
              metadata: {
                paymentDate: moment(paymentDate).format('YYYY-MM-DD'),
                payer_id: builderCompany.id,
                lms_paymentId,
                isAutoDebit,
              },
            },
          ],
          { session },
        )
        op = cbwOperation
        await pull(
          op,
          {
            amount,
            currency: 'USD',
            transactionDateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            reason,
            fee: 0,
            ...achSender,
          },
          session,
          { identification: senderId.COLLECTION, timeout: cbwTimeout },
        )
      }
    }
    return op
  } catch (err) {
    log.error(
      { err, loan_id: loanApplication._id, amount, paymentDate, bankAccount },
      'An error occurred when trying to make loan payment',
    )
    throw err
  }
}

async function isEligibleForDrawRepayments(...companyIds: string[]) {
  await getEnvironmentVariables()
  const companies = process.env.DRAW_REPAYMENTS_COMPANIES
  logger.info(`DRAW_REPAYMENTS_COMPANIES: ${companies}`)

  if (companies) {
    if (companies.toLowerCase().includes('all')) {
      logger.info(`'all' found in DRAW_REPAYMENTS_COMPANIES`)
      return true
    }

    const ids = companies.split(',')
    const eligibleCompanyId = companyIds.find((id) => ids.includes(id))

    if (eligibleCompanyId) {
      logger.info(`found ${eligibleCompanyId} in DRAW_REPAYMENTS_COMPANIES`)
      return true
    }
  }

  return false
}

const getExternalACHPullPayload = async (
  app: ILoanApplication,
  builderCompany: ICompany,
  builderBankAccount: IBankAccount | Partial<IBankAccount> | undefined,
) => {
  const draft = app.draft
  const address = draft?.businessInfo_businessAddress
  const account =
    typeof builderBankAccount?.accountNumber === 'string'
      ? (builderBankAccount.accountNumber as string)
      : await CryptService.decrypt(
          (builderBankAccount?.accountNumber as IEncrypted).cipher,
        )
  const customerEIN = builderCompany.ein
    ? typeof builderCompany.ein === 'string'
      ? builderCompany.ein
      : await CryptService.decrypt(builderCompany.ein.cipher)
    : null
  return {
    customer: {
      userType: 'BUSINESS',
      firstName:
        builderBankAccount?.accountholderName ||
        builderCompany.legalName ||
        draft?.businessInfo_businessName?.legalName ||
        draft?.businessOwner_firstName,
      ...(customerEIN
        ? {
            identificationType: 'EIN',
            identification: customerEIN,
          }
        : {}),
    },
    customerAccount: {
      identification: account?.trim(),
      identificationType2: builderBankAccount?.accountType?.toUpperCase(),
      institution: {
        identification: builderBankAccount?.routingNumber?.trim() || '',
      },
    },
    customerContact: {
      primaryEmail: builderCompany.email,
      primaryPhone: builderCompany.phone,
    },
    customerPostalAddress: {
      addressLine1: address?.address,
      city: address?.city,
      state: statesHashMap[address?.state as States],
      zipCode: address?.zip,
    },
  }
}

import React from 'react'
import { View, StyleSheet } from 'react-native'
import { BtText } from '@linqpal/components/src/ui'
import { useResponsive } from '../../../../../../utils/hooks'
import { useTranslation } from 'react-i18next'
import { CheckBlueOutlined } from '../../../../../../assets/icons'

export const IHCNoneDescriptionLabel: React.FC = () => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  const benefits = [
    'Bank.IHC.None.Benefits.0',
    'Bank.IHC.None.Benefits.1',
    'Bank.IHC.None.Benefits.2',
    'Bank.IHC.None.Benefits.3',
  ]

  return (
    <View style={sm ? styles.container : styles.containerMobile}>
      <BtText
        style={
          sm
            ? styles.description
            : [styles.description, styles.descriptionMobile]
        }
      >
        {t('Bank.IHC.None.Description')}
      </BtText>

      <View style={styles.benefitsContainer}>
        {benefits.map((benefitKey, index) => (
          <View key={index} style={styles.benefitRow}>
            <CheckBlueOutlined width={20} height={20} />
            <BtText style={[styles.description, styles.benefitsText]}>
              {t(benefitKey as any)}
            </BtText>
          </View>
        ))}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  containerMobile: {
    marginBottom: 24,
  },
  container: {
    marginBottom: 24,
  },
  descriptionMobile: {
    fontSize: 14,
    lineHeight: 20,
  },
  description: {
    fontFamily: 'Inter, sans-serif',
    fontSize: 16,
    fontWeight: '500',
    color: '#001929',
    lineHeight: 28,
    letterSpacing: 0,
  },
  benefitsContainer: {
    marginTop: 8,
  },
  benefitRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 16,
    gap: 10,
  },
  benefitsText: {
    fontSize: 14,
    lineHeight: 20,
  },
})

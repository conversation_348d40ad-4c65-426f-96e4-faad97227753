import React from 'react'
import { TurnOffAutoPayIHCFlowProps, TurnOffAutoPayIHCFlowState } from './types'
import { SuccessStep } from './components/SuccessStep'
import { InitialStep } from './components/InitialStep'
import { useTurnOffAutoPayIHCFlow } from '../../hooks/useTurnOffAutoPayIHC'

export const TurnOffAutoPayIHCFlow: React.FC<TurnOffAutoPayIHCFlowProps> = ({
  onClose,
  onKeepAutoPayEnabled,
  onSuccess,
}) => {
  const { flowState, loading, handleTurnOffAutoPay } =
    useTurnOffAutoPayIHCFlow()

  switch (flowState) {
    case TurnOffAutoPayIHCFlowState.SUCCESS:
      return <SuccessStep visible={true} onClose={onSuccess} />
    case TurnOffAutoPayIHCFlowState.INIT:
    default:
      return (
        <InitialStep
          visible={true}
          onClose={onClose}
          onKeepAutoPayEnabled={onKeepAutoPayEnabled}
          onTurnOffAutoPay={handleTurnOffAutoPay}
          isLoading={loading}
        />
      )
  }
}

export default TurnOffAutoPayIHCFlow

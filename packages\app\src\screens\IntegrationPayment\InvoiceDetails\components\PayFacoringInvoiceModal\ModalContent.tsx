import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { StyleSheet, Text, View } from 'react-native'
import { useTranslation } from 'react-i18next'
import { DetailRow } from './components/DetailRow'
import { AddNewPaymentLink } from './components/AddNewPaymentLink'
import { currencyMask } from '../../../../../utils/helpers/masking'
import { IInvoiceTotal, PaymentTypeOptions } from './types'
import { Spacer } from '@linqpal/components/src/ui'
import { PaymentMethodField } from './components/PaymentMethodField'
import { formatPaymentMethodName, getInvoiceDetailsRows } from './helpers'
import { PaymentTypeSelector } from './components/PaymentTypeSelector'
import { getCardFee } from '@linqpal/models/src/helpers/getCardFee'
import { PAYMENT_METHOD_TYPE } from '@linqpal/models/src/dictionaries'

interface ModalContentProps {
  invoiceTotal: IInvoiceTotal
  invoice: any
  paymentMethod: any
  handleChangePaymentMethod: () => void
  handleAddNewPaymentMethod: () => void
  onCalculatedDetailsChange: (details: IInvoiceTotal) => void
}

export const PayFactoringInvoiceModalContent: React.FC<ModalContentProps> = ({
  invoiceTotal,
  invoice,
  paymentMethod,
  handleChangePaymentMethod,
  handleAddNewPaymentMethod,
  onCalculatedDetailsChange,
}) => {
  const { t } = useTranslation(['errors', 'global'])
  const { invoiceAmount, totalAmount, lateFee, customerFee, pricingPackage } =
    invoiceTotal

  const [paymentType, setPaymentType] = useState<PaymentTypeOptions>(
    PaymentTypeOptions.Full,
  )
  const [partialAmount, setPartialAmount] = useState('')
  const [calculatedPaymentDetails, setCalculatedPaymentDetails] =
    useState(invoiceTotal)

  const calculatePaymentDetails = useCallback(
    (amount: number) => {
      const finalLateFee = paymentType === PaymentTypeOptions.Full ? lateFee : 0
      const finalCustomerFee =
        paymentType === PaymentTypeOptions.Full ? customerFee ?? 0 : 0

      let processingFee = 0

      if (paymentMethod.paymentMethodType === PAYMENT_METHOD_TYPE.CARD) {
        const cardFee = getCardFee({
          cardNetwork: paymentMethod?.cardMetadata?.network,
          cardType: paymentMethod?.cardMetadata?.type,
          isRegulated: paymentMethod?.cardMetadata?.isRegulated,
        })

        processingFee =
          (amount + finalCustomerFee + finalLateFee) * (cardFee / 100)
      } else if (pricingPackage) {
        processingFee =
          ((amount + finalCustomerFee + finalLateFee) *
            pricingPackage.customer.percentage) /
            100 +
          pricingPackage.customer.amount
      }

      return {
        fee: processingFee,
        invoiceAmount: amount,
        lateFee,
        customerFee,
        totalAmount: amount,
        totalAmountWithFee: amount + processingFee,
        pricingPackage,
      }
    },
    [paymentType, lateFee, customerFee, paymentMethod, pricingPackage],
  )

  useEffect(() => {
    if (paymentType === PaymentTypeOptions.Full) {
      setCalculatedPaymentDetails(invoiceTotal)
      onCalculatedDetailsChange(invoiceTotal)
    } else {
      const partialValue = Number(partialAmount.replace(/[^0-9.]/g, '')) || 0
      const newPaymentDetails = calculatePaymentDetails(partialValue)
      setCalculatedPaymentDetails({ ...newPaymentDetails, paymentType })
      onCalculatedDetailsChange({ ...newPaymentDetails, paymentType })
    }
  }, [
    paymentType,
    partialAmount,
    invoiceTotal,
    calculatePaymentDetails,
    onCalculatedDetailsChange,
  ])

  const handlePaymentTypeChange = useCallback((type: PaymentTypeOptions) => {
    setPaymentType(type)
    if (type === PaymentTypeOptions.Partial) {
      setPartialAmount('')
    }
  }, [])

  const handlePartialAmountChange = useCallback(
    (value: string) => {
      const sanitizedValue = value.replace(/-/g, '')
      const numericValue = Number(sanitizedValue.replace(/[^0-9.]/g, ''))

      if (
        !isNaN(numericValue) &&
        numericValue >= 0 &&
        numericValue <= totalAmount
      ) {
        setPartialAmount(sanitizedValue)
      } else if (numericValue < 0) {
        setPartialAmount('$0.00')
      }
    },
    [totalAmount],
  )
  const invoiceDetails = useMemo(() => {
    const rows = getInvoiceDetailsRows(t, invoice, invoiceAmount, lateFee)
    return rows.map((row, index, array) => (
      <DetailRow
        key={row.label}
        {...row}
        withDivider={index !== array.length - 1}
      />
    ))
  }, [t, invoice, invoiceAmount, lateFee])

  const getFormattedPaymentMethod = useMemo(
    () => formatPaymentMethodName(paymentMethod),
    [paymentMethod],
  )

  return (
    <View style={styles.container}>
      {/* Invoice Details */}
      <View style={styles.detailsContainer}>{invoiceDetails}</View>

      <Spacer height={10} />

      {/* Payment Method Selection */}
      <View style={styles.section}>
        <PaymentMethodField
          label={t('global:payIhcInvoice.paymentMethod.label')}
          value={getFormattedPaymentMethod}
          required
          onPress={handleChangePaymentMethod}
        />
        <AddNewPaymentLink
          onPress={handleAddNewPaymentMethod}
          label={t('global:payIhcInvoice.paymentMethod.addNewPaymentMethod')}
        />
      </View>

      <Spacer height={16} />

      {/* Payment Options*/}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          {t('global:payIhcInvoice.selectOptionToPay.title')}
        </Text>
        <PaymentTypeSelector
          paymentType={paymentType}
          onPaymentTypeChange={handlePaymentTypeChange}
          totalAmount={totalAmount}
          partialAmount={partialAmount}
          onPartialAmountChange={handlePartialAmountChange}
        />
      </View>

      {/* Fee and Total */}
      <View style={{ marginBottom: 15 }}>
        <DetailRow
          label={t('global:payIhcInvoice.processingFee')}
          value={currencyMask(calculatedPaymentDetails.fee ?? 0)}
          withDivider
        />
        <DetailRow
          label={t('global:payIhcInvoice.totalAmountToPay')}
          value={currencyMask(calculatedPaymentDetails.totalAmountWithFee ?? 0)}
          isTotal
          withDivider
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  detailsContainer: {
    backgroundColor: '#F8F9F9',
    borderRadius: 8,
    paddingHorizontal: 20,
    marginBottom: 24,
    paddingVertical: 4,
  },
  section: {
    marginBottom: 24,
  },
  input: {
    height: 48,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '600',
    letterSpacing: -0.5,
    color: '#001929',
    fontFamily: 'Inter',
    marginBottom: 10,
  },
  radioGroup: {},
  radioRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  radioRowWithAmount: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 16,
  },
  remainingAmount: {
    fontSize: 16,
    fontWeight: '500',
    color: '#335C75',
  },
  amountInput: {
    flex: 1,
    height: 40,
  },
  payButton: {
    width: '100%',
    height: 48,
    borderRadius: 8,
  },
})

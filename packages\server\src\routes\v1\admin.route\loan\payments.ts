import { Company, getPlan, LMS, LoanApplication } from '@linqpal/common-backend'
import moment from 'moment-timezone'
import { Request, Response } from 'express'
import { LOAN_APPLICATION_STATUS } from '@linqpal/models/src/dictionaries'
import { adminRequired } from '../../../../services/auth.service'

const loanPayments = {
  middlewares: {
    pre: [adminRequired],
  },
  async get(req: Request, res: Response) {
    const date = moment((req.query.date as string) || undefined).startOf('day')
    const payments = await LMS.getUpcomingLoan(date, 14)

    let items = await LoanApplication.aggregate([])
      .match({
        lms_id: { $in: payments.map((p) => p.id) },
        status: { $in: [LOAN_APPLICATION_STATUS.APPROVED] },
      })
      .addFields({ company_id: { $toObjectId: '$company_id' } })
      .lookup({
        from: Company.collection.name,
        localField: 'company_id',
        foreignField: '_id',
        as: 'company',
      })
      .unwind('$company')
      .addFields({ companyName: '$company.name' })
      .project({
        _id: 1,
        companyName: 1,
        issueDate: 1,
        approvedAmount: { $ifNull: ['$usedAmount', '$approvedAmount', 0] },
        nextPaymentAmount: { $ifNull: ['$nextPaymentAmount', '0'] },
        remainingAmount: { $ifNull: ['$remainingAmount', 0] },
        nextPaymentDate: { $ifNull: ['$nextPaymentDate', ''] },
        amountDue: { $ifNull: ['$amountDue', '0'] },
        lastPaymentDate: {
          $cond: {
            if: {
              $or: [
                { $eq: ['$lastPaymentDate', 'Invalid date'] },
                { $eq: ['$lastPaymentDate', null] },
              ],
            },
            then: '',
            else: '$lastPaymentDate',
          },
        },
        'invoiceDetails.paymentPlan': 1,
        status: 1,
        lms_id: 1,
        payment_cancelled: { $ifNull: ['$metadata.payment_cancelled', false] },
      })

    items = await Promise.all(
      items.map(async (item) => {
        const { id, ...rest } = payments.find((p) => p.id === item.lms_id)!
        return {
          ...item,
          loanOption:
            item.metadata?.paymentPlan ||
            (await getPlan(item.invoiceDetails?.paymentPlan).catch((e) =>
              console.log(e),
            )),
          ...rest,
        }
      }),
    )

    items.sort((a, b) =>
      moment(a.nextPaymentDate, 'YYYY-MM-DD').diff(
        moment(b.nextPaymentDate, 'YYYY-MM-DD'),
      ),
    )

    res.send({ items, total: items.length })
  },
}

export default loanPayments

export const toggleLoanAppPaymentCancelled = {
  middlewares: {
    pre: [adminRequired],
  },
  async post(req: Request, res: Response) {
    const app = await LoanApplication.findById(req.body.id)
    if (!app) {
      res.send({})
      return
    }
    if (app.metadata) {
      app.metadata.payment_cancelled = req.body.payment_cancelled
      app.metadata.payment_cancelled_by =
        req.user?.name || req.user?.login || ''
      app.metadata.payment_cancelled_date = moment().toDate()
      app.markModified('metadata')
    } else {
      app.metadata = {
        payment_cancelled: req.body.payment_cancelled,
        payment_cancelled_by: req.user?.name || req.user?.login || '',
        payment_cancelled_date: moment().toDate(),
      }
    }
    await app.save()
    res.send({})
  },
}

export const toggleLoanDisbursementPause = {
  middlewares: {
    pre: [adminRequired],
  },
  async post(req: Request, res: Response) {
    const app = await LoanApplication.findById(req.body.appId)

    if (app) {
      if (!app.metadata) app.metadata = {}
      if (!app.metadata.repayment) app.metadata.repayment = {}

      app.metadata.repayment.disbursementPause = {
        enabled: req.body.enabled,
        updatedBy: req.user?.name || req.user?.login || '',
      }

      await app.save()
    }

    res.send(app?.metadata?.repayment?.disbursementPause ?? {})
  },
}

export const toggleLoanSkipFinalPayment = {
  middlewares: {
    pre: [adminRequired],
  },
  async post(req: Request, res: Response) {
    const app = await LoanApplication.findById(req.body.id)
    if (!app) {
      res.send({})
      return
    }
    if (app.metadata) {
      app.metadata.skip_final_payment = req.body.skip_final_payment
      app.markModified('metadata')
    } else {
      app.metadata = { skip_final_payment: req.body.skip_final_payment }
    }
    await app.save()
    res.send({})
  },
}

export const toggleLoanAutoPaymentCollection = {
  middlewares: {
    pre: [adminRequired],
  },
  async post(req: Request, res: Response): Promise<void> {
    try {
      const { isAutoCollectionPaused, loanId } = req.body
      const userId = req.user!._id.toString()

      if (!loanId) {
        res.status(400).send({ error: 'Loan ID is required' })
        return
      }

      const result = await LMS.toggleLoanAutoPaymentCollection(
        loanId,
        isAutoCollectionPaused,
        userId,
      )

      res.send(result)
    } catch (error) {
      console.error('Failed to toggle auto collection:', error)
      res.status(500).send({ error: 'Failed to toggle auto collection' })
    }
  },
}

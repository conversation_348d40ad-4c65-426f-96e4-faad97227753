import React from 'react'
import { BankIcon, CardIconBuilder } from '../../../../../../../assets/icons'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react-lite'
import { ListItem } from '../../../../../../../ui/atoms/builder-2.0/ListItem'
import { EndIcon } from '../../../../../../../ui/add-payment-method-components/EndIcon'
import { Row } from '../../../../../../../ui/atoms/Grid'
import BuilderBottomModal from '../../../../../../../ui/molecules/BuilderBottomModal'
import { AddNewPaymentMethodHeader } from './components/AddNewPaymentMethodHeader'
import useIsMobile from '../../../../../PayablesTab/hooks/useIsMobile'

export enum AddNewPaymentMethodMode {
  AddFirstMethod = 'add_first',
  AddAdditionalMethod = 'add_additional',
}

interface AddNewPaymentMethodModalProps {
  onConnectBank: () => void
  onLinkCard: () => void
  onClose: () => void
  onBack?: () => void
  mode: AddNewPaymentMethodMode
}

export const AddNewPaymentMethodModal: React.FC<AddNewPaymentMethodModalProps> =
  observer(
    ({
      onConnectBank,
      onLinkCard,
      onClose,
      onBack,
      mode = AddNewPaymentMethodMode.AddFirstMethod,
    }) => {
      const { t } = useTranslation('global')
      const isMobile = useIsMobile()

      return (
        <BuilderBottomModal
          height={
            isMobile
              ? '100%'
              : mode === AddNewPaymentMethodMode.AddFirstMethod
              ? 380
              : 300
          }
          visible={true}
          testID={'add-new-payment-method-modal'}
          title={
            <AddNewPaymentMethodHeader
              mode={mode}
              onClose={onClose}
              onBack={onBack}
            />
          }
          paddingHorizontal={0}
          width={600}
        >
          <Row
            style={{
              marginHorizontal: 0,
              paddingHorizontal: isMobile ? 8 : 23,
            }}
            testID={'add-new-payment-method-row'}
          >
            <ListItem
              onPress={onConnectBank}
              icon={<BankIcon />}
              title={t('ConnectYourBank')}
              subtitle={t('ConnectionsAreEncrypted')}
              endIcon={<EndIcon />}
              testID={'connect-bank'}
              disabled={false}
            />
            <ListItem
              onPress={onLinkCard}
              icon={<CardIconBuilder />}
              title={t('LinkYourCard')}
              subtitle={t('CardDetailsAreTokenized')}
              endIcon={<EndIcon />}
              testID={'link-card'}
              disabled={false}
            />
          </Row>
        </BuilderBottomModal>
      )
    },
  )

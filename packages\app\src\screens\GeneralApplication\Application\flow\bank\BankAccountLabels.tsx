import React from 'react'
import { GetPaidNoneDescriptionLabel } from './components/GetPaidNoneLabels'
import { IHCNoneDescriptionLabel } from './components/IhcNoneLabels'

interface BankLabelProps {
  selectedBank?: any
  hasAccountAdded: boolean
}

interface BankLabels {
  title: string
  description: React.ReactElement | string
}
// Currently some translations are duplicated, but in fear of future changes, I suggest to keep them separate
export const GetPaidLabels = ({
  selectedBank,
  hasAccountAdded,
}: BankLabelProps): BankLabels => {
  if (hasAccountAdded) {
    return {
      title: 'Bank.GetPaid.Connected.Title',
      description: 'Bank.GetPaid.Connected.Description',
    }
  } else if (selectedBank) {
    return {
      title: 'Bank.GetPaid.Selected.Title',
      description: 'Bank.GetPaid.Selected.Description',
    }
  } else {
    return {
      title: 'Bank.GetPaid.None.Title',
      description: <GetPaidNoneDescriptionLabel />,
    }
  }
}

export const IHCLabels = ({
  selectedBank,
  hasAccountAdded,
}: BankLabelProps): BankLabels => {
  if (hasAccountAdded) {
    return {
      title: 'Bank.IHC.Connected.Title',
      description: 'Bank.IHC.Connected.Description',
    }
  } else if (selectedBank) {
    return {
      title: 'Bank.IHC.Selected.Title',
      description: 'Bank.IHC.Selected.Description',
    }
  } else {
    return {
      title: 'Bank.IHC.None.Title',
      description: <IHCNoneDescriptionLabel />,
    }
  }
}

export const LOCLabels = ({
  selectedBank,
  hasAccountAdded,
}: BankLabelProps): BankLabels => {
  if (hasAccountAdded) {
    return {
      title: 'Bank.LOC.Connected.Title',
      description: 'Bank.LOC.Connected.Description',
    }
  } else if (selectedBank) {
    return {
      title: 'Bank.LOC.Selected.Title',
      description: 'Bank.LOC.Selected.Description',
    }
  } else {
    return {
      title: 'Bank.LOC.None.Title',
      description: 'Bank.LOC.None.Description',
    }
  }
}

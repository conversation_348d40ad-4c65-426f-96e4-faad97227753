import React, { useState } from 'react'
import { StyleSheet, View } from 'react-native'

import BuilderBottomModal from '../../../../../../../ui/molecules/BuilderBottomModal'
import {
  AutoPayIHCMode,
  ModalContent,
  <PERSON>dal<PERSON>eader,
  ModalFooter,
} from './components'
import { Spinner } from '../../../../../../../ui/atoms/Spinner'
import { useTranslation } from 'react-i18next'
import useIsMobile from '../../../../../PayablesTab/hooks/useIsMobile'

export interface IChooseAutoPayIHCPaymentMethodProps {
  paymentMethods: any[]
  arePaymentMethodsLoading: boolean
  onAddNewPaymentMethod: () => void
  onSavePaymentMethod: (paymentMethod: any) => void
  onTurnOffAutoPay?: () => void
  onClose: () => void
  mode?: AutoPayIHCMode
  initialPaymentMethod?: any
  isSubmitting?: boolean
}

export const ChooseAutoPayIHCPaymentMethodBottomModal = ({
  paymentMethods = [],
  arePaymentMethodsLoading,
  onAddNewPaymentMethod,
  onSavePaymentMethod,
  onTurnOffAutoPay,
  onClose,
  mode = AutoPayIHCMode.Enable,
  initialPaymentMethod = null,
  isSubmitting = false,
}: IChooseAutoPayIHCPaymentMethodProps) => {
  const { t } = useTranslation('global')
  const isMobile = useIsMobile()

  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState(initialPaymentMethod)

  const handleSelectPaymentMethod = (paymentMethod: any) => {
    setSelectedPaymentMethod(paymentMethod)
  }

  const handleSavePaymentMethod = () => {
    if (selectedPaymentMethod) {
      onSavePaymentMethod(selectedPaymentMethod?.id.toString())
    }
  }

  const getTitleText = () => {
    switch (mode) {
      case AutoPayIHCMode.UpdateAutoPayNotRequired:
        return t('autoPayIHCFlow.updatePaymentMethod.title')
      case AutoPayIHCMode.UpdateAutoPayRequired:
        return t('autoPayIHCFlow.updatePaymentMethod.title')
      case AutoPayIHCMode.Enable:
      default:
        return t('autoPayIHCFlow.addOrSelectPaymentMethod.title')
    }
  }

  const getSubtitleText = () => {
    switch (mode) {
      case AutoPayIHCMode.UpdateAutoPayNotRequired:
        return t('autoPayIHCFlow.updatePaymentMethod.subtitle')
      case AutoPayIHCMode.UpdateAutoPayRequired:
        return t('autoPayIHCFlow.updatePaymentMethod.subtitle')
      case AutoPayIHCMode.Enable:
      default:
        return t('autoPayIHCFlow.addOrSelectPaymentMethod.subtitle')
    }
  }

  return (
    <BuilderBottomModal
      visible={true}
      title={
        arePaymentMethodsLoading ? undefined : (
          <ModalHeader
            title={getTitleText()}
            subtitle={getSubtitleText()}
            onClose={onClose}
            mode={mode}
          />
        )
      }
      footer={
        arePaymentMethodsLoading ? null : (
          <ModalFooter
            onSavePaymentMethod={handleSavePaymentMethod}
            onTurnOffAutoPay={onTurnOffAutoPay}
            hasSelectedPaymentMethod={!!selectedPaymentMethod}
            mode={mode}
            isSubmitting={isSubmitting}
            submitDisabled={
              selectedPaymentMethod?.id?.toString() ===
              initialPaymentMethod?.id?.toString()
            }
          />
        )
      }
      width={600}
      paddingHorizontal={0}
      height={isMobile ? '100%' : undefined}
    >
      {arePaymentMethodsLoading ? (
        <View
          style={{
            height: 380,
            width: '100%',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Spinner size="large" status="primary" style={styles.spinner} />
        </View>
      ) : (
        <ModalContent
          paymentMethods={paymentMethods}
          selectedPaymentMethod={selectedPaymentMethod}
          onSelectPaymentMethod={handleSelectPaymentMethod}
          onAddNewPaymentMethod={onAddNewPaymentMethod}
        />
      )}
    </BuilderBottomModal>
  )
}

const styles = StyleSheet.create({
  spinner: {
    alignSelf: 'center',
    zIndex: 200,
    marginTop: -20,
  },
  methodsContainer: {
    marginTop: 30,
    width: 350,
    alignSelf: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    lineHeight: 28,
    color: '#001929',
    marginBottom: 30,
    letterSpacing: 0.2,
    textAlign: 'center',
    fontFamily: 'Inter',
  },
})

import React from 'react'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { StyleSheet, Text, View } from 'react-native'
import { useStore } from '../../../../../store'
import { getPaymentMethodTitle } from '../../../../../ui/organisms/AutoPayIHCWarning/getPaymentMethodMetadata'
import StyledAlert from '../../../../../ui/molecules/StyledAlert'
import { Spacer } from '@linqpal/components/src/ui'
import useIsMobile from '../../../PayablesTab/hooks/useIsMobile'

export const AutoPayIHCEnabledAlert: React.FC<{
  onActionPress: () => void
}> = observer(({ onActionPress }) => {
  const { t } = useTranslation('global')
  const {
    userStore,
    screensStore: {
      paymentMethodsStore: { paymentMethods },
    },
  } = useStore()
  const isMobile = useIsMobile()

  const isAnyFactoringEnabled =
    userStore.suppliers?.some((suppl) => suppl.isFactoringEnabled) || false

  if (!isAnyFactoringEnabled) {
    return null
  }

  const inHouseSettings = userStore.company?.settings?.inHouseCredit
  const isAutoPayEnabled = inHouseSettings?.isAutoPayEnabledByCompanyUser
  const primaryIHCAutoPayMethod = paymentMethods.find(
    (method) => method.isPrimaryForIHCAutoPay,
  )

  if (!(isAutoPayEnabled && primaryIHCAutoPayMethod)) {
    return null
  }

  const renderTitle = (
    <View style={styles.titleContainer}>
      <Text style={[styles.texts, styles.title]}>
        {t('PaymentHistoryInvoice.autoPayIHCAlert.title') + ' '}
      </Text>
      <Text style={[styles.texts, styles.title, styles.onText]}>
        {t('PaymentHistoryInvoice.autoPayIHCAlert.on')}
      </Text>
    </View>
  )

  const renderMessage = (
    <View>
      <Text style={[styles.texts, styles.accountText]}>
        {getPaymentMethodTitle(primaryIHCAutoPayMethod, t)}
      </Text>
    </View>
  )

  return (
    <View
      style={{ width: isMobile ? '100%' : 350, marginLeft: isMobile ? 0 : 5 }}
    >
      <Spacer height={30} />
      <StyledAlert
        title={renderTitle}
        message={renderMessage}
        buttonText={t('PaymentHistoryInvoice.autoPayIHCAlert.button')}
        onActionPress={onActionPress}
        status="info"
        testID="ihc_invoice_autopay_enabled_banner"
        buttonStyle={{
          backgroundColor: 'transparent',
          height: 20,
          paddingHorizontal: 0,
        }}
        buttonTextStyle={{ color: '#00A0F3' }}
      />
      <Spacer height={5} />
    </View>
  )
})

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  title: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '700',
  },
  onText: {
    color: '#0EC06B',
  },
  accountText: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500',
  },
  texts: {
    fontFamily: 'Inter',
    color: '#001929',
    letterSpacing: -0.3,
  },
})

export default AutoPayIHCEnabledAlert

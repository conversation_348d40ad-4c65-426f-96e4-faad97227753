import {
  Company,
  connectToDatabase,
  emailService,
  getEnvironmentVariables,
  Invoice,
  LMS,
  LoanApplication,
  Logger,
  Operation,
  RemoteConfig,
  Settings,
} from '@linqpal/common-backend'
import moment from 'moment-timezone'
import 'moment-business-days'
import {
  IInvoice,
  ILoanApplication,
} from '@linqpal/common-backend/src/models/types'
import { dictionaries } from '@linqpal/models'
import {
  ILoan,
  LoanPaymentType,
  LoanProduct,
  LoanStatus,
} from '@linqpal/common-backend/src/services/lms.service'
import {
  LOAN_REPAYMENT_SCHEDULE_STATUS,
  LOAN_REPAYMENT_STATUS,
} from '@linqpal/models/src/dictionaries/loanStatuses'
import smsService from '@linqpal/common-backend/src/services/sms.service'
import EmailNotifications, {
  EmailNotification,
} from '@linqpal/common-backend/src/helpers/EmailNotifications'
import SmsNotifications from '@linqpal/common-backend/src/helpers/SmsNotifications'
import {
  DefaultSettings,
  OPERATION_STATUS,
  OPERATION_TYPES,
  SettingKeys,
} from '@linqpal/models/src/dictionaries'
import mongoose from 'mongoose'
import { CriticalError } from '@linqpal/models/src/types/exceptions'

const logger = new Logger({ module: 'LoanPro', subModule: 'closeLoan' })

export async function closeLoan() {
  await getEnvironmentVariables()
  await connectToDatabase()
  const log = logger.startTransaction()

  log.info('start closing loans process')

  const closeLoanDaysSetting = await Settings.findOne({
    key: SettingKeys.CloseLoanDays,
  })

  const closeLoanDays =
    closeLoanDaysSetting?.value ?? DefaultSettings.CloseLoanDays

  const [loans, autoPaymentDelay, manualPaymentDelay] = await Promise.all([
    LMS.findLoans({
      fromDate: moment()
        .startOf('day')
        .subtract(closeLoanDays, 'days')
        .toDate(),
      toDate: new Date(), // toDate is required when fromDate is specified
      loanStatus: LoanStatus.Started,
      detailed: true,
    }),
    RemoteConfig.getSetting(
      SettingKeys.CloseLoanDelayMinutesAfterAutoPayment,
      DefaultSettings.CloseLoanDelayMinutesAfterAutoPayment,
    ),
    RemoteConfig.getSetting(
      SettingKeys.CloseLoanDelayMinutesAfterManualPayment,
      DefaultSettings.CloseLoanDelayMinutesAfterManualPayment,
    ),
  ])

  for (const loan of loans) {
    // prettier-ignore
    if (![LoanProduct.LineOfCredit, LoanProduct.InHouseCredit].includes(loan.activeLoanTemplate.product)) {
      continue
    }

    const isFullyPaid = ensureLoanIsFullyPaid(loan, log)
    if (!isFullyPaid) continue

    // prettier-ignore
    log.info({ loanId: loan.id }, 'start processing loan')

    const invoiceIds =
      loan.loanPayables?.map((payable) => payable.payableId) || []

    const invoices = invoiceIds.length
      ? await Invoice.find({ _id: mongoose.trusted({ $in: invoiceIds }) })
      : []

    if (!invoices.length) {
      logger.warn({ loanId: loan.id }, 'no invoices found for loan')
      continue
    }

    switch (loan.activeLoanTemplate.product) {
      case LoanProduct.LineOfCredit:
        // prettier-ignore
        await tryCloseRegularLoan(loan, invoices, autoPaymentDelay, manualPaymentDelay, log)
        break
      case LoanProduct.InHouseCredit:
        // prettier-ignore
        await tryCloseFactoringLoan(loan, invoices, autoPaymentDelay, log)
        break
      default:
        throw new CriticalError(`unsupported product`, {
          loanId: loan.id,
          product: loan.activeLoanTemplate.product,
        })
    }

    log.info({ loanId: loan.id }, 'closeLoan completed')
  }
}

async function tryCloseRegularLoan(
  loan: ILoan,
  invoices: IInvoice[],
  autoPaymentDelay: number,
  manualPaymentDelay: number,
  log: Logger,
): Promise<void> {
  const app = await LoanApplication.findOne<ILoanApplication>({
    lms_id: loan.id,
  })

  log.info({ appId: app?._id, loanId: loan.id }, 'found loan application')

  if (!app) {
    log.warn(`no loan app found with lms_id ${loan.id}`)
    return
  }

  // prettier-ignore
  const isFinalPaymentSuccessful = await ensureRegularFinalPaymentIsSuccessful(app, loan, log)
  if (!isFinalPaymentSuccessful) return

  // prettier-ignore
  const isLastPaymentCleared = ensureLastPaymentIsCleared(loan, autoPaymentDelay, manualPaymentDelay, log)
  if (!isLastPaymentCleared) return

  if (app.status === dictionaries.LOAN_APPLICATION_STATUS.CLOSED) {
    if (loan.status !== 'Closed') {
      log.info({ loanId: loan.id }, 'sync closed loan app status with LMS')
      await LMS.closeLoan(loan.id)

      log.info({ loanId: loan.id }, 'closed loan in LMS')
    }

    return
  }

  log.info({ applicationId: app.id }, 'syncing loan application fields')
  await LoanApplication.updateOne(
    { lms_id: app.lms_id },
    {
      status: dictionaries.LOAN_APPLICATION_STATUS.CLOSED,
      lastPaymentDate: moment(loan?.lastPaymentDate).format('YYYY-MM-DD'),
      nextPaymentDate: null,
      nextPaymentAmount: null,
      remainingAmount: 0,
      processingAmount: 0,
      pastDueAmount: 0,
      amountDue: 0,
      closeDate: moment().toDate(),
    },
  )

  log.info({ applicationId: app.id }, 'closing loan in LMS')

  await LMS.closeLoan(loan.id)
  await sendRegularLoanClosedNotifications(loan, invoices, log)
}

async function tryCloseFactoringLoan(
  loan: ILoan,
  invoices: IInvoice[],
  paymentDelay: number,
  log: Logger,
): Promise<void> {
  const invoiceId = invoices[0]?.id

  if (!invoiceId) {
    log.warn({ loanId: loan.id }, 'no invoices found')
    return
  }

  const operation = await Operation.findOne({
    owner_id: invoiceId,
    type: OPERATION_TYPES.FACTORING.FINAL_PAYMENT,
    status: OPERATION_STATUS.SUCCESS,
  })

  if (!operation) {
    log.warn({ loanId: loan.id }, 'no successful final payment operation found')
    return
  }

  const lastPermittedMoment =
    process.env.LP_MODE === 'prod'
      ? moment().startOf('day').businessSubtract(paymentDelay, 'minutes').utc()
      : moment().businessSubtract(paymentDelay, 'minutes').utc()

  const finalPaymentMoment = moment.tz(operation.updatedAt, 'UTC') // operation is updated by .NET so UTC

  if (finalPaymentMoment.isAfter(lastPermittedMoment)) {
    log.info(
      {
        operation,
        finalPaymentDate: finalPaymentMoment.format(),
        lastPermittedDate: lastPermittedMoment.format(),
      },
      `final payment is not cleared yet, skipping loan ${loan.id}`,
    )

    return
  }

  log.info({ loanId: loan.id }, 'closing loan in LMS')
  await LMS.closeLoan(loan.id)
}

function ensureLoanIsFullyPaid(loan: ILoan, log: Logger) {
  const unpaidReceivables = loan.loanReceivables.filter(
    (r) =>
      r.status !== LOAN_REPAYMENT_STATUS.PAID &&
      r.status !== LOAN_REPAYMENT_STATUS.CANCELED &&
      r.status !== LOAN_REPAYMENT_STATUS.NONE &&
      r.scheduleStatus === LOAN_REPAYMENT_SCHEDULE_STATUS.CURRENT,
  )

  if (unpaidReceivables.length > 0) {
    log.warn(
      { unpaidReceivables: unpaidReceivables, lms_id: loan.id },
      'closeLoan: Loan can not be closed because it has unpaid receivables',
    )

    return false
  }

  return true
}

function ensureLastPaymentIsCleared(
  loan: ILoan,
  autoPaymentDelay: number,
  manualPaymentDelay: number,
  log: Logger,
) {
  if (loan.payments?.length) {
    // if latest payment is auto payment waiting for 2 days before loan closing to ensure payment is cleared
    // if latest payment was added manually in BO waiting 1 day, since it's instant
    // use minutes in settings for testing purpose, in prod they will normally represent full days

    const latestPayment = loan.payments.reduce((latest, current) => {
      return new Date(current.createdAt) > new Date(latest.createdAt)
        ? current
        : latest
    }, loan.payments[0])

    let paymentClearanceDelay: number

    if (latestPayment.type === LoanPaymentType.AutoDebit) {
      paymentClearanceDelay = autoPaymentDelay
    } else if (
      latestPayment.type === LoanPaymentType.Manual ||
      latestPayment.type === LoanPaymentType.Custom
    ) {
      paymentClearanceDelay = manualPaymentDelay
    } else {
      // for safety fallback to 7 days for payments with some future unknown types
      log.warn(
        { latestPayment, loanId: loan.id },
        `unknown payment type, waiting 7 days before closing loan ${loan.id}`,
      )

      paymentClearanceDelay = process.env.LP_MODE === 'prod' ? 60 * 24 * 7 : 0
    }

    const lastPermittedMoment =
      process.env.LP_MODE === 'prod'
        ? moment()
            .startOf('day')
            .businessSubtract(paymentClearanceDelay, 'minutes')
            .utc()
        : moment().businessSubtract(paymentClearanceDelay, 'minutes').utc()

    const lastPaymentMoment =
      process.env.LP_MODE === 'prod'
        ? moment.tz(loan.lastPaymentDate, 'UTC')
        : moment.tz(latestPayment.createdAt, 'UTC') // lastPaymentDate is date-only so for testing with short intervals use approx. payment time

    if (lastPaymentMoment.isAfter(lastPermittedMoment)) {
      log.info(
        {
          latestPayment,
          lastPaymentDate: lastPaymentMoment.format(),
          lastPermittedDate: lastPermittedMoment.format(),
        },
        `last payment is not cleared yet, skipping loan ${loan.id}`,
      )

      return false
    }

    log.info(
      {
        latestPayment,
        lastPaymentDate: lastPaymentMoment.format(),
        lastPermittedMoment: lastPermittedMoment.format(),
      },
      'last payment is cleared',
    )
  } else {
    log.warn({ loan }, `no payments found for loan ${loan.id}`)
  }

  return true
}

async function ensureRegularFinalPaymentIsSuccessful(
  app: ILoanApplication,
  loan: ILoan,
  log: Logger,
) {
  if (
    app.metadata?.loanPackage?.finalPayment &&
    app.metadata.loanPackage.finalPayment > 0 &&
    !app.metadata.skip_final_payment
  ) {
    const finalPayment = await Operation.findOne({
      owner_id: app.id,
      type: 'loan_final_issue',
    })

    if (
      !finalPayment ||
      finalPayment.status !== dictionaries.OPERATION_STATUS.SUCCESS
    ) {
      log.info(
        { appId: app?._id, loanId: loan.id },
        'closeLoan: Final payment not done skipping for now',
      )

      return false
    }
  }

  return true
}

async function sendRegularLoanClosedNotifications(
  loan: ILoan,
  invoices: IInvoice[],
  log: Logger,
) {
  log.info(
    `sending Draw Paid Off & Closed notifications for the loan ${loan.id}`,
  )

  const supplierId = invoices[0].company_id
  const supplier = supplierId ? await Company.findById(supplierId) : null // not sure if we can have here valid invoices without supplier, just replicated the legacy version

  let emailMessage: EmailNotification
  let smsMessage: string

  // prettier-ignore
  if (invoices.length === 1) {
    const params = {
      supplierName: supplier?.legalName || invoices[0].supplierInvitationDetails?.name || '',
      invoiceNumber: invoices[0].invoice_number,
      drawId: loan.id,
      totalPaid: loan.loanDetails?.totalPaid ?? 0,
      datePaidOff: moment(loan.lastPaymentDate)
    }

    emailMessage = EmailNotifications.drawPaidOffAndClosedForSingleInvoice(params)
    smsMessage = SmsNotifications.drawPaidOffAndClosedForSingleInvoice(params)
  } else {
    const params = {
      supplierName: supplier?.legalName || invoices[0].supplierInvitationDetails?.name || '',
      invoiceNumbers: invoices.map(invoice => invoice.invoice_number),
      drawId: loan.id,
      totalPaid: loan.loanDetails?.totalPaid ?? 0,
      datePaidOff: moment(loan.lastPaymentDate)
    }

      emailMessage = EmailNotifications.drawPaidOffAndClosedForGroupedInvoices(params)
      smsMessage = SmsNotifications.drawPaidOffAndClosedForGroupedInvoices(params)
  }

  // legacy setting for closeLoan only - still actual?
  const disableNotifications = await RemoteConfig.getConfig(
    SettingKeys.DisableNotifications,
  )

  if (!disableNotifications || disableNotifications === 'off') {
    await Promise.allSettled([
      emailService.sendToCompanyUsers(loan.companyId, emailMessage),
      smsService.smsUser(loan.companyId, smsMessage),
    ])
  }
}

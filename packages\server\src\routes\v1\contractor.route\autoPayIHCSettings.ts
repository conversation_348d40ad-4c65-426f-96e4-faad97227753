import { authRequired } from '../../../services/auth.service'
import { Company, bankAccountsService } from '@linqpal/common-backend'
import { ControllerItem } from 'src/routes/controllerItem'
import { Request, Response } from 'express'
import { exceptions } from '@linqpal/models'

export const saveAutoPayIHCPaymentMethod: ControllerItem = {
  middlewares: {
    pre: [authRequired()],
  },
  post: async (req: Request, res: Response) => {
    try {
      const { paymentMethodId } = req.body

      if (!req.company) {
        throw new exceptions.LogicalError('Company context is required')
      }

      const companyId = req.company._id.toString()

      if (!paymentMethodId) {
        throw new exceptions.LogicalError('Payment method ID is required')
      }

      const company = await Company.findById(companyId).populate('bankAccounts')
      if (!company) {
        throw new exceptions.LogicalError('Company not found')
      }

      const paymentMethod = company.bankAccounts?.find(
        (account) => account._id.toString() === paymentMethodId,
      )
      if (!paymentMethod) {
        throw new exceptions.LogicalError('Payment method not found')
      }

      await bankAccountsService.updatePrimaryForIHCAutoPay(
        companyId,
        paymentMethodId,
      )

      await Company.findByIdAndUpdate(
        companyId,
        {
          $set: {
            'settings.inHouseCredit.isAutoPayEnabledByCompanyUser': true,
          },
        },
        { new: true },
      )

      res.send({ result: 'ok' })
    } catch (error) {
      console.error('Error saving auto pay IHC payment method:', error)
      throw error
    }
  },
}

export const turnOffAutoPayIHC: ControllerItem = {
  middlewares: {
    pre: [authRequired()],
  },
  post: async (req: Request, res: Response) => {
    try {
      if (!req.company) {
        throw new exceptions.LogicalError('Company context is required')
      }

      const companyId = req.company._id.toString()

      const company = await Company.findById(companyId)

      if (!company) {
        throw new exceptions.LogicalError('Company not found')
      }

      await bankAccountsService.turnOffIHCAutoPay(companyId)

      await Company.findByIdAndUpdate(
        companyId,
        {
          $set: {
            'settings.inHouseCredit.isAutoPayEnabledByCompanyUser': false,
          },
        },
        { new: true },
      )

      res.send({ result: 'ok' })
    } catch (error) {
      console.error('Error turning off auto pay IHC:', error)
      throw error
    }
  },
}

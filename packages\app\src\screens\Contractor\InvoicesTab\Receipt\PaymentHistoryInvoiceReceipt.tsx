import React, { useCallback, useEffect, useState } from 'react'
import Wrapper from '../../Wrapper'
import { HeaderButton, Toolbar } from '../../../../ui/molecules/Layout'
import { Linking, StyleSheet, Text, View, TouchableOpacity } from 'react-native'
import Loading from '../../../../screens/Loading'
import { BackHeader, NewAttchmentIcon } from '../../../../assets/icons'
import { GetIcon } from '../../../../ui/atoms/GetIcon'
import { isWeb } from '../../../../utils/helpers/commonUtils'
import { routes } from '@linqpal/models'
import { useTranslation } from 'react-i18next'
import { useInvoiceDetailsContext } from '../../TabInvoice/InvoiceDetailsContext'
import { paths } from '../../../links'
import ReceiptStore from './ReceiptStore'
import { observer } from 'mobx-react-lite'
import { PaymentHistoryInvoiceDetails } from './components/PaymentHistoryInvoiceDetails'
import { BtText, Spacer } from '@linqpal/components/src/ui'
import { PaymentHistory } from './components/PaymentHistory'
import { PaymentSchedule } from './components/PaymentSchedule'
import useIsMobile from '../../PayablesTab/hooks/useIsMobile'
import { PaymentHistoryInvoiceDetailsBottomModal } from './modals/PaymentHistoryInvoiceDetailsModal'
import Button from '../../../IntegrationPayment/RegularIntegration/components/Button'
import {
  PaidInvoiceStatuses,
  PaymentProcessingInvoiceStatuses,
} from '../../PayablesTab/enums'
import { PaymentFlow } from './modals/InvoicePaymentFlow'
import { useStore } from '../../../../store'
import cookies from '../../../../utils/service/cooker'
import { SingleVendor, Vendors } from '../../PayablesTab/Store/VendorsStore'
import AutoPayIHCEnabledAlert from './components/AutoPayIHCEnabledAlert'
import { TabPayablesNavigator } from '../../PayablesTab/Components/TabPayablesNavigator'
import { InvoicePaymentType } from '@linqpal/models'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'

interface InvoiceReceiptProps {
  route: any
  navigation: any
}

export const ToolbarCenter = () => {
  const { t } = useTranslation('global')
  return (
    <Text style={styles.toolbarHeading}>
      {t('PaymentHistoryInvoice.details')}{' '}
    </Text>
  )
}

export const ToolbarRight = ({ invoice }) => {
  const [url, setUrl] = useState(undefined)
  useEffect(() => {
    if (invoice?.invoice_document) {
      routes.file
        .download(invoice.invoice_document)
        .then(({ url: u }) => setUrl(u))
    }
  }, [invoice])
  return invoice?.invoice_document ? (
    <GetIcon
      icon={<NewAttchmentIcon height="20" width="20" />}
      onPress={() => {
        if (isWeb) {
          window.open(url, '_blank')
        } else {
          Linking.openURL(url || '')
        }
      }}
    />
  ) : (
    <View style={{ width: 20 }} />
  )
}

export const ToolbarLeft = ({ navigation }) => {
  const onBackPress = () => {
    if (navigation.canGoBack()) {
      navigation.goBack()
    } else {
      navigation.replace(paths.Home._self, {
        screen: paths.Console.Payables._self,
        params: {
          screen: paths.Console.Payables.home,
        },
      })
    }
  }

  return (
    <HeaderButton
      icon={
        <GetIcon
          icon={<BackHeader height="20" width="20" />}
          onPress={onBackPress}
        />
      }
    />
  )
}

// Tab names for payment history and schedule
const TabNames = {
  PaymentHistory: 'PaymentHistory',
  PaymentSchedule: 'PaymentSchedule',
}

export const PaymentHistoryInvoiceReceipt: React.FC<InvoiceReceiptProps> =
  observer(({ route, navigation }) => {
    const { t } = useTranslation('global')
    const { id } = route.params
    const {
      getInvoice,
      getPaymentSchedule,
      loading,
      invoice,
      loanReceivables,
      paymentScheduleLoading,
    } = ReceiptStore
    const isMobile = useIsMobile()
    const [paymentFlow, setPaymentFlow] = useState<boolean>(false)
    const [isPaymentSubmitted, setIsPaymentSubmitted] = useState(false)
    const store = useStore()
    const vendorsStore = Vendors
    const singleVendorStore = SingleVendor

    const isUserAuthenticated = isWeb
      ? !!cookies.get('session')
      : store.userStore.isAuthenticated

    const { setPaymentHistoryInvoiceDetailsModalVisibility } =
      useInvoiceDetailsContext()

    useEffect(() => {
      getInvoice(id)
    }, [id, getInvoice])

    // Fetch payment schedule when invoice is factoring and approved
    useEffect(() => {
      if (isFactoringApproved && id) {
        getPaymentSchedule(id)
      }
    }, [isFactoringApproved, id, getPaymentSchedule])

    const showMoreInvoiceDetails = useCallback(() => {
      setPaymentHistoryInvoiceDetailsModalVisibility(true)
    }, [setPaymentHistoryInvoiceDetailsModalVisibility])
    const transactions = invoice?.operation?.allTransactions || []

    // Check if invoice is factoring and approved
    const isFactoringApproved =
      invoice?.paymentDetails?.paymentType === InvoicePaymentType.FACTORING &&
      invoice?.paymentDetails?.arAdvanceStatus === ArAdvanceStatus.Approved

    const startPaymentFlow = useCallback(() => {
      setPaymentFlow(true)
    }, [])

    const stopPaymentFlow = useCallback(() => {
      setPaymentFlow(false)
    }, [])

    const handlePaymentSuccess = useCallback(() => {
      setIsPaymentSubmitted(true)
      setPaymentFlow(false)
      setTimeout(() => {
        getInvoice(id)
        setIsPaymentSubmitted(false)
      }, 1000)
      setTimeout(() => {
        if (singleVendorStore.vendorId) {
          singleVendorStore.refreshData()
        }
        vendorsStore.refreshData()
      }, 1500)
    }, [id, getInvoice, vendorsStore, singleVendorStore])

    const handleManageAutoPayPress = useCallback(() => {
      if (isMobile) {
        navigation.navigate(paths.Console.More._self, {
          screen: paths.Console.More.Wallet,
        })
      } else {
        navigation.navigate(paths.Console.Accounts._self)
      }
    }, [navigation, isMobile])

    if (!invoice || loading) {
      return <Loading />
    }

    const cardPaymentAvailable =
      invoice.company.settings?.cardPricingPackageId !== 'optOut'

    const acceptAchPayment =
      invoice.company?.settings?.acceptAchPayment !== false &&
      invoice.customer?.settings?.acceptAchPayment !== false

    const isInvoicePaid =
      PaidInvoiceStatuses.includes(invoice.status) ||
      PaymentProcessingInvoiceStatuses.includes(invoice.status)

    return (
      <Wrapper
        styleWidth={isMobile ? '100%' : 375}
        toolbar={
          <Toolbar
            centerComponent={<ToolbarCenter />}
            leftComponent={
              isUserAuthenticated ? (
                <ToolbarLeft navigation={navigation} />
              ) : (
                <View />
              )
            }
            rightComponent={<ToolbarRight invoice={invoice} />}
            containerStyle={[
              {
                borderBottomWidth: 1,
                borderBottomColor: '#E6EBEE',
                height: 60,
              },
              isMobile ? styles.mobile : styles.desktop,
            ]}
          />
        }
        contentContainerStyle={
          isMobile ? styles.mobile : { width: 375, paddingHorizontal: 10 }
        }
        containerStyle={isMobile ? {} : { maxHeight: 800 }}
        footer={
          !isInvoicePaid &&
          !!invoice.totalRemainingAmount && (
            <View
              style={[
                styles.buttonContainer,
                isMobile
                  ? {
                      width: '100%',
                      marginHorizontal: 0,
                      paddingHorizontal: 20,
                    }
                  : styles.desktop,
              ]}
            >
              <Button
                onPress={startPaymentFlow}
                disabled={
                  (!cardPaymentAvailable && !acceptAchPayment) ||
                  isPaymentSubmitted
                }
                label={t('PaymentHistoryInvoice.makePayment')}
              />
            </View>
          )
        }
      >
        <PaymentHistoryInvoiceDetails />

        <Spacer height={5} />

        <ViewMoreDetails onPress={showMoreInvoiceDetails} />

        <AutoPayIHCEnabledAlert onActionPress={handleManageAutoPayPress} />

        <Spacer height={20} />

        {!!transactions.length && !isFactoringApproved && (
          <PaymentHistory
            transactions={transactions}
            totalAmount={invoice.total_amount + invoice.lateFee}
          />
        )}

        {isFactoringApproved && (
          <TabPayablesNavigator
            initialTab={TabNames.PaymentHistory}
            styles={{
              indicatorStyle: {
                backgroundColor: '#00A0F3',
                opacity: 1,
              },
              tabStyle: {
                width: 'auto',
                paddingHorizontal: 20,
              },
              labelStyle: {
                textTransform: 'none',
                fontWeight: '500',
                fontSize: 14,
                lineHeight: 24,
                fontFamily: 'Inter, sans-serif',
              },
              activeTintColor: '#00A0F3',
              inactiveTintColor: '#394D5A',
              style: {
                marginBottom: 24,
              },
            }}
            tabs={[
              {
                name: TabNames.PaymentHistory,
                options: {
                  title: t('PaymentHistoryInvoice.paymentHistory'),
                },
                component: () => (
                  <PaymentHistory
                    transactions={transactions}
                    totalAmount={invoice.total_amount + invoice.lateFee}
                  />
                ),
              },
              {
                name: TabNames.PaymentSchedule,
                options: {
                  title: t('PaymentHistoryInvoice.paymentsSchedule'),
                },
                component: () => (
                  <PaymentSchedule
                    loanReceivables={loanReceivables}
                    isMobile={isMobile}
                    loading={paymentScheduleLoading}
                  />
                ),
              },
            ]}
          />
        )}

        <PaymentHistoryInvoiceDetailsBottomModal invoice={invoice} />

        {!!paymentFlow && (
          <PaymentFlow
            invoice={invoice}
            onClose={stopPaymentFlow}
            acceptAchPayment={acceptAchPayment}
            cardPaymentAvailable={cardPaymentAvailable}
            navigation={navigation}
            onSuccess={handlePaymentSuccess}
          />
        )}
      </Wrapper>
    )
  })

interface IViewMoreDetailsProps {
  onPress: () => void
}

const ViewMoreDetails: React.FC<IViewMoreDetailsProps> = ({ onPress }) => {
  const { t } = useTranslation('global')
  return (
    <View style={styles.viewMoreDetailsContainer}>
      <TouchableOpacity onPress={onPress} style={{ width: 160 }}>
        <BtText style={styles.viewMoreDetailsText}>
          {t('PaymentHistoryInvoice.viewMoreDetails')}
        </BtText>
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  toolbarHeading: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 24,
    fontFamily: 'Inter',
    color: '#001929',
  },
  viewMoreDetailsContainer: {
    display: 'flex',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    paddingLeft: 5,
  },
  viewMoreDetailsText: {
    fontSize: 14,
    fontWeight: '700',
    lineHeight: 21,
    fontFamily: 'Inter',
    color: '#00A0F3',
  },
  desktop: {
    width: 375,
    paddingHorizontal: 15,
  },
  mobile: {
    width: '100%',
  },
  buttonContainer: {
    height: 80,
    paddingVertical: 10,
    borderTopColor: '#E6EBEE',
    borderTopWidth: 1,
    marginLeft: 18,
    justifyContent: 'center',
  },
})

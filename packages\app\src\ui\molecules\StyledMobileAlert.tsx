import React, { ReactNode } from 'react'
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  StyleProp,
  TextStyle,
  ViewStyle,
} from 'react-native'
import IconSmallClose from '../../assets/icons/IconSmallClose'
import Button from '../../screens/IntegrationPayment/RegularIntegration/components/Button'
import { Spinner } from '../atoms/Spinner'

export type AlertStatus = 'error' | 'info'

interface StyledAlertProps {
  icon: ReactNode
  title: ReactNode | string
  message: ReactNode | string
  buttonText: string
  onActionPress: () => void
  onClose?: () => void
  status: AlertStatus
  testID?: string
  buttonStyle?: StyleProp<ViewStyle>
  buttonTextStyle?: StyleProp<TextStyle>
  loading?: boolean
}

export const StyledMobileAlert: React.FC<StyledAlertProps> = ({
  icon,
  title,
  message,
  buttonText,
  onActionPress,
  onClose,
  status,
  testID,
  buttonStyle,
  buttonTextStyle,
  loading = false,
}) => {
  const containerStyle = [
    styles.container,
    status === 'error' ? styles.errorContainer : styles.infoContainer,
  ]

  if (loading) {
    return (
      <View
        style={containerStyle}
        testID={testID || `styled_mobile_alert_${status}_loading`}
      >
        <View style={styles.loadingContainer}>
          <Spinner size="small" status="primary" />
        </View>
      </View>
    )
  }

  return (
    <View style={containerStyle} testID={testID || `styled_alert_${status}`}>
      <View style={styles.iconContainer}>{icon}</View>
      <View style={styles.textContainer}>
        {typeof title === 'string' ? (
          <Text style={styles.title}>{title}</Text>
        ) : (
          title
        )}{' '}
        {typeof message === 'string' ? (
          <Text style={styles.message}>{message}</Text>
        ) : (
          message
        )}
        <Button
          onPress={onActionPress}
          testID={`${testID}_action_button` || `${status}_action_button`}
          textStyle={[styles.buttonText, buttonTextStyle]}
          buttonStyle={[styles.button, buttonStyle]}
          label={buttonText}
        />
      </View>

      {onClose && (
        <TouchableOpacity
          style={styles.closeButtonContainer}
          onPress={onClose}
          testID={`${testID}_close_button` || `${status}_close_button`}
        >
          <IconSmallClose color="#00A0F3" width={18} height={18} />
        </TouchableOpacity>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    border: 'none',
    padding: 16,
    borderWidth: 1,
  },
  errorContainer: {
    backgroundColor: '#FFF2F4',
  },
  infoContainer: {
    backgroundColor: '#EFF8FE',
    borderColor: '#CCE8FC',
  },
  iconContainer: {
    marginRight: 12,
    marginTop: 2,
    alignSelf: 'flex-start',
  },
  textContainer: {
    flex: 1,
    marginRight: 16,
  },
  title: {
    fontFamily: 'Inter',
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '700',
    color: '#001929',
    marginBottom: 4,
    letterSpacing: -0.3,
  },
  message: {
    maxWidth: 840,
    fontFamily: 'Inter',
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
    letterSpacing: -0.3,
    color: '#001929',
  },
  buttonText: {
    textAlign: 'center',
    fontWeight: '700',
    fontSize: 14,
    lineHeight: 24,
    fontFamily: 'Inter',
  },
  button: {
    alignSelf: 'flex-start',
    paddingVertical: 6,
    paddingHorizontal: 20,
    borderRadius: 8,
    height: 36,
    marginTop: 12,
  },
  closeButtonContainer: {
    marginLeft: 12,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start',
  },
  closeButton: {
    width: 24,
    height: 24,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    paddingVertical: 8,
  },
  loadingText: {
    fontFamily: 'Inter',
    fontSize: 14,
    fontWeight: '500',
    color: '#668598',
    marginLeft: 12,
  },
})

export default StyledMobileAlert

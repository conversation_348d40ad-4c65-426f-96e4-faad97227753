import React, { memo, useCallback, useState } from 'react'
import { StyleSheet, View } from 'react-native'
import { useTranslation } from 'react-i18next'
import BuilderBottomModal from '../../../../../ui/molecules/BuilderBottomModal'
import Loading from '../../../../Loading'
import { PayFactoringInvoiceModalContent } from './ModalContent'
import { IInvoiceTotal, PaymentTypeOptions } from './types'
import { BButton } from '../../../../../ui/atoms/builder-2.0/Button'
import { Spacer } from '@linqpal/components/src/ui'
import { Header } from './components/Header'
import useIsMobile from '../../../../Contractor/PayablesTab/hooks/useIsMobile'
import { currencyMask } from '../../../../../utils/helpers/masking'

const Footer = ({
  onAgree,
  loading = false,
  disabled = false,
  totalAmount = 0,
}) => {
  const { t } = useTranslation(['global'])
  const isMobile = useIsMobile()
  return (
    <>
      <View
        style={{
          backgroundColor: '#FFF',
          width: '100%',
          alignItems: 'center',
          paddingHorizontal: 15,
        }}
      >
        <View
          style={{
            width: '100%',
            flexDirection: 'row',
            justifyContent: isMobile ? 'center' : 'flex-end',
          }}
        >
          <BButton
            onPress={onAgree}
            buttonStyle={{ minWidth: isMobile ? 335 : 'auto' }}
            loading={loading}
            disabled={disabled}
          >
            {t('global:payIhcInvoice.agree-and-pay', {
              amount: currencyMask(totalAmount),
            })}
          </BButton>
        </View>
        <Spacer height={22} />
      </View>
    </>
  )
}

interface PayWithPaymentMethodModalProps {
  invoiceTotal?: IInvoiceTotal
  invoice: any
  loading: boolean
  disabled?: boolean
  onPay: (currentCalculatedDetails?: IInvoiceTotal) => void
  onClose: () => void
  paymentMethod: any
  handleChangePaymentMethod: () => void
  handleAddNewPaymentMethod: () => void
}

export const PayFactoringInvoiceModal: React.FC<PayWithPaymentMethodModalProps> =
  memo(
    ({
      loading,
      invoiceTotal,
      invoice,
      disabled = false,
      onPay,
      onClose,
      paymentMethod,
      handleChangePaymentMethod,
      handleAddNewPaymentMethod,
    }) => {
      const { t } = useTranslation(['errors', 'global'])
      const [currentCalculatedDetails, setCurrentCalculatedDetails] =
        useState(invoiceTotal)

      const handlePayment = useCallback(() => {
        const requestedAmount = Number(currentCalculatedDetails?.invoiceAmount)
        // TODO: calculate total amount in one place
        const totalAmount =
          Number(invoiceTotal?.invoiceAmount || 0) +
          Number(invoiceTotal?.lateFee || 0) +
          Number(invoiceTotal?.customerFee || 0)

        if (
          requestedAmount < totalAmount &&
          currentCalculatedDetails?.paymentType === PaymentTypeOptions.Partial
        ) {
          onPay(currentCalculatedDetails)
        } else {
          onPay()
        }
      }, [currentCalculatedDetails, invoiceTotal, onPay])

      return (
        <BuilderBottomModal
          visible={true}
          title={
            <Header
              title={t('global:payIhcInvoice.makePayment')}
              onClose={onClose}
            />
          }
          height={700}
          width={530}
          footer={
            <Footer
              onAgree={handlePayment}
              disabled={
                disabled ||
                loading ||
                !Number(currentCalculatedDetails?.totalAmountWithFee || 0)
              }
              loading={loading}
              totalAmount={currentCalculatedDetails?.totalAmountWithFee}
            />
          }
          testID={'pay-ihc-invoice-with-payment-method-modal'}
        >
          {loading || !invoiceTotal ? (
            <View style={styles.loaderContainer}>
              <Loading />
            </View>
          ) : (
            <PayFactoringInvoiceModalContent
              invoiceTotal={invoiceTotal}
              invoice={invoice}
              paymentMethod={paymentMethod}
              handleChangePaymentMethod={handleChangePaymentMethod}
              handleAddNewPaymentMethod={handleAddNewPaymentMethod}
              onCalculatedDetailsChange={setCurrentCalculatedDetails}
            />
          )}
        </BuilderBottomModal>
      )
    },
  )

const styles = StyleSheet.create({
  loaderContainer: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
})

import React from 'react'
import { StyleSheet, Text, View } from 'react-native'
import { observer } from 'mobx-react'
import { IconCheckCircleOutlined } from '../../../assets/icons'
import StyledAlert from '../../../ui/molecules/StyledAlert'
import StyledMobileAlert from '../../../ui/molecules/StyledMobileAlert'
import { useTranslation } from 'react-i18next'
import { getPaymentMethodTitle } from './getPaymentMethodMetadata'
import { useNavigation } from '@react-navigation/native'
import { useResponsive } from '@linqpal/components/src/hooks'
import useIsMobile from '../../../screens/Contractor/PayablesTab/hooks/useIsMobile'
import { paths } from '../../../screens/links'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'
import { calculateProcessingFee } from '../../../screens/Contractor/MoreTab/Wallet/AutoPayIHCFlow/modals/ChooseAutoPayIHCPaymentMethod/utils/calculateProcessingFee'

export enum AutoPayIHCEnabledWarningOrigin {
  Payables = 'payables',
  Account = 'account',
}

interface AutoPayIHCEnabledWarningProps {
  primaryPaymentMethod: any
  onActionPress: () => void
  origin?: AutoPayIHCEnabledWarningOrigin
  loading?: boolean
}

export const AutoPayIHCEnabledWarning: React.FC<AutoPayIHCEnabledWarningProps> =
  observer(
    ({
      onActionPress,
      primaryPaymentMethod,
      origin = AutoPayIHCEnabledWarningOrigin.Payables,
      loading,
    }) => {
      const { t } = useTranslation('global')
      const navigation = useNavigation()
      const { sm } = useResponsive()
      const isMobile = useIsMobile()

      const Alert = isMobile ? StyledMobileAlert : StyledAlert

      const handleActionPress = () => {
        if (origin === AutoPayIHCEnabledWarningOrigin.Account) {
          onActionPress()
        } else {
          if (sm) {
            navigation.navigate(paths.Console.Accounts._self)
          } else {
            navigation.navigate(paths.Console.More._self, {
              screen: paths.Console.More.Wallet,
            })
          }
        }
      }

      const renderTitle = (
        <View style={styles.titleContainer}>
          <Text
            style={[
              styles.texts,
              styles.title,
              isMobile && { fontSize: 14, lineHeight: 20 },
            ]}
          >
            {t('autoPayWarning.enabled.title') + ' '}
          </Text>
          <Text style={[styles.texts, styles.title, styles.onText]}>
            {t('autoPayWarning.enabled.on')}
          </Text>
        </View>
      )

      const processingFeePercentage =
        calculateProcessingFee(primaryPaymentMethod)

      const renderMessage = (
        <View>
          <Text style={[styles.texts, styles.message]}>
            {t('autoPayWarning.enabled.message') + ' '}
            <Text style={[styles.texts, styles.message, styles.accountText]}>
              {getPaymentMethodTitle(primaryPaymentMethod, t)}
            </Text>
          </Text>
        </View>
      )

      return (
        <View>
          <Alert
            icon={<IconCheckCircleOutlined width={20} height={20} />}
            title={renderTitle}
            message={renderMessage}
            buttonText={
              origin === AutoPayIHCEnabledWarningOrigin.Payables
                ? t('autoPayWarning.enabled.buttonTextPayables')
                : t('autoPayWarning.enabled.buttonTextAccount')
            }
            onActionPress={handleActionPress}
            status="info"
            testID="ihc_autopay_enabled_banner"
            loading={loading}
            buttonStyle={
              origin === AutoPayIHCEnabledWarningOrigin.Payables
                ? composeStyle(
                    { backgroundColor: 'transparent' },
                    isMobile
                      ? {
                          marginLeft: -20,
                          height: 25,
                        }
                      : { paddingHorizontal: 0 },
                  )
                : undefined
            }
            buttonTextStyle={
              origin === AutoPayIHCEnabledWarningOrigin.Payables
                ? { color: '#00A0F3' }
                : undefined
            }
          />
          {origin === AutoPayIHCEnabledWarningOrigin.Account &&
            !!processingFeePercentage &&
            !loading && (
              <Text style={[styles.texts, styles.processingFeeText]}>
                {'*' +
                  t('autoPayIHCFlow.processingFeeNote', {
                    processingFee: processingFeePercentage,
                  })}
              </Text>
            )}
        </View>
      )
    },
  )

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  title: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '700',
  },
  onText: {
    color: '#0EC06B',
  },
  message: {
    maxWidth: 840,
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500',
  },
  accountText: {
    fontWeight: '700',
  },
  processingFeeText: {
    marginTop: 16,
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500',
    color: '#00A0F3',
    letterSpacing: 0,
  },
  texts: {
    fontFamily: 'Inter',
    color: '#001929',
    letterSpacing: -0.3,
  },
})

export default AutoPayIHCEnabledWarning

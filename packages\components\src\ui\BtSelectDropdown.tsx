import React, { useState, useRef, useEffect } from 'react'
import {
  StyleSheet,
  TouchableOpacity,
  View,
  FlatList,
  ViewStyle,
  TextStyle,
  Platform,
} from 'react-native'
import { BtText } from './BtText'
import { BtDropdownArrow } from './icons/BtDropdownArrow'

// Import ReactDOM for web portal functionality
let Portal: any = null
if (Platform.OS === 'web') {
  try {
    const ReactDOM = require('react-dom')
    Portal = ReactDOM.createPortal
  } catch (e) {
    // Fallback if ReactDOM is not available
    Portal = null
  }
}

export interface BtSelectDropdownProps<T = any> {
  data: T[]
  selectedItem?: T | null
  onSelect: (item: T) => void
  renderItem?: (params: {
    item: T
    isSelected: boolean
    onPress: () => void
  }) => React.ReactElement
  renderSelectedItem?: (item: T | null) => string | React.ReactElement
  placeholder?: string
  label?: string
  keyExtractor?: (item: T, index: number) => string
  isItemSelected?: (item: T, selectedItem: T | null) => boolean
  maxDropdownHeight?: number
  scrollEnabled?: boolean
  disabled?: boolean
  style?: ViewStyle
  dropdownButtonStyle?: ViewStyle
  dropdownContainerStyle?: ViewStyle
  selectedTextStyle?: TextStyle
  placeholderTextStyle?: TextStyle
  labelStyle?: TextStyle
  arrowColor?: string
}

export const BtSelectDropdown = <T,>({
  data,
  selectedItem = null,
  onSelect,
  renderItem,
  renderSelectedItem,
  placeholder = '',
  label,
  keyExtractor,
  isItemSelected,
  maxDropdownHeight = 200,
  scrollEnabled,
  disabled = false,
  style,
  dropdownButtonStyle,
  dropdownContainerStyle,
  selectedTextStyle,
  placeholderTextStyle,
  labelStyle,
  arrowColor = '#19262F',
}: BtSelectDropdownProps<T>) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [dropdownPosition, setDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
  })
  const dropdownButtonRef = useRef<any>(null)
  const dropdownContainerRef = useRef<any>(null)
  const isSelectingItem = useRef(false)

  const toggleDropdown = () => {
    if (!disabled) {
      setIsDropdownOpen(!isDropdownOpen)

      // Calculate position for portal on web
      if (
        !isDropdownOpen &&
        Platform.OS === 'web' &&
        Portal &&
        dropdownButtonRef.current
      ) {
        const rect = dropdownButtonRef.current.getBoundingClientRect()
        setDropdownPosition({
          top: rect.bottom + window.scrollY,
          left: rect.left + window.scrollX,
          width: rect.width,
        })
      }
    }
  }
  const handleSelectItem = (item: T) => {
    isSelectingItem.current = true
    onSelect(item)
    setIsDropdownOpen(false)
    // Reset the flag after a small delay
    setTimeout(() => {
      isSelectingItem.current = false
    }, 100)
  }
  // Close dropdown when clicking outside
  useEffect(() => {
    if (Platform.OS === 'web') {
      const handleClickOutside = (event: any) => {
        // Don't close if currently selecting an item
        if (isSelectingItem.current) {
          return
        }

        // Use setTimeout to allow item selection to complete first
        setTimeout(() => {
          if (
            dropdownButtonRef.current &&
            !dropdownButtonRef.current.contains(event.target) &&
            dropdownContainerRef.current &&
            !dropdownContainerRef.current.contains(event.target)
          ) {
            setIsDropdownOpen(false)
          }
        }, 50)
      }

      if (isDropdownOpen) {
        document.addEventListener('mousedown', handleClickOutside)
        return () => {
          document.removeEventListener('mousedown', handleClickOutside)
        }
      }
    }
    return undefined
  }, [isDropdownOpen])

  const defaultIsItemSelected = (item: T, selected: T | null) => {
    if (!selected || !item) return false
    // Try to compare by id first, then by reference
    if (
      typeof item === 'object' &&
      item !== null &&
      'id' in item &&
      typeof selected === 'object' &&
      selected !== null &&
      'id' in selected
    ) {
      return (item as any).id === (selected as any).id
    }
    return item === selected
  }

  const checkIsSelected = isItemSelected || defaultIsItemSelected

  const defaultKeyExtractor = (item: T, index: number) => {
    if (typeof item === 'object' && item !== null && 'id' in item) {
      return String((item as any).id)
    }
    return `dropdown-item-${index}`
  }

  const getKeyExtractor = keyExtractor || defaultKeyExtractor

  const defaultRenderItem = ({ item }: { item: T }) => {
    const isSelected = checkIsSelected(item, selectedItem)

    return (
      <TouchableOpacity
        style={[styles.dropdownItem, isSelected && styles.activeDropdownItem]}
        onPress={() => handleSelectItem(item)}
      >
        <BtText
          style={[
            styles.dropdownItemText,
            isSelected && styles.activeDropdownItemText,
          ]}
        >
          {String(item)}
        </BtText>
      </TouchableOpacity>
    )
  }

  const defaultRenderSelectedItem = (item: T | null) => {
    if (!item) return placeholder
    return String(item)
  }

  const getSelectedText = renderSelectedItem || defaultRenderSelectedItem
  const itemRenderer = renderItem || defaultRenderItem

  const flatListRenderItem = ({ item }: { item: T }) => {
    const isSelected = checkIsSelected(item, selectedItem)
    return itemRenderer({
      item,
      isSelected,
      onPress: () => handleSelectItem(item),
    })
  }

  const shouldScroll =
    scrollEnabled !== undefined ? scrollEnabled : data.length > 4
  // Create dropdown content
  const dropdownContent = isDropdownOpen && (
    <View
      ref={dropdownContainerRef}
      data-dropdown-container="true"
      style={[
        styles.dropdownContainer,
        { maxHeight: maxDropdownHeight },
        dropdownContainerStyle,
        Platform.OS === 'web' && Portal
          ? ({
              position: 'fixed' as any,
              top: dropdownPosition.top,
              left: dropdownPosition.left,
              width: dropdownPosition.width,
              right: 'auto',
            } as ViewStyle)
          : {},
      ]}
    >
      <FlatList
        data={data}
        renderItem={flatListRenderItem}
        keyExtractor={getKeyExtractor}
        style={styles.dropdownList}
        contentContainerStyle={styles.dropdownListContent}
        scrollEnabled={shouldScroll}
        showsVerticalScrollIndicator={shouldScroll}
      />
    </View>
  )

  return (
    <View style={[styles.container, style]}>
      {label && <BtText style={[styles.label, labelStyle]}>{label}</BtText>}

      <TouchableOpacity
        ref={dropdownButtonRef}
        style={[
          styles.dropdownButton,
          isDropdownOpen && styles.activeDropdownButton,
          disabled && styles.disabledDropdownButton,
          dropdownButtonStyle,
        ]}
        onPress={toggleDropdown}
        disabled={disabled}
      >
        <BtText
          style={[
            styles.selectedText,
            !selectedItem && styles.placeholderText,
            !selectedItem && placeholderTextStyle,
            selectedItem && selectedTextStyle,
          ]}
        >
          {getSelectedText(selectedItem)}
        </BtText>
        <BtDropdownArrow
          width={24}
          height={24}
          color={arrowColor}
          isOpen={isDropdownOpen}
        />
      </TouchableOpacity>

      {/* Render dropdown content */}
      {Platform.OS === 'web' && Portal && typeof document !== 'undefined'
        ? Portal(dropdownContent, document.body)
        : dropdownContent}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1000,
    // Ensure the container can extend beyond its bounds when needed
    overflow: 'visible',
  },
  label: {
    fontSize: 12,
    lineHeight: 16,
    color: '#001929',
    marginBottom: 6,
    fontWeight: '400',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 13,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#99ADBA',
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
    minHeight: 48,
    marginBottom: 5,
  },
  activeDropdownButton: {
    borderWidth: 1,
  },
  disabledDropdownButton: {
    backgroundColor: '#F9FAFB',
    borderColor: '#E5E7EB',
  },
  selectedText: {
    color: '#374151',
    fontSize: 16,
    fontWeight: '400',
    flex: 1,
  },
  placeholderText: {
    color: '#9CA3AF',
  },
  dropdownContainer: {
    position: 'absolute',
    paddingHorizontal: 5,
    paddingVertical: 8,
    top: '100%',
    left: 0,
    right: 0,
    borderWidth: 1,
    borderColor: '#DEE5EB',
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    shadowColor: 'rgba(22, 34, 51, 0.08)',
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 3,
    zIndex: 1001,
    // Add these properties to prevent clipping in scroll views
    overflow: 'visible',
    ...(Platform.OS === 'web' && {
      // For web, ensure dropdown appears above other elements
      position: 'fixed' as any,
    }),
  },
  dropdownList: {
    width: '100%',
    borderRadius: 4,
  },
  dropdownListContent: {
    paddingVertical: 0,
  },
  dropdownItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    backgroundColor: '#FFFFFF',
  },
  activeDropdownItem: {
    backgroundColor: '#EFF6FF',
  },
  dropdownItemText: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '400',
  },
  activeDropdownItemText: {
    color: '#3B82F6',
    fontWeight: '500',
  },
})

import { IBankAccount, ILoanApplication } from '../../models/types'
import { Company } from '../../models'
import { makeLoanPayment } from '../cbw'
import {
  cancelPayment,
  performPayment,
  syncLoanApplicationFields,
} from '../lms.service'
import { Logger } from '../logger/logger.service'
import mongoose from 'mongoose'
import { exceptions } from '@linqpal/models'
import {
  getSourceBankAccount,
  isPaymentMethodCard,
} from '../bankAccounts/helper'

export const initializeRepayment = async (
  {
    application,
    amountDue,
    paymentDate,
    logger,
    bankAccount,
    cardToken,
    firstName,
    lastName,
    phoneNumber,
    isAutoPay,
    isAutoDebit,
  }: {
    application: ILoanApplication
    amountDue: number
    paymentDate: string | Date
    logger?: Logger
    bankAccount?: Partial<IBankAccount>
    cardToken?: string
    firstName?: string
    lastName?: string
    phoneNumber?: string
    isAutoPay?: boolean
    isAutoDebit?: boolean
  },
  session: mongoose.ClientSession,
) => {
  logger?.info(
    { lms_loan_id: application.lms_id, appId: application._id },
    'initializeRepayment: Start performing payment in LMS',
  )

  const payment = await performPayment(application.lms_id, amountDue, isAutoPay)

  logger?.info(
    {
      lms_payment_id: payment.id,
      appId: application._id,
      lms_loan_id: application.lms_id,
    },
    'initializeRepayment: End performing payment in LMS',
  )

  try {
    logger?.info(
      { payment_id: payment.id, appId: application._id },
      'initializeRepayment: Start performing payment in Mongo',
    )

    const op = await makeLoanPayment(
      {
        loanApplication: application,
        amount: amountDue,
        paymentDate,
        bankAccount,
        lms_paymentId: payment.id,
        cardToken,
        firstName,
        lastName,
        phoneNumber,
        isAutoDebit,
      },
      session,
      isAutoPay ? 30000 : 10000,
    )
    logger?.info(
      { operationId: op?._id, appId: application._id },
      'initializeRepayment: End performing payment in Mongo',
    )
    const builderCompany = await Company.findById(
      application.company_id,
    ).populate('bankAccounts')

    if (!builderCompany)
      throw new exceptions.LogicalError('Company does not exist')

    const sourceBankAccount = getSourceBankAccount(bankAccount, builderCompany)

    if (op || !isPaymentMethodCard(sourceBankAccount, cardToken)) {
      logger?.info(
        { lms_loan_id: application.lms_id, appId: application._id },
        'initializeRepayment: Start syncing loan application fields process',
      )
      await syncLoanApplicationFields(application, session)
      logger?.info(
        { lms_loan_id: application.lms_id, appId: application._id },
        'initializeRepayment: End syncing loan application fields process',
      )
    } else {
      await cancelPayment(
        payment.id,
        'Rollback, operation not created',
        'Application',
      )
      logger?.error(
        'initializeRepayment: Loan is not fully paid. Operation not created',
      )
    }
  } catch (e) {
    await cancelPayment(
      payment.id,
      'Rollback, operation not created',
      'Application',
    )
    logger?.error(
      'initializeRepayment: Error occured during payment processing. Operation not created',
    )
    throw e
  }
}

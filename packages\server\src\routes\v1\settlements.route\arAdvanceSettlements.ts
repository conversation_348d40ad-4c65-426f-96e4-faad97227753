import { Invoice } from '@linqpal/common-backend'
import { InvoicePaymentType } from '@linqpal/models'
import { PipelineStage } from 'mongoose'
import { ControllerItem } from 'src/routes/controllerItem'
import {
  invoiceStatus,
  OPERATION_STATUS,
  OPERATION_TYPES,
  SETTLEMENT_STATUS,
  TRANSACTION_STATUS,
} from '@linqpal/models/src/dictionaries'
import { createPagination } from '@linqpal/common-backend'
import moment from 'moment-timezone'

const format = '%m/%d/%Y'
const timezone = 'America/Chicago'

export default {
  get: async (req, res) => {
    const dateFrom = req.query.dateFrom
      ? moment(req.query.dateFrom.toString(), 'MM/DD/YYYY').toDate()
      : moment().startOf('day').subtract(2, 'years').toDate()

    const dateTo = req.query.dateTo
      ? moment(req.query.dateTo.toString(), 'MM/DD/YYYY').endOf('day').toDate()
      : moment().startOf('day').add(10, 'years').toDate()

    const pipeline: PipelineStage[] = [
      {
        $match: {
          company_id: req.company!.id,
          'paymentDetails.paymentType': InvoicePaymentType.FACTORING,
          'paymentDetails.arAdvanceStatus': { $in: ['completed', 'approved'] },
        },
      },

      {
        $lookup: {
          from: 'operations',
          as: 'operations',
          let: { invoiceId: { $toString: '$_id' } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$owner_id', '$$invoiceId'] },
                    {
                      $in: [
                        '$status',
                        [OPERATION_STATUS.SUCCESS, OPERATION_STATUS.PROCESSING],
                      ],
                    },
                    {
                      $in: [
                        '$type',
                        [
                          OPERATION_TYPES.FACTORING.DISBURSEMENT,
                          OPERATION_TYPES.FACTORING.FINAL_PAYMENT,
                        ],
                      ],
                    },
                  ],
                },
              },
            },
            {
              $lookup: {
                from: 'transactions',
                as: 'transactions',
                let: { opId: { $toString: '$_id' } },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: [{ $toString: '$operation_id' }, '$$opId'],
                      },
                    },
                  },
                  {
                    $sort: { createdAt: -1 },
                  },
                  {
                    $limit: 1,
                  },
                  {
                    $project: {
                      transactionNumber: '$metadata.transactionNumber',
                      amount: 1,
                      date: 1,
                      status: 1,
                      createdAt: 1,
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: '$transactions',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $project: {
                type: 1,
                amount: 1,
                status: 1,
                transaction: '$transactions',
              },
            },
          ],
        },
      },

      {
        $match: {
          operations: { $ne: [] },
        },
      },

      {
        $addFields: {
          advanceOperation: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$operations',
                  cond: {
                    $eq: [
                      '$$this.type',
                      OPERATION_TYPES.FACTORING.DISBURSEMENT,
                    ],
                  },
                },
              },
              0,
            ],
          },
          finalOperation: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$operations',
                  cond: {
                    $eq: [
                      '$$this.type',
                      OPERATION_TYPES.FACTORING.FINAL_PAYMENT,
                    ],
                  },
                },
              },
              0,
            ],
          },
        },
      },

      {
        $match: {
          advanceOperation: { $ne: null },
        },
      },

      {
        $lookup: {
          from: 'loanpaymentplans',
          as: 'paymentPlan',
          let: {
            paymentPlanId: {
              $convert: {
                input: '$paymentDetails.loanPlanId',
                to: 'objectId',
                onError: null,
              },
            },
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$_id', '$$paymentPlanId'],
                },
              },
            },
            { $project: { days: 1, name: 1 } },
          ],
        },
      },
      {
        $lookup: {
          from: 'loanpricingpackages',
          as: 'pricingPackage',
          let: {
            pricingPackageId: {
              $convert: {
                input: '$paymentDetails.pricingPackageId',
                to: 'objectId',
                onError: null,
              },
            },
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$_id', '$$pricingPackageId'],
                },
              },
            },
            {
              $project: {
                metadata: {
                  merchant: 1,
                  skipFinalPayment: '$metadata.finalPayment',
                },
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$paymentPlan',
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $unwind: {
          path: '$pricingPackage',
          preserveNullAndEmptyArrays: false,
        },
      },

      {
        $lookup: {
          from: 'customeraccounts',
          as: 'customer',
          let: { customerId: { $toObjectId: '$customer_account_id' } },
          pipeline: [
            {
              $match: { $expr: { $eq: ['$_id', '$$customerId'] } },
            },
            {
              $lookup: {
                from: 'companies',
                as: 'company',
                let: { company_id: '$company_id' },
                pipeline: [
                  {
                    $match: {
                      $expr: { $eq: ['$_id', { $toObjectId: '$$company_id' }] },
                    },
                  },
                  {
                    $project: {
                      name: 1,
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: '$company',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $project: {
                name: { $ifNull: ['$name', '$company.name'] },
                first_name: 1,
                last_name: 1,
                display_name: 1,
                phone: 1,
              },
            },
          ],
        },
      },

      {
        $unwind: {
          path: '$customer',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          id: { $toString: '$_id' },
          customer_account_id: 1,
          customer_user_id: 1,
          invoice_number: 1,
          invoice_date: {
            $dateToString: { date: '$invoice_date', format, timezone },
          },
          invoice_due_date: {
            $dateToString: { date: '$invoice_due_date', format, timezone },
          },
          total_amount: 1,
          seen: 1,
          tax_amount: 1,
          material_subtotal: 1,
          expiration_date: 1,
          address: 1,
          addressType: 1,
          invoice_document: 1,
          document_name: 1,
          dismiss_reasons: 1,
          material_description: 1,
          note: 1,
          createdAt: 1,
          customer: 1,
          advancePayment: '$advanceOperation.transaction',
          finalPayment: '$finalOperation.transaction',
          merchantFeePercentage: '$pricingPackage.metadata.merchant',
          skipFinalPayment: {
            $cond: {
              if: { $eq: ['$pricingPackage.metadata.skipFinalPayment', 0] },
              then: true,
              else: false,
            },
          },
          termDays: {
            $cond: {
              if: { $ne: ['$paymentPlan.days', null] },
              then: '$paymentPlan.days',
              else: 0,
            },
          },
          term: '$paymentPlan.name',
          transactionDate: '$advanceOperation.transaction.createdAt',
          advanceReleaseDate: {
            $ifNull: [
              '$advanceOperation.transaction.date',
              '$advanceOperation.transaction.createdAt',
            ],
          },
          advanceSettledAmount: '$advanceOperation.transaction.amount',
          advanceStatus: {
            $cond: {
              if: {
                $eq: [
                  '$advanceOperation.transaction.status',
                  TRANSACTION_STATUS.SUCCESS,
                ],
              },
              then: SETTLEMENT_STATUS.SETTLED,
              else: SETTLEMENT_STATUS.IN_PROGRESS,
            },
          },
          finalStatus: {
            $cond: {
              if: {
                $eq: [
                  '$finalOperation.transaction.status',
                  TRANSACTION_STATUS.SUCCESS,
                ],
              },
              then: SETTLEMENT_STATUS.SETTLED,
              else: {
                $cond: {
                  if: { $gt: ['$finalOperation.transaction', null] },
                  then: SETTLEMENT_STATUS.IN_PROGRESS,
                  else: SETTLEMENT_STATUS.SCHEDULED,
                },
              },
            },
          },
        },
      },
      {
        $addFields: {
          merchantFee: {
            $round: [
              {
                $multiply: [
                  { $divide: ['$total_amount', 100] },
                  '$merchantFeePercentage',
                ],
              },
              2,
            ],
          },
        },
      },
      {
        $addFields: {
          settledAmount: {
            $subtract: ['$total_amount', '$merchantFee'],
          },
        },
      },
      {
        $addFields: {
          finalSettledAmount: {
            $cond: {
              if: { $eq: ['$skipFinalPayment', true] },
              then: 0,
              else: {
                $ifNull: [
                  '$finalPayment.amount',
                  {
                    $round: [
                      {
                        $subtract: ['$settledAmount', '$advanceSettledAmount'],
                      },
                      2,
                    ],
                  },
                ],
              },
            },
          },
          finalReleaseDate: {
            $cond: {
              if: { $eq: ['$finalStatus', SETTLEMENT_STATUS.SCHEDULED] },
              then: null,
              else: {
                $cond: {
                  if: { $eq: ['$skipFinalPayment', false] },
                  then: {
                    $ifNull: [
                      '$finalPayment.date',
                      {
                        $dateAdd: {
                          startDate: '$transactionDate',
                          unit: 'day',
                          amount: '$termDays',
                        },
                      },
                    ],
                  },
                  else: null,
                },
              },
            },
          },
        },
      },

      {
        $project: {
          _id: '$advancePayment.transactionNumber',
          transactionNumber: '$advancePayment.transactionNumber',
          customer: '$customer',
          invoices: ['$$ROOT'],
          invoiceAmount: { $round: ['$total_amount', 2] },
          merchantFeePercentage: '$merchantFeePercentage',
          merchantFee: '$merchantFee',
          term: '$term',
          transactionDate: '$transactionDate',
          settledAmount: { $round: ['$settledAmount', 2] },
          advanceSettledAmount: '$advanceSettledAmount',
          advanceReleaseDate: '$advanceReleaseDate',
          advanceStatus: '$advanceStatus',
          skipFinalPayment: '$skipFinalPayment',
          finalSettledAmount: '$finalSettledAmount',
          finalReleaseDate: '$finalReleaseDate',
          finalStatus: '$finalStatus',
          invoice_number: '$invoice_number',
          status: projectInvoiceStatus(),
        },
      },

      {
        $match: {
          $or: [
            {
              advanceReleaseDate: {
                $gte: dateFrom,
                $lte: dateTo,
              },
            },
            {
              finalReleaseDate: {
                $gte: dateFrom,
                $lte: dateTo,
              },
            },
          ],
        },
      },
    ]

    applyFilterParams(req.query, pipeline)

    pipeline.push({ $sort: { transactionDate: -1 } })

    const { paginationPipeline, pageSize } = createPagination(req.query)

    const result = await Invoice.aggregate([
      ...pipeline,
      {
        $facet: {
          overview: [
            {
              $group: {
                _id: null,
                paymentsCount: {
                  $sum: {
                    $cond: {
                      if: {
                        $and: [
                          shouldIncludeInOverview(
                            '$advanceStatus',
                            '$advanceReleaseDate',
                            dateFrom,
                            dateTo,
                          ),
                          shouldIncludeInOverview(
                            '$finalStatus',
                            '$finalReleaseDate',
                            dateFrom,
                            dateTo,
                          ),
                        ],
                      },
                      then: 2,
                      else: {
                        $cond: {
                          if: {
                            $or: [
                              shouldIncludeInOverview(
                                '$advanceStatus',
                                '$advanceReleaseDate',
                                dateFrom,
                                dateTo,
                              ),
                              shouldIncludeInOverview(
                                '$finalStatus',
                                '$finalReleaseDate',
                                dateFrom,
                                dateTo,
                              ),
                            ],
                          },
                          then: 1,
                          else: 0,
                        },
                      },
                    },
                  },
                },
                advanceSettledAmount: {
                  $sum: {
                    $cond: {
                      if: shouldIncludeInOverview(
                        '$advanceStatus',
                        '$advanceReleaseDate',
                        dateFrom,
                        dateTo,
                      ),
                      then: '$advanceSettledAmount',
                      else: 0,
                    },
                  },
                },
                finalSettledAmount: {
                  $sum: {
                    $cond: {
                      if: shouldIncludeInOverview(
                        '$finalStatus',
                        '$finalReleaseDate',
                        dateFrom,
                        dateTo,
                      ),
                      then: '$finalSettledAmount',
                      else: 0,
                    },
                  },
                },
                outstandingFinalAmount: {
                  $sum: {
                    $cond: {
                      if: {
                        $eq: ['$finalStatus', SETTLEMENT_STATUS.SCHEDULED],
                      },
                      then: '$finalSettledAmount',
                      else: 0,
                    },
                  },
                },
              },
            },
          ],
          total: [{ $count: 'count' }],
          items: paginationPipeline,
        },
      },
      { $unwind: { path: '$total', preserveNullAndEmptyArrays: true } },
      { $unwind: { path: '$overview', preserveNullAndEmptyArrays: true } },
    ])

    const { total, items, overview } = result[0]
    const count = total?.count ?? 0

    res.send({
      overview,
      items,
      count: pageSize > 0 ? count : items.length,
    })
  },
} as ControllerItem

function projectInvoiceStatus() {
  return {
    $cond: {
      if: {
        $and: [
          { $eq: ['$advanceStatus', SETTLEMENT_STATUS.SETTLED] },
          {
            $or: [
              { $eq: ['$skipFinalPayment', true] },
              { $eq: ['$finalStatus', SETTLEMENT_STATUS.SETTLED] },
            ],
          },
        ],
      },
      then: invoiceStatus.paid,
      else: invoiceStatus.paymentProcessing,
    },
  }
}

function applyFilterParams(query: any, pipeline: PipelineStage[]) {
  const { search, status } = query

  if (search?.toString()?.trim()) {
    pipeline.push(
      ...[
        {
          $set: {
            searchAmount: {
              $convert: {
                input: search,
                to: 'double',
                onError: -1,
              },
            },
          },
        },
        {
          $match: {
            $or: [
              { 'customer.name': { $regex: search, $options: 'i' } },
              { transactionNumber: { $regex: search, $options: 'i' } },
              {
                $expr: {
                  $or: [
                    { $eq: ['$invoiceAmount', '$searchAmount'] },
                    { $eq: ['$settledAmount', '$searchAmount'] },
                    { $eq: ['$advanceSettledAmount', '$searchAmount'] },
                    { $eq: ['$finalSettledAmount', '$searchAmount'] },
                  ],
                },
              },
              {
                invoice_number: { $regex: search, $options: 'i' },
              },
            ],
          },
        },
        { $unset: 'searchAmount' },
      ],
    )
  }

  if (status) {
    pipeline.push({
      $match: {
        $expr: {
          $or: [
            { $eq: ['$advanceStatus', status] },
            { $eq: ['$finalStatus', status] },
          ],
        },
      },
    })
  }
}

function shouldIncludeInOverview(
  statusField: string,
  dateField: string,
  dateFrom: Date,
  dateTo: Date,
) {
  return {
    $and: [
      { $eq: [statusField, SETTLEMENT_STATUS.SETTLED] },
      { $gte: [dateField, dateFrom] },
      { $lte: [dateField, dateTo] },
    ],
  }
}

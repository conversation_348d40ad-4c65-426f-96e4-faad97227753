import {
  Company,
  getSelectedCardPricingPackageDetails,
  Invoice,
} from '@linqpal/common-backend'
import { exceptions, InvoicePaymentType } from '@linqpal/models'
import {
  Package,
  PricingPackageForIntegrationRequest,
  PricingPackageRequest,
  PricingPackageResponse,
} from '@linqpal/models/src/routes2/invoice/types'
import { TCompoundRoute } from '@linqpal/models/src/routes2/types'
import { authRequired } from '../../../services/auth.service'
import moment from 'moment-timezone'
import { round } from 'lodash'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'
import mongoose from 'mongoose'
import {
  OPERATION_STATUS,
  OPERATION_TYPES,
} from '@linqpal/models/src/dictionaries'

export const getPricingPackage: TCompoundRoute<
  PricingPackageRequest,
  PricingPackageResponse
> = {
  middlewares: {
    pre: [authRequired()],
  },
  post: async (params) => {
    if (!params.invoiceIds) return {}

    const [initInvoice] = await Invoice.aggregate([
      {
        $match: {
          _id: new mongoose.Types.ObjectId(params.invoiceIds[0].toString()),
        },
      },
      {
        $lookup: {
          from: 'operations',
          as: 'operation',
          let: { invoice_id: { $toString: '$_id' } },
          pipeline: [
            { $match: { $expr: { $eq: ['$owner_id', '$$invoice_id'] } } },
            {
              $match: {
                status: {
                  $in: [
                    OPERATION_STATUS.PLACED,
                    OPERATION_STATUS.PROCESSING,
                    OPERATION_STATUS.SUCCESS,
                    OPERATION_STATUS.FAIL,
                  ],
                },
                type: OPERATION_TYPES.INVOICE.PAYMENT,
              },
            },
            { $sort: { createdAt: -1 } },
            { $limit: 1 },
          ],
        },
      },
      {
        $unwind: {
          path: '$operation',
          preserveNullAndEmptyArrays: true,
        },
      },
    ])

    if (!initInvoice?.company_id) return {}

    const supplierCompany = await Company.findById(initInvoice.company_id)

    if (!supplierCompany) return {}

    let totalAmount = 0
    let totalDiscount = 0

    for (const invoiceId of params.invoiceIds) {
      const invoice = await Invoice.findById(invoiceId)

      if (!invoice) continue
      totalAmount += invoice.total_amount
      totalDiscount += calculateDiscount(invoice, supplierCompany)
    }

    if (initInvoice) {
      let lateFee = 0

      const isFactoringInvoice =
        initInvoice.paymentDetails?.paymentType ===
          InvoicePaymentType.FACTORING &&
        initInvoice.paymentDetails?.arAdvanceStatus === ArAdvanceStatus.Approved

      if (isFactoringInvoice) {
        const {
          totalAmount: totalRemainingAmount,
          lateFee: lateInterestCharge,
        } = await calculateFactoringAmount(initInvoice, supplierCompany)

        totalAmount = totalRemainingAmount
        lateFee = lateInterestCharge
      }

      const pricingPackage = await getPricingPackageFee(
        initInvoice.company_id,
        params.accountId,
        totalAmount,
        lateFee,
      )

      pricingPackage.discount = totalDiscount

      return {
        pricingPackage,
        lateFee,
        customerFee: initInvoice?.paymentDetails?.customerFee ?? 0,
      }
    }
    return {}
  },
}

export const getPricingPackageForIntegration: TCompoundRoute<
  PricingPackageForIntegrationRequest,
  PricingPackageResponse
> = {
  middlewares: {
    pre: [authRequired()],
  },
  post: async (params) => {
    if (!(params.companyId && params.accountId && params.totalAmount)) return {}

    const { companyId, accountId, totalAmount } = params

    const pricingPackage = await getPricingPackageFee(
      companyId,
      accountId,
      totalAmount,
    )
    pricingPackage.discount = 0
    return { pricingPackage }
  },
}

export default async function getPricingPackageFee(
  companyId: string,
  accountId: string,
  total_amount: string | number,
  lateFee = 0,
): Promise<Package> {
  const pricingPackage = await getSelectedCardPricingPackageDetails(
    companyId,
    accountId,
  )
  if (!pricingPackage)
    throw new exceptions.LogicalError('Package for the payment type not found')

  const amount = Number(total_amount)

  const fee = pricingPackage
    ? ((amount + lateFee) * pricingPackage.customer.percentage) / 100 +
      pricingPackage.customer.amount
    : 0
  const totalAmount = amount + fee
  return {
    pricingPackage,
    totalAmount,
    fee,
    invoiceAmount: amount,
  }
}

const calculateDiscount = (invoice: any, supplierCompany: any): number => {
  if (
    !invoice.company_id ||
    (invoice.paymentDetails?.paymentType === InvoicePaymentType.FACTORING &&
      invoice.paymentDetails?.arAdvanceStatus === ArAdvanceStatus.Approved)
  ) {
    return 0
  }

  if (!supplierCompany?.settings.achDiscount) {
    return 0
  }

  if (invoice.total_amount <= 0) return 0

  const discountDate = moment(invoice.invoice_date).add(
    supplierCompany.settings.achDiscount.validityInDays,
    'days',
  )
  const today = moment().endOf('day')
  const dueDate = moment(invoice.invoice_due_date)

  if (discountDate.diff(today, 'day') >= 0 && dueDate.diff(today, 'day') >= 0) {
    return round(
      (invoice.total_amount * supplierCompany.settings.achDiscount.percentage) /
        100,
      2,
    )
  }

  return 0
}
const calculateFactoringAmount = async (
  invoice: any,
  supplierCompany: any,
): Promise<{ totalAmount: number; lateFee: number }> => {
  let lateFee = 0

  if (!supplierCompany?.settings?.arAdvance?.isLateInterestChargedToMerchant) {
    lateFee = invoice?.paymentDetails?.fees ?? 0
  }

  const operationStatus = invoice.operation?.status ?? ''
  const operationAmount = invoice.operation?.amount ?? 0

  const totalPaidAmount =
    invoice.operation?.paidAmount ??
    (operationStatus === OPERATION_STATUS.SUCCESS ? operationAmount : 0)

  const totalProcessingAmount =
    invoice.operation?.processingAmount ??
    (operationStatus === OPERATION_STATUS.PROCESSING && totalPaidAmount === 0
      ? operationAmount
      : 0)

  const totalAmount = Number(
    (
      Math.round(
        (operationAmount - totalPaidAmount - totalProcessingAmount) * 100,
      ) / 100
    ).toFixed(2),
  )

  return { totalAmount, lateFee }
}

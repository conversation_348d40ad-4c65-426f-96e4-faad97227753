import {
  AccountingSettings,
  AwsService,
  BankAccount,
  Company,
  crypt,
  LoanApplication,
  Settings,
  UserRole,
} from '@linqpal/common-backend'
import md5 from 'crypto-js/md5'
import mongoose, { PipelineStage, Types } from 'mongoose'
import { getAccountingSettings } from '../../../../controllers/accounting.controller'
import xlsx from 'xlsx'
import moment from 'moment/moment'
import { Request, Response } from 'express'
import { IBankAccount } from '@linqpal/common-backend/src/models/types'
import { IEncrypted } from '@linqpal/models'
import { maskBankAccountNumber } from '@linqpal/common-backend/src/helpers/maskBankAccountNumber'
import {
  DefaultSettings,
  LOAN_APPLICATION_STATUS,
  SettingKeys,
} from '@linqpal/models/src/dictionaries'
import { ICustomerCompanySettings } from '@linqpal/models/src/types/routes'
import { formatPersonName } from '@linqpal/models/src/helpers/personNameFormatter'

interface Query {
  search?: string
  approved?: 'false' | 'true'
  page?: string
}

export default async function customers(
  req: Request<unknown, unknown, unknown, Query>,
  res: Response,
) {
  const { search = '', approved = 'false', page } = req.query || {}
  const pageInt = parseInt(page || '1')

  let agg = Company.aggregate()

  if (search.trim().length > 0) {
    const searchFixed = search.trim().toLowerCase()
    agg = agg.match({
      $or: [
        { name: { $regex: searchFixed, $options: 'i' } },
        { 'address.name': { $regex: searchFixed, $options: 'i' } },
        { 'address.city': { $regex: searchFixed, $options: 'i' } },
        { 'address.state': { $regex: searchFixed, $options: 'i' } },
      ],
    })
  }

  if (approved === 'true') {
    agg = agg.match({ status: 'approved' })
  }

  const _total = await Company.aggregate(agg.pipeline()).count('total').exec()
  const total = _total[0]?.total || 0

  agg = agg.sort({ name: -1, 'address.address': 1, createdAt: -1 })
  if (total > 0 && pageInt > 0) {
    agg = agg.skip((pageInt - 1) * 10)
  }
  if (pageInt !== -1) {
    agg = agg.limit(10)
  }

  const items = await agg
    .addFields({ id: { $toString: '$_id' } })
    .lookup({
      from: 'bankaccounts',
      as: 'bankAccounts',
      let: { bankAccounts: '$bankAccounts' },
      pipeline: [
        { $match: { $expr: { $in: ['$_id', '$$bankAccounts'] } } },
        {
          $addFields: {
            isPlaidConnection: { $ifNull: ['$plaid', false] },
          },
        },
      ],
    })
    .lookup({
      from: 'userroles',
      as: 'owners',
      let: { company_id: { $toString: '$_id' } },
      pipeline: UserRole.aggregate()
        .match({
          $expr: {
            $and: [
              { $eq: ['$company_id', '$$company_id'] },
              { $eq: ['$role', 'Owner'] },
              { $eq: ['$status', 'Active'] },
            ],
          },
        })
        .lookup({
          from: 'users',
          as: 'user',
          localField: 'sub',
          foreignField: 'sub',
        })
        .unwind('user')
        .project({
          'user.firstName': 1,
          'user.lastName': 1,
          'user.phone': 1,
          'user.email': 1,
          'user.login': 1,
        })
        .replaceRoot('$user')
        .pipeline() as Exclude<
        PipelineStage,
        PipelineStage.Merge | PipelineStage.Out | PipelineStage.Search
      >[],
    })
    .exec()
  if (pageInt !== -1) {
    await Promise.all(
      items.map(async (item) => {
        if (item.ein?.cipher) {
          try {
            item.ein = await crypt.decrypt(item.ein.cipher)
          } catch (e) {
            item.ein = ''
          }
        }
        item.accountingSettings = await getAccountingSettings(item._id)
        if (item.bankAccounts) {
          await Promise.all(
            item.bankAccounts.map(async (bankAccount: IBankAccount) => {
              const cipher =
                (bankAccount.accountNumber as IEncrypted)?.cipher || ''
              const display =
                (bankAccount.accountNumber as IEncrypted)?.display || ''
              if (cipher) {
                try {
                  bankAccount.accountNumber = await crypt.decrypt(cipher)
                } catch {
                  bankAccount.accountNumber = display
                }
              } else if (display) {
                bankAccount.accountNumber = display
              }
            }),
          )
        }
      }),
    )
  }
  if (pageInt === -1) {
    const file = xlsx.utils.sheet_to_csv(
      xlsx.utils.json_to_sheet(
        items.map((customer) => {
          const { id, _id, name, address, status } = customer
          return {
            id: id || _id,
            name,
            address: address
              ? [address.address, address.city, address.state].join(', ')
              : '',
            approved: status === 'approved',
            firstName: customer.owners?.[0]?.firstName,
            lastName: customer.owners?.[0]?.lastName,
          }
        }),
      ),
    )

    const filename = `customers-${moment().unix()}.csv`
    const bucket = `${process.env.LP_MODE}.uw1.linqpal-temp-assets`

    await AwsService.putS3File(bucket, filename, file, 'us-west-1')

    return res.send({
      downloadLink: await AwsService.getPreSignedUrl({
        key: filename,
        method: 'get',
        bucket,
      }),
    })
  }
  const modifiedList = await Promise.all(
    items.map(async (item) => {
      const r = await LoanApplication.findOne({
        company_id: item._id,
        status: mongoose.trusted({ $ne: LOAN_APPLICATION_STATUS.NEW }),
      }).exec()
      return { ...item, isCreditApplied: !!r }
    }),
  )

  return res.send({ items: modifiedList, total })
}

export const saveCustomerBankAccounts = {
  async post(req: Request, res: Response) {
    const {
      company_id = '',
      bankAccounts = [],
    }: {
      company_id: string
      bankAccounts: (IBankAccount & { accountNumber: string })[]
    } = req.body

    await Promise.all(
      bankAccounts.map(async (bankAccount) => {
        const {
          _id,
          name = '',
          accountholderName = '',
          accountNumber = '',
          routingNumber = '',
          isPrimary = false,
          isPrimaryForCredit = false,
          status = 'notverified',
        } = bankAccount
        if (accountNumber) {
          const _accountNumber = {
            cipher: await crypt.encrypt(accountNumber),
            hash: md5(accountNumber).toString(),
            display: maskBankAccountNumber(accountNumber),
          }
          if (_id) {
            await BankAccount.updateOne(
              { _id, isManualEntry: true },
              { accountNumber: _accountNumber, routingNumber },
            )
            await BankAccount.updateOne(
              { _id },
              { isPrimary, isPrimaryForCredit },
            )
            if (status === 'manualverified') {
              await BankAccount.updateOne({ _id }, { status })
            }
          } else {
            const account = await BankAccount.create({
              name,
              accountholderName,
              routingNumber,
              accountNumber: _accountNumber,
              paymentMethodType: 'bank',
              accountType: 'savings',
              isPrimary,
              isPrimaryForCredit,
              status: 'notverified',
              isManualEntry: true,
              isDeactivated: false,
              createdBy: req.user
                ? formatPersonName(req.user.firstName, req.user.lastName)
                : null,
            })
            await Company.updateOne(
              { _id: company_id },
              { $push: { bankAccounts: new Types.ObjectId(account._id) } },
            )
          }
        }
      }),
    )
    return res.send({})
  },
}

export const saveCustomerCompanySettings = {
  post: async (req: Request, res: Response) => {
    const {
      company_id,
      settings: newSettings,
    }: { company_id: string; settings: Partial<ICustomerCompanySettings> } =
      req.body

    if (!company_id) throw new Error(`company_id is required`)
    if (!newSettings) throw new Error(`settings are required`)

    const company = await Company.findById(company_id)
    if (!company) throw new Error(`unable to find company ${company_id}`)

    if (
      newSettings.downPaymentDetails?.isRequired &&
      !newSettings.downPaymentDetails.expireDays
    ) {
      const expireDays = await Settings.findOne({
        key: SettingKeys.DownPaymentExpireDays,
      })

      newSettings.downPaymentDetails.expireDays =
        expireDays?.value ?? DefaultSettings.DownPaymentExpireDays
    }
    const updatedSettings = { ...company.settings, ...newSettings }

    company.set('settings', updatedSettings)
    await company.save({ session: req.session })

    res.send(company?.settings || {})
  },
}

export const saveCustomerAccountingSettings = {
  async post(req: Request, res: Response) {
    const { company_id = '', settings } = req.body
    const { email, credit_expire_days, status } = settings || {}
    const accountSettings = await AccountingSettings.findOne({ company_id })
    if (!accountSettings)
      await AccountingSettings.create({
        company_id,
        email: email.toLowerCase(),
        credit_expire_days,
        status,
        pdfUrls: [],
      })
    else
      await AccountingSettings.findOneAndUpdate(
        { company_id },
        {
          email: email.toLowerCase(),
          credit_expire_days,
          status,
        },
      )

    return res.send({})
  },
}
